"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses\n    // async getLowStockItems(branchId?: string) {\n    //   const { data, error } = await supabase\n    //     .rpc('get_low_stock_items', { branch_id: branchId })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getLowStockItems (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \");\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        const lowStockItems = data === null || data === void 0 ? void 0 : data.filter((item)=>item.current_stock < item.minimum_stock).sort((a, b)=>a.current_stock - b.current_stock);\n        return lowStockItems;\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(unit, name),\\n        warehouse:warehouses(name, code)\\n      \").eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(\"Failed to fetch inventory: \".concat(fetchError.message));\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            var _inventory_ingredient;\n            throw new Error(\"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit));\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            var _inventory_ingredient1, _inventory_ingredient2, _inventory_warehouse;\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(\"Failed to update inventory: \".concat(updateError.message));\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: ((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.unit) || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || \"Stock \".concat(movementType, \" - \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.name, \" at \").concat((_inventory_warehouse = inventory.warehouse) === null || _inventory_warehouse === void 0 ? void 0 : _inventory_warehouse.name),\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(\"Failed to record stock movement: \".concat(movementError.message));\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Stock update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: (ingredient === null || ingredient === void 0 ? void 0 : ingredient.unit) || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*),\\n        performer:profiles(full_name)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n        *,\\n        from_warehouse:warehouses!from_warehouse_id(*),\\n        to_warehouse:warehouses!to_warehouse_id(*),\\n        requester:profiles!requested_by(*),\\n        transfer_items(*, ingredient:ingredients(*))\\n      \").eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(\"from_warehouse_id.eq.\".concat(warehouseId, \",to_warehouse_id.eq.\").concat(warehouseId));\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer\n    async approveTransfer (transferId, approvedBy, approvedItems) {\n        // Update transfer status\n        const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n            status: 'approved',\n            approved_by: approvedBy,\n            approved_at: new Date().toISOString()\n        }).eq('id', transferId);\n        if (transferError) throw transferError;\n        // Update approved quantities for items\n        for (const item of approvedItems){\n            const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                approved_quantity: item.approved_quantity\n            }).eq('id', item.id);\n            if (itemError) throw itemError;\n        }\n        return {\n            success: true\n        };\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    var _inventory_ingredient, _inventory_ingredient1;\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n              *,\\n              ingredient:ingredients(unit, name),\\n              warehouse:warehouses(name, code)\\n            \").eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to fetch inventory: \".concat(fetchError.message)\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        var _inventory_ingredient2;\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.unit)\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to update inventory: \".concat(updateError.message)\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: ((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit) || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || \"Bulk stock \".concat(update.movementType, \" - \").concat((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.name),\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Bulk update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*),\\n        warehouses(*),\\n        kitchens(*)\\n      \").eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        branch:branches(*),\\n        manager:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        manager:profiles(*)\\n      \").eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy) {\n        const batchNumber = \"BATCH-\".concat(Date.now());\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString()\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        starter:profiles!started_by(*),\\n        completer:profiles!completed_by(*)\\n      \");\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name)\\n      \").eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(\"\\n        *,\\n        branch:branches(*),\\n        server:profiles!served_by(*),\\n        sales_transaction_items(\\n          *,\\n          menu_item:menu_items(*)\\n        )\\n      \");\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n        *,\\n        recipe:recipes(*)\\n      \").eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(\"\\n        *,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        )\\n      \").eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        current_stock,\\n        minimum_stock,\\n        ingredient:ingredients(cost_per_unit)\\n      \").in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>{\n            var _item_ingredient;\n            return sum + item.current_stock * (((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0);\n        }, 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        total_price,\\n        menu_item:menu_items(name, category),\\n        sales_transactions!inner(created_at, branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});