'use client'

import { useState, useEffect } from 'react'
import { inventoryService } from '@/lib/database'

interface InventoryMetrics {
  totalItems: number
  totalValue: number
  lowStockCount: number
  criticalStockCount: number
  reorderNeededCount: number
  averageStockLevel: number
  turnoverRate: number
}

interface ReorderSuggestion {
  inventoryId: string
  ingredientId: string
  ingredientName: string
  ingredientCode: string
  warehouseId: string
  warehouseName: string
  currentStock: number
  minimumStock: number
  reorderPoint: number
  maximumStock: number
  suggestedQuantity: number
  estimatedCost: number
  priority: 'critical' | 'low' | 'reorder'
  daysUntilStockout: number | null
  unit: string
}

interface ReorderSummary {
  totalItems: number
  totalEstimatedCost: number
  criticalCount: number
  lowCount: number
  reorderCount: number
}

interface InventoryAnalyticsProps {
  warehouseId?: string
  branchId?: string
}

export default function InventoryAnalytics({ warehouseId, branchId }: InventoryAnalyticsProps) {
  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null)
  const [reorderSuggestions, setReorderSuggestions] = useState<ReorderSuggestion[]>([])
  const [reorderSummary, setReorderSummary] = useState<ReorderSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadAnalytics()
  }, [warehouseId, branchId])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load metrics and reorder suggestions in parallel
      const [metricsData, reorderData] = await Promise.all([
        inventoryService.getInventoryMetrics(warehouseId, branchId),
        inventoryService.generateReorderSuggestions(warehouseId, branchId)
      ])

      setMetrics(metricsData)
      setReorderSuggestions(reorderData.suggestions)
      setReorderSummary(reorderData.summary)
    } catch (err) {
      console.error('Failed to load analytics:', err)
      setError('Failed to load inventory analytics')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800'
      case 'low':
        return 'bg-yellow-100 text-yellow-800'
      case 'reorder':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return '🚨'
      case 'low':
        return '⚠️'
      case 'reorder':
        return '📦'
      default:
        return '📊'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">📦</div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.totalItems.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">💰</div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.totalValue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">🚨</div>
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Stock</p>
                <p className="text-2xl font-bold text-red-600">{metrics.criticalStockCount}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">📊</div>
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Stock Level</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.averageStockLevel}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reorder Summary */}
      {reorderSummary && reorderSummary.totalItems > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Reorder Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{reorderSummary.criticalCount}</div>
              <div className="text-sm text-red-800">Critical Items</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{reorderSummary.lowCount}</div>
              <div className="text-sm text-yellow-800">Low Stock Items</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{reorderSummary.reorderCount}</div>
              <div className="text-sm text-blue-800">Reorder Needed</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{formatCurrency(reorderSummary.totalEstimatedCost)}</div>
              <div className="text-sm text-green-800">Estimated Cost</div>
            </div>
          </div>
        </div>
      )}

      {/* Reorder Suggestions */}
      {reorderSuggestions.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Reorder Suggestions</h3>
            <p className="text-sm text-gray-600">Items that need to be restocked</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ingredient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Suggested Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Estimated Cost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Until Stockout
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Warehouse
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reorderSuggestions.slice(0, 10).map((suggestion) => (
                  <tr key={`${suggestion.inventoryId}-${suggestion.ingredientId}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                        {getPriorityIcon(suggestion.priority)} {suggestion.priority.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{suggestion.ingredientName}</div>
                        <div className="text-sm text-gray-500">Code: {suggestion.ingredientCode}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {suggestion.currentStock.toLocaleString()} {suggestion.unit}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {suggestion.suggestedQuantity.toLocaleString()} {suggestion.unit}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(suggestion.estimatedCost)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {suggestion.daysUntilStockout !== null ? `${suggestion.daysUntilStockout} days` : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {suggestion.warehouseName}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {reorderSuggestions.length > 10 && (
            <div className="px-6 py-3 bg-gray-50 text-sm text-gray-500 text-center">
              Showing 10 of {reorderSuggestions.length} suggestions
            </div>
          )}
        </div>
      )}

      {/* No Suggestions */}
      {reorderSuggestions.length === 0 && !loading && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <div className="text-4xl mb-2">✅</div>
          <h3 className="text-lg font-medium text-green-800 mb-2">All Stock Levels Look Good!</h3>
          <p className="text-green-700">No immediate reorder actions needed at this time.</p>
        </div>
      )}
    </div>
  )
}
