# Padang Restaurant Management System - Setup Guide

## 🚀 Quick Start

Your Padang Restaurant Management System is now set up and running! Here's how to get started:

### 1. Current Status
- ✅ Next.js project initialized with TypeScript and Tailwind CSS
- ✅ Supabase integration configured
- ✅ Authentication system with role-based access implemented
- ✅ Database schema designed and ready for deployment
- ✅ Basic inventory management module created
- ✅ Responsive dashboard layout implemented

### 2. Access the Application

The application is currently running at: **http://localhost:3000**

### 3. Database Setup (Required)

Before you can use the application, you need to set up the database:

#### Option A: Manual Setup (Recommended)
1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database/schema.sql`
4. Execute the SQL to create all tables and functions
5. Copy and paste the contents of `database/sample_data.sql`
6. Execute the SQL to populate with sample data

#### Option B: Automated Setup
1. Add your Supabase service role key to `.env.local`:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```
2. Install additional dependencies:
   ```bash
   npm install dotenv
   ```
3. Run the setup script:
   ```bash
   node database/setup.js
   ```

### 4. Create Your First User

1. Open the application in your browser
2. You'll see a login form since no user is authenticated
3. Currently, you'll need to create a user through Supabase Auth
4. Go to Supabase Dashboard > Authentication > Users
5. Click "Add User" and create an admin user
6. The user profile will be automatically created with 'staff' role
7. Update the user's role to 'admin' in the profiles table

### 5. System Features Available

#### ✅ Completed Features:
- **Authentication System**: Role-based access (admin, manager, staff, cashier)
- **Dashboard Layout**: Responsive design with navigation
- **Database Schema**: Complete schema for multi-location restaurant chain
- **Inventory Management**: Basic inventory viewing and stock tracking
- **Sample Data**: Realistic test data for development

#### 🚧 In Development:
- Multi-warehouse inventory transfers
- Production management
- Branch management
- POS system
- Reporting and analytics

### 6. User Roles and Permissions

- **Admin**: Full system access, can manage all branches and users
- **Manager**: Branch-level management, inventory, production, reports
- **Staff**: Inventory management, production tracking
- **Cashier**: POS system, basic dashboard access

### 7. Sample Data Included

The system comes with sample data for testing:
- 3 restaurant branches (Central, Kelapa Gading, Pondok Indah)
- 3 warehouses (1 central, 2 branch-specific)
- 3 kitchens (1 central, 2 branch-specific)
- 10 common ingredients (beef, chicken, spices, etc.)
- 5 traditional Padang recipes (Rendang, Ayam Pop, Gulai, etc.)
- Sample inventory and production data

### 8. Next Steps

1. **Set up the database** using one of the methods above
2. **Create your admin user** and log in
3. **Explore the inventory management** features
4. **Customize the sample data** to match your restaurant
5. **Continue development** of remaining features

### 9. Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```

### 10. Environment Variables

Make sure your `.env.local` file contains:
```
NEXT_PUBLIC_SUPABASE_URL=https://klgxgxjhjnojngrtcloe.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtsZ3hneGpoam5vam5ncnRjbG9lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1MzQ0NDAsImV4cCI6MjA2NzExMDQ0MH0.5F7hUxdV4XPRyfTrCYbCDXLQ6jzDiroSClLj1IcQj1s

# Optional: For automated database setup
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 11. Troubleshooting

#### Common Issues:

1. **"Table doesn't exist" errors**: Make sure you've run the database schema setup
2. **Authentication not working**: Check your Supabase configuration and environment variables
3. **Permission denied**: Ensure Row Level Security policies are properly set up
4. **Build errors**: Make sure all dependencies are installed with `npm install`

#### Getting Help:

- Check the browser console for JavaScript errors
- Check the Supabase logs in your dashboard
- Verify your environment variables are correct
- Ensure your database schema is properly set up

### 12. Architecture Overview

```
padang-restaurant-system/
├── src/
│   ├── app/                 # Next.js app router pages
│   ├── components/          # Reusable React components
│   ├── contexts/           # React contexts (Auth, etc.)
│   └── lib/                # Utility functions and services
├── database/               # Database schema and setup
├── public/                 # Static assets
└── configuration files
```

### 13. Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Authentication**: Supabase Auth with Row Level Security
- **Database**: PostgreSQL with comprehensive schema
- **Deployment**: Ready for Vercel deployment

---

## 🎉 Congratulations!

Your Padang Restaurant Management System foundation is complete! The system is designed to scale with your restaurant operations and can be customized to meet your specific needs.

For questions or support, refer to the documentation in each module or check the database README for detailed schema information.
