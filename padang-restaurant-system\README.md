# Padang Restaurant Management System

A comprehensive, modern restaurant management system specifically designed for Padang restaurant chains. Built with Next.js, TypeScript, and Supabase, this system supports multi-location operations with centralized and distributed management capabilities.

## 🌟 Features

### ✅ **Complete System Modules**

#### 🔐 **Authentication & User Management**
- Role-based access control (Admin, Manager, Staff, Cashier)
- Branch-specific user permissions
- Secure authentication with Supabase Auth
- User profile management

#### 📦 **Multi-Warehouse Inventory Management**
- Real-time stock tracking across multiple warehouses
- Inter-warehouse transfer system with approval workflows
- Minimum stock alerts and reorder point monitoring
- Comprehensive stock movement audit trails
- Batch tracking with expiry date management

#### 👨‍🍳 **Multi-Kitchen Production Management**
- Standardized recipes for traditional Padang dishes
- Production batch tracking and quality control
- Raw material consumption monitoring
- Recipe ingredient management
- Production planning and scheduling

#### 🏪 **Branch Management System**
- Individual branch dashboards with KPIs
- Branch performance analytics and comparison
- Menu synchronization across locations
- Operating hours and manager assignment
- Real-time branch status monitoring

#### 💰 **Integrated POS System**
- Visual menu interface optimized for Padang restaurants
- Multiple payment method support (Cash, Card, E-wallet, QRIS)
- Order tracking and customer management
- Special instructions and customization options
- Real-time inventory integration

#### 📊 **Reporting & Analytics**
- Comprehensive business intelligence dashboards
- Sales performance reports and trends
- Top-selling items analysis
- Branch comparison reports
- Inventory valuation and movement reports
- Exportable reports in multiple formats

## 🏗️ **System Architecture**

### **Technology Stack**
- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Database**: PostgreSQL with Row Level Security
- **Authentication**: Supabase Auth with JWT
- **Deployment**: Vercel-ready

### **Database Design**
- **18+ interconnected tables** supporting complex restaurant operations
- **Multi-tenant architecture** for branch isolation
- **Comprehensive audit trails** for all transactions
- **Real-time data synchronization** across locations
- **Scalable schema** designed for growth

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+
- npm or yarn
- Supabase account

### **Installation**

1. **Clone and install dependencies:**
   ```bash
   cd padang-restaurant-system
   npm install
   ```

2. **Environment setup:**
   The `.env.local` file is already configured with your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://klgxgxjhjnojngrtcloe.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

3. **Database setup:**
   ```bash
   # Option 1: Automated setup
   node database/setup.js

   # Option 2: Manual setup via Supabase Dashboard
   # Copy and execute database/schema.sql
   # Copy and execute database/sample_data.sql
   ```

4. **Start development server:**
   ```bash
   npm run dev
   ```

5. **Access the application:**
   Open [http://localhost:3000](http://localhost:3000)

## 📋 **User Roles & Permissions**

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access, user management, all reports |
| **Manager** | Branch management, inventory, production, reports |
| **Staff** | Inventory management, production tracking |
| **Cashier** | POS system, basic dashboard access |

## 🗄️ **Database Schema Overview**

### **Core Entities**
- **Users & Profiles**: Authentication and user management
- **Branches**: Multi-location restaurant management
- **Warehouses**: Central and branch-specific storage
- **Kitchens**: Production facilities management

### **Inventory Management**
- **Ingredients**: Raw materials and supplies
- **Inventory**: Stock levels per warehouse
- **Stock Movements**: Complete audit trail
- **Transfers**: Inter-warehouse transfers

### **Production Management**
- **Recipes**: Standardized Padang dish recipes
- **Production Batches**: Cooking session tracking
- **Quality Control**: Taste and quality scoring

### **Sales Management**
- **Menu Items**: Available dishes and pricing
- **Sales Transactions**: Customer orders
- **Daily Summaries**: Aggregated sales data

## 🎯 **Sample Data Included**

The system comes with realistic sample data:
- **3 Restaurant Branches** (Central, Kelapa Gading, Pondok Indah)
- **Traditional Padang Recipes** (Rendang, Ayam Pop, Gulai, etc.)
- **Common Ingredients** (Beef, Chicken, Spices, etc.)
- **Sample Inventory** across all warehouses
- **Production History** and quality scores

## 🔧 **Development Commands**

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
node database/setup.js    # Setup database schema and sample data
```

## 📱 **Responsive Design**

The system is fully responsive and optimized for:
- **Desktop**: Full dashboard experience
- **Tablet**: Touch-optimized POS interface
- **Mobile**: Essential functions for on-the-go management

## 🔒 **Security Features**

- **Row Level Security (RLS)** on all sensitive tables
- **Role-based access control** with branch isolation
- **JWT-based authentication** with automatic token refresh
- **Audit trails** for all critical operations
- **Data validation** at both client and server levels

## 📈 **Scalability & Performance**

- **Optimized database queries** with proper indexing
- **Real-time updates** using Supabase subscriptions
- **Efficient state management** with React Context
- **Code splitting** and lazy loading for optimal performance
- **CDN-ready** static assets

## 🚀 **Deployment**

### **Vercel Deployment (Recommended)**
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on every push

### **Manual Deployment**
```bash
npm run build
npm run start
```

## 📚 **Documentation**

- **Setup Guide**: `SETUP.md` - Complete setup instructions
- **Database Documentation**: `database/README.md` - Schema details
- **API Documentation**: Auto-generated from TypeScript types

## 🤝 **Contributing**

This system is designed to be easily extensible. Key areas for enhancement:
- Additional payment integrations
- Advanced analytics and forecasting
- Mobile app development
- Third-party integrations (accounting, suppliers)

## 📞 **Support**

For technical support or customization requests:
- Check the documentation in each module
- Review the database schema documentation
- Examine the sample data for usage patterns

## 🎉 **Success Metrics**

This system is designed to help Padang restaurants achieve:
- **Improved inventory accuracy** (reduce waste by 20-30%)
- **Faster order processing** (reduce wait times by 40%)
- **Better cost control** (track ingredient costs precisely)
- **Enhanced quality consistency** (standardized recipes and quality scoring)
- **Data-driven decisions** (comprehensive analytics and reporting)

---

## 🏆 **Built for Padang Restaurants**

This system understands the unique needs of Padang restaurants:
- **Traditional recipe management** with precise ingredient ratios
- **Spice level tracking** for customer preferences
- **Signature dish highlighting** for marketing
- **Multi-location consistency** while allowing local customization
- **Indonesian currency and locale** support throughout

**Ready to transform your Padang restaurant operations!** 🍛✨
