"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/inventory/AddStockModal */ \"(app-pages-browser)/./src/components/inventory/AddStockModal.tsx\");\n/* harmony import */ var _components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/inventory/UpdateStockModal */ \"(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx\");\n/* harmony import */ var _components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/inventory/TransferStockModal */ \"(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx\");\n/* harmony import */ var _components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/inventory/BulkUpdateModal */ \"(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InventoryPage() {\n    var _warehouses_find;\n    _s();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [warehouses, setWarehouses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWarehouse, setSelectedWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lowStockItems, setLowStockItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInventoryLoading, setIsInventoryLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddStockModalOpen, setIsAddStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferStockModalOpen, setIsTransferStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWarehouses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadWarehouses]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.warehouseService.getAllWarehouses();\n                setWarehouses(data);\n                // Auto-select first warehouse if available and no warehouse is selected\n                if (data.length > 0 && !selectedWarehouse) {\n                    setSelectedWarehouse(data[0].id);\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';\n                setError(errorMessage);\n                console.error('Error loading warehouses:', err);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadWarehouses]\"], [\n        selectedWarehouse\n    ]);\n    const loadInventory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadInventory]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null) // Clear previous errors\n                ;\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getWarehouseInventory(warehouseId);\n                setInventory(data || []) // Ensure we always have an array\n                ;\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory';\n                setError(errorMessage);\n                console.error('Error loading inventory:', err);\n                setInventory([]) // Reset inventory on error\n                ;\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadInventory]\"], []);\n    const loadLowStockItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadLowStockItems]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getLowStockItems();\n                setLowStockItems(data || []);\n            } catch (err) {\n                console.error('Failed to load low stock items:', err);\n            // Don't set error state for this as it's not critical\n            }\n        }\n    }[\"InventoryPage.useCallback[loadLowStockItems]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            const initializeData = {\n                \"InventoryPage.useEffect.initializeData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        loadWarehouses(),\n                        loadLowStockItems()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"InventoryPage.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"InventoryPage.useEffect\"], [\n        loadWarehouses,\n        loadLowStockItems\n    ]);\n    // Load inventory when warehouse changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            if (selectedWarehouse) {\n                loadInventory(selectedWarehouse);\n            } else {\n                setInventory([]);\n            }\n        }\n    }[\"InventoryPage.useEffect\"], [\n        selectedWarehouse,\n        loadInventory\n    ]);\n    const getStockStatus = (item)=>{\n        if (item.current_stock <= item.minimum_stock) {\n            return {\n                status: 'critical',\n                color: 'text-red-600 bg-red-100'\n            };\n        } else if (item.current_stock <= item.reorder_point) {\n            return {\n                status: 'low',\n                color: 'text-yellow-600 bg-yellow-100'\n            };\n        } else {\n            return {\n                status: 'good',\n                color: 'text-green-600 bg-green-100'\n            };\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const calculateInventoryStats = ()=>{\n        const totalItems = inventory.length;\n        const lowStockCount = inventory.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        const reorderNeededCount = inventory.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const totalValue = inventory.reduce((total, item)=>{\n            const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n            return total + itemValue;\n        }, 0);\n        return {\n            totalItems,\n            lowStockCount,\n            reorderNeededCount,\n            totalValue\n        };\n    };\n    const handleWarehouseChange = (event)=>{\n        setSelectedWarehouse(event.target.value);\n    };\n    const handleAddStock = ()=>{\n        setIsAddStockModalOpen(true);\n    };\n    const handleBulkUpdate = ()=>{\n        setIsBulkUpdateModalOpen(true);\n    };\n    const handleUpdateStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsUpdateStockModalOpen(true);\n        }\n    };\n    const handleTransferStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsTransferStockModalOpen(true);\n        }\n    };\n    const handleStockUpdated = ()=>{\n        if (selectedWarehouse) {\n            loadInventory(selectedWarehouse);\n        }\n        loadLowStockItems();\n    };\n    const stats = calculateInventoryStats();\n    // Show loading spinner during initial load\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Inventory Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWarehouse,\n                                        onChange: handleWarehouseChange,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                        disabled: warehouses.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Warehouse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            warehouses.map((warehouse)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: warehouse.id,\n                                                    children: [\n                                                        warehouse.name,\n                                                        \" (\",\n                                                        warehouse.code,\n                                                        \")\"\n                                                    ]\n                                                }, warehouse.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddStock,\n                                                disabled: !selectedWarehouse,\n                                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Add Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleBulkUpdate,\n                                                disabled: !selectedWarehouse || inventory.length === 0,\n                                                className: \"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Bulk Update\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"absolute top-0 bottom-0 right-0 px-4 py-3\",\n                                \"aria-label\": \"Close error message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    lowStockItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 text-xl mr-3\",\n                                    \"aria-hidden\": \"true\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-red-800\",\n                                            children: \"Low Stock Alert\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: [\n                                                lowStockItems.length,\n                                                \" item\",\n                                                lowStockItems.length !== 1 ? 's' : '',\n                                                lowStockItems.length === 1 ? ' is' : ' are',\n                                                \" running low across all warehouses\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: stats.totalItems\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Low Stock Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: stats.lowStockCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCB0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: formatCurrency(stats.totalValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Reorder Needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: stats.reorderNeededCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Current Inventory\",\n                                        selectedWarehouse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                ((_warehouses_find = warehouses.find((w)=>w.id === selectedWarehouse)) === null || _warehouses_find === void 0 ? void 0 : _warehouses_find.name) || 'Unknown Warehouse'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            isInventoryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, this) : !selectedWarehouse ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"Please select a warehouse to view inventory\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this) : inventory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"No inventory items found for this warehouse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Ingredient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Min/Max\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: inventory.map((item)=>{\n                                                const stockStatus = getStockStatus(item);\n                                                const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.ingredient.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.ingredient.code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    item.current_stock.toLocaleString(),\n                                                                    \" \",\n                                                                    item.ingredient.unit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(stockStatus.color),\n                                                                children: stockStatus.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: [\n                                                                item.minimum_stock.toLocaleString(),\n                                                                \" / \",\n                                                                item.maximum_stock.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(itemValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpdateStock(item.id),\n                                                                    className: \"text-indigo-600 hover:text-indigo-900 mr-3 transition-colors\",\n                                                                    children: \"Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleTransferStock(item.id),\n                                                                    className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                    children: \"Transfer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isAddStockModalOpen,\n                        onClose: ()=>setIsAddStockModalOpen(false),\n                        warehouseId: selectedWarehouse,\n                        onStockAdded: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: isUpdateStockModalOpen,\n                        onClose: ()=>setIsUpdateStockModalOpen(false),\n                        item: selectedItem,\n                        onStockUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: isTransferStockModalOpen,\n                        onClose: ()=>setIsTransferStockModalOpen(false),\n                        item: selectedItem,\n                        onTransferCreated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        isOpen: isBulkUpdateModalOpen,\n                        onClose: ()=>setIsBulkUpdateModalOpen(false),\n                        items: inventory,\n                        onBulkUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"mXes4mXyA0gAywNKq7n/MQuDdtk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx":
/*!******************************************************!*\
  !*** ./src/components/inventory/BulkUpdateModal.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BulkUpdateModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction BulkUpdateModal(param) {\n    let { isOpen, onClose, items, onBulkUpdated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [bulkItems, setBulkItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [globalMovementType, setGlobalMovementType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('adjustment');\n    const [globalNotes, setGlobalNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BulkUpdateModal.useEffect\": ()=>{\n            if (isOpen && items.length > 0) {\n                const initialBulkItems = items.map({\n                    \"BulkUpdateModal.useEffect.initialBulkItems\": (item)=>({\n                            inventoryId: item.id,\n                            newStock: item.current_stock,\n                            movementType: 'adjustment',\n                            notes: '',\n                            selected: false\n                        })\n                }[\"BulkUpdateModal.useEffect.initialBulkItems\"]);\n                setBulkItems(initialBulkItems);\n                setError('');\n                setSelectAll(false);\n                setGlobalMovementType('adjustment');\n                setGlobalNotes('');\n            }\n        }\n    }[\"BulkUpdateModal.useEffect\"], [\n        isOpen,\n        items\n    ]);\n    const handleSelectAll = (checked)=>{\n        setSelectAll(checked);\n        setBulkItems((prev)=>prev.map((item)=>({\n                    ...item,\n                    selected: checked\n                })));\n    };\n    const handleItemSelect = (inventoryId, selected)=>{\n        setBulkItems((prev)=>prev.map((item)=>item.inventoryId === inventoryId ? {\n                    ...item,\n                    selected\n                } : item));\n        // Update select all state\n        const updatedItems = bulkItems.map((item)=>item.inventoryId === inventoryId ? {\n                ...item,\n                selected\n            } : item);\n        setSelectAll(updatedItems.every((item)=>item.selected));\n    };\n    const handleStockChange = (inventoryId, newStock)=>{\n        setBulkItems((prev)=>prev.map((item)=>item.inventoryId === inventoryId ? {\n                    ...item,\n                    newStock: Math.max(0, newStock)\n                } : item));\n    };\n    const handleMovementTypeChange = (inventoryId, movementType)=>{\n        setBulkItems((prev)=>prev.map((item)=>item.inventoryId === inventoryId ? {\n                    ...item,\n                    movementType\n                } : item));\n    };\n    const handleNotesChange = (inventoryId, notes)=>{\n        setBulkItems((prev)=>prev.map((item)=>item.inventoryId === inventoryId ? {\n                    ...item,\n                    notes\n                } : item));\n    };\n    const applyGlobalSettings = ()=>{\n        setBulkItems((prev)=>prev.map((item)=>item.selected ? {\n                    ...item,\n                    movementType: globalMovementType,\n                    notes: globalNotes\n                } : item));\n    };\n    const handleBulkUpdate = async ()=>{\n        const selectedItems = bulkItems.filter((item)=>item.selected);\n        if (selectedItems.length === 0) {\n            setError('Please select at least one item to update');\n            return;\n        }\n        // Validate all selected items\n        for (const item of selectedItems){\n            const inventoryItem = items.find((inv)=>inv.id === item.inventoryId);\n            if (!inventoryItem) continue;\n            if (item.newStock < 0) {\n                setError(\"Stock cannot be negative for \".concat(inventoryItem.ingredient.name));\n                return;\n            }\n            if (item.newStock > inventoryItem.maximum_stock && inventoryItem.maximum_stock > 0) {\n                setError(\"Stock cannot exceed maximum limit for \".concat(inventoryItem.ingredient.name));\n                return;\n            }\n        }\n        setIsLoading(true);\n        setError('');\n        try {\n            const updates = selectedItems.map((item)=>({\n                    inventoryId: item.inventoryId,\n                    newStock: item.newStock,\n                    movementType: item.movementType,\n                    notes: item.notes || globalNotes\n                }));\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.inventoryService.bulkUpdateStock(updates, user === null || user === void 0 ? void 0 : user.id);\n            if (result.success) {\n                onBulkUpdated();\n                handleClose();\n            } else {\n                setError(\"Bulk update completed with \".concat(result.failureCount, \" failures. Check individual items for details.\"));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to perform bulk update');\n            console.error(err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        setBulkItems([]);\n        setError('');\n        setSelectAll(false);\n        setGlobalMovementType('adjustment');\n        setGlobalNotes('');\n        onClose();\n    };\n    if (!isOpen || items.length === 0) return null;\n    const selectedCount = bulkItems.filter((item)=>item.selected).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative top-10 mx-auto p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Bulk Update Inventory\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Update multiple inventory items at once\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 mb-3\",\n                                children: \"Global Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Movement Type\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: globalMovementType,\n                                                onChange: (e)=>setGlobalMovementType(e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"adjustment\",\n                                                        children: \"Adjustment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"in\",\n                                                        children: \"Stock In\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"out\",\n                                                        children: \"Stock Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"waste\",\n                                                        children: \"Waste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Global Notes\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: globalNotes,\n                                                onChange: (e)=>setGlobalNotes(e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                                placeholder: \"Notes for all selected items\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: applyGlobalSettings,\n                                            disabled: selectedCount === 0,\n                                            className: \"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                \"Apply to Selected (\",\n                                                selectedCount,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectAll,\n                                                        onChange: (e)=>handleSelectAll(e.target.checked),\n                                                        className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Ingredient\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Current Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"New Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Movement Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: items.map((item, index)=>{\n                                            const bulkItem = bulkItems[index];\n                                            if (!bulkItem) return null;\n                                            const stockChange = bulkItem.newStock - item.current_stock;\n                                            const isOverMax = bulkItem.newStock > item.maximum_stock && item.maximum_stock > 0;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: bulkItem.selected ? 'bg-blue-50' : '',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: bulkItem.selected,\n                                                            onChange: (e)=>handleItemSelect(item.id, e.target.checked),\n                                                            className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: item.ingredient.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        item.ingredient.code,\n                                                                        \" | \",\n                                                                        item.warehouse.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: [\n                                                            item.current_stock.toLocaleString(),\n                                                            \" \",\n                                                            item.ingredient.unit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.001\",\n                                                                min: \"0\",\n                                                                value: bulkItem.newStock,\n                                                                onChange: (e)=>handleStockChange(item.id, parseFloat(e.target.value) || 0),\n                                                                className: \"w-24 border rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 \".concat(isOverMax ? 'border-red-300 bg-red-50' : 'border-gray-300')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-1 text-sm text-gray-500\",\n                                                                children: item.ingredient.unit\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            stockChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs \".concat(stockChange > 0 ? 'text-green-600' : 'text-red-600'),\n                                                                children: [\n                                                                    stockChange > 0 ? '+' : '',\n                                                                    stockChange.toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: bulkItem.movementType,\n                                                            onChange: (e)=>handleMovementTypeChange(item.id, e.target.value),\n                                                            className: \"border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"adjustment\",\n                                                                    children: \"Adjustment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"in\",\n                                                                    children: \"Stock In\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"out\",\n                                                                    children: \"Stock Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"waste\",\n                                                                    children: \"Waste\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: bulkItem.notes,\n                                                            onChange: (e)=>handleNotesChange(item.id, e.target.value),\n                                                            className: \"w-32 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                                            placeholder: \"Optional notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-blue-50 rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-blue-900\",\n                                children: \"Update Summary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800\",\n                                children: [\n                                    selectedCount,\n                                    \" of \",\n                                    items.length,\n                                    \" items selected for bulk update\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleClose,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBulkUpdate,\n                                disabled: isLoading || selectedCount === 0,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? 'Updating...' : \"Update \".concat(selectedCount, \" Items\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\inventory\\\\BulkUpdateModal.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(BulkUpdateModal, \"zCQhqW+LA4/pM541lmEHCT5j8Mg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = BulkUpdateModal;\nvar _c;\n$RefreshReg$(_c, \"BulkUpdateModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx\n"));

/***/ })

});