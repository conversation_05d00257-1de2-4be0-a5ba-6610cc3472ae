"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses\n    // async getLowStockItems(branchId?: string) {\n    //   const { data, error } = await supabase\n    //     .rpc('get_low_stock_items', { branch_id: branchId })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getLowStockItems (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \");\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        const lowStockItems = data === null || data === void 0 ? void 0 : data.filter((item)=>item.current_stock < item.minimum_stock).sort((a, b)=>a.current_stock - b.current_stock);\n        return lowStockItems;\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(unit, name),\\n        warehouse:warehouses(name, code)\\n      \").eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(\"Failed to fetch inventory: \".concat(fetchError.message));\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            var _inventory_ingredient;\n            throw new Error(\"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit));\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            var _inventory_ingredient1, _inventory_ingredient2, _inventory_warehouse;\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(\"Failed to update inventory: \".concat(updateError.message));\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: ((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.unit) || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || \"Stock \".concat(movementType, \" - \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.name, \" at \").concat((_inventory_warehouse = inventory.warehouse) === null || _inventory_warehouse === void 0 ? void 0 : _inventory_warehouse.name),\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(\"Failed to record stock movement: \".concat(movementError.message));\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Stock update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: (ingredient === null || ingredient === void 0 ? void 0 : ingredient.unit) || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*),\\n        performer:profiles(full_name)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n        *,\\n        from_warehouse:warehouses!from_warehouse_id(*),\\n        to_warehouse:warehouses!to_warehouse_id(*),\\n        requester:profiles!requested_by(*),\\n        transfer_items(*, ingredient:ingredients(*))\\n      \").eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(\"from_warehouse_id.eq.\".concat(warehouseId, \",to_warehouse_id.eq.\").concat(warehouseId));\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer with enhanced validation\n    async approveTransfer (transferId, approvedBy, approvedItems) {\n        try {\n            // Validate transfer exists and is in pending status\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'pending') {\n                throw new Error(\"Cannot approve transfer with status: \".concat(transfer.status));\n            }\n            // Validate stock availability for approved quantities\n            for (const approvedItem of approvedItems){\n                const transferItem = transfer.transfer_items.find((item)=>item.id === approvedItem.id);\n                if (!transferItem) {\n                    throw new Error(\"Transfer item not found: \".concat(approvedItem.id));\n                }\n                // Check current stock in source warehouse\n                const { data: inventory, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', transferItem.ingredient_id).single();\n                if (inventoryError) {\n                    throw new Error(\"Cannot verify stock for ingredient: \".concat(transferItem.ingredient.name));\n                }\n                if (inventory.current_stock < approvedItem.approved_quantity) {\n                    throw new Error(\"Insufficient stock for \".concat(transferItem.ingredient.name, \". Available: \").concat(inventory.current_stock, \", Requested: \").concat(approvedItem.approved_quantity));\n                }\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'approved',\n                approved_by: approvedBy,\n                approved_at: timestamp\n            }).eq('id', transferId);\n            if (transferError) {\n                throw new Error(\"Failed to approve transfer: \".concat(transferError.message));\n            }\n            // Update approved quantities for items\n            for (const item of approvedItems){\n                const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                    approved_quantity: item.approved_quantity\n                }).eq('id', item.id);\n                if (itemError) {\n                    throw new Error(\"Failed to update approved quantity: \".concat(itemError.message));\n                }\n            }\n            return {\n                success: true,\n                transferId,\n                approvedAt: timestamp,\n                approvedItems: approvedItems.length\n            };\n        } catch (error) {\n            throw new Error(\"Transfer approval failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Complete transfer and update inventory levels\n    async completeTransfer (transferId, completedBy) {\n        try {\n            // Fetch transfer with all details\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {\n                throw new Error(\"Cannot complete transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            const stockMovements = [];\n            // Process each transfer item\n            for (const item of transfer.transfer_items){\n                const approvedQty = item.approved_quantity || item.requested_quantity;\n                // Reduce stock from source warehouse\n                const { data: sourceInventory, error: sourceError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', item.ingredient_id).single();\n                if (sourceError) {\n                    throw new Error(\"Source inventory not found for \".concat(item.ingredient.name));\n                }\n                if (sourceInventory.current_stock < approvedQty) {\n                    throw new Error(\"Insufficient stock for \".concat(item.ingredient.name, \". Available: \").concat(sourceInventory.current_stock, \", Required: \").concat(approvedQty));\n                }\n                // Update source inventory\n                const { error: sourceUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: sourceInventory.current_stock - approvedQty,\n                    updated_at: timestamp\n                }).eq('id', sourceInventory.id);\n                if (sourceUpdateError) {\n                    throw new Error(\"Failed to update source inventory: \".concat(sourceUpdateError.message));\n                }\n                // Add stock movement for source (outbound)\n                stockMovements.push({\n                    warehouse_id: transfer.from_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_out',\n                    reference_id: transferId,\n                    notes: \"Transfer out to \".concat(transfer.to_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n                // Check if destination inventory exists\n                const { data: destInventory, error: destFetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.to_warehouse_id).eq('ingredient_id', item.ingredient_id).maybeSingle();\n                if (destFetchError) {\n                    throw new Error(\"Error checking destination inventory: \".concat(destFetchError.message));\n                }\n                if (destInventory) {\n                    // Update existing destination inventory\n                    const { error: destUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: destInventory.current_stock + approvedQty,\n                        updated_at: timestamp,\n                        last_restocked_at: timestamp\n                    }).eq('id', destInventory.id);\n                    if (destUpdateError) {\n                        throw new Error(\"Failed to update destination inventory: \".concat(destUpdateError.message));\n                    }\n                } else {\n                    // Create new destination inventory record\n                    const { error: destCreateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n                        warehouse_id: transfer.to_warehouse_id,\n                        ingredient_id: item.ingredient_id,\n                        current_stock: approvedQty,\n                        minimum_stock: 0,\n                        maximum_stock: approvedQty * 10,\n                        reorder_point: approvedQty * 0.2,\n                        last_restocked_at: timestamp\n                    });\n                    if (destCreateError) {\n                        throw new Error(\"Failed to create destination inventory: \".concat(destCreateError.message));\n                    }\n                }\n                // Add stock movement for destination (inbound)\n                stockMovements.push({\n                    warehouse_id: transfer.to_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_in',\n                    reference_id: transferId,\n                    notes: \"Transfer in from \".concat(transfer.from_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n            }\n            // Insert all stock movements\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n            if (movementError) {\n                throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n            }\n            // Update transfer status to completed\n            const { error: completeError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'completed',\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (completeError) {\n                throw new Error(\"Failed to complete transfer: \".concat(completeError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                completedAt: timestamp,\n                itemsTransferred: transfer.transfer_items.length,\n                stockMovements: stockMovements.length\n            };\n        } catch (error) {\n            throw new Error(\"Transfer completion failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Cancel transfer\n    async cancelTransfer (transferId, cancelledBy, reason) {\n        try {\n            // Validate transfer exists and can be cancelled\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select('*').eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (![\n                'pending',\n                'approved',\n                'in_transit'\n            ].includes(transfer.status)) {\n                throw new Error(\"Cannot cancel transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: cancelError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'cancelled',\n                notes: transfer.notes ? \"\".concat(transfer.notes, \"\\n\\nCancelled: \").concat(reason || 'No reason provided') : \"Cancelled: \".concat(reason || 'No reason provided'),\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (cancelError) {\n                throw new Error(\"Failed to cancel transfer: \".concat(cancelError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                cancelledAt: timestamp,\n                reason: reason || 'No reason provided'\n            };\n        } catch (error) {\n            throw new Error(\"Transfer cancellation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    var _inventory_ingredient, _inventory_ingredient1;\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n              *,\\n              ingredient:ingredients(unit, name),\\n              warehouse:warehouses(name, code)\\n            \").eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to fetch inventory: \".concat(fetchError.message)\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        var _inventory_ingredient2;\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.unit)\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to update inventory: \".concat(updateError.message)\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: ((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit) || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || \"Bulk stock \".concat(update.movementType, \" - \").concat((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.name),\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Bulk update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*),\\n        warehouses(*),\\n        kitchens(*)\\n      \").eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        branch:branches(*),\\n        manager:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        manager:profiles(*)\\n      \").eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy) {\n        const batchNumber = \"BATCH-\".concat(Date.now());\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString()\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        starter:profiles!started_by(*),\\n        completer:profiles!completed_by(*)\\n      \");\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name)\\n      \").eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(\"\\n        *,\\n        branch:branches(*),\\n        server:profiles!served_by(*),\\n        sales_transaction_items(\\n          *,\\n          menu_item:menu_items(*)\\n        )\\n      \");\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n        *,\\n        recipe:recipes(*)\\n      \").eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(\"\\n        *,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        )\\n      \").eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        current_stock,\\n        minimum_stock,\\n        ingredient:ingredients(cost_per_unit)\\n      \").in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>{\n            var _item_ingredient;\n            return sum + item.current_stock * (((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0);\n        }, 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        total_price,\\n        menu_item:menu_items(name, category),\\n        sales_transactions!inner(created_at, branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZGF0YWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFxQztBQUVyQyxpQ0FBaUM7QUFDMUIsTUFBTUMsbUJBQW1CO0lBQzlCLDBDQUEwQztJQUMxQyxNQUFNQyx1QkFBc0JDLFdBQW1CO1FBQzdDLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUUsNkZBS1JDLEVBQUUsQ0FBQyxnQkFBZ0JMLGFBQ25CTSxLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFMUMsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsNENBQTRDO0lBQzVDLDhDQUE4QztJQUM5QywyQ0FBMkM7SUFDM0MsMkRBQTJEO0lBRTNELDJCQUEyQjtJQUMzQixnQkFBZ0I7SUFDaEIsS0FBSztJQUNMLE1BQU1PLGtCQUFpQkMsUUFBaUI7UUFDdEMsSUFBSUMsUUFBUWIsK0NBQVFBLENBQ2pCTSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFFO1FBTVgsSUFBSUssVUFBVTtZQUNaQyxRQUFRQSxNQUFNTCxFQUFFLENBQUMsd0JBQXdCSTtRQUMzQztRQUVBLE1BQU0sRUFBRVIsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUTtRQUU5QixJQUFJUixPQUFPLE1BQU1BO1FBRWpCLHVDQUF1QztRQUN2QyxNQUFNUyxnQkFBZ0JWLGlCQUFBQSwyQkFBQUEsS0FBTVcsTUFBTSxDQUFDQyxDQUFBQSxPQUNqQ0EsS0FBS0MsYUFBYSxHQUFHRCxLQUFLRSxhQUFhLEVBQ3ZDQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRUgsYUFBYSxHQUFHSSxFQUFFSixhQUFhO1FBRWxELE9BQU9IO0lBQ1Q7SUFFQSw4Q0FBOEM7SUFDOUMseUJBQXlCO0lBQ3pCLHlCQUF5QjtJQUN6QixnQkFBZ0I7SUFDaEIsV0FBVztJQUNYLG1DQUFtQztJQUNuQyxnQ0FBZ0M7SUFDaEMsU0FBUztJQUNULHlDQUF5QztJQUV6QyxvQkFBb0I7SUFDcEIseURBQXlEO0lBQ3pELE1BQU07SUFFTixvRkFBb0Y7SUFFcEYsMkJBQTJCO0lBQzNCLGdCQUFnQjtJQUNoQixLQUFLO0lBRUwsc0VBQXNFO0lBQ3RFLE1BQU1RLGFBQVlDLFdBQW1CLEVBQUVDLFFBQWdCLEVBQUVDLFlBQW9CLEVBQUVDLEtBQWMsRUFBRUMsV0FBb0I7UUFDakgsbUJBQW1CO1FBQ25CLElBQUksQ0FBQ0osZUFBZUMsV0FBVyxHQUFHO1lBQ2hDLE1BQU0sSUFBSUksTUFBTTtRQUNsQjtRQUVBLElBQUksQ0FBQztZQUFDO1lBQU07WUFBTztZQUFjO1NBQVEsQ0FBQ0MsUUFBUSxDQUFDSixlQUFlO1lBQ2hFLE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLG9EQUFvRDtRQUNwRCxNQUFNLEVBQUV4QixNQUFNMEIsU0FBUyxFQUFFekIsT0FBTzBCLFVBQVUsRUFBRSxHQUFHLE1BQU0vQiwrQ0FBUUEsQ0FDMURNLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUUsK0dBS1JDLEVBQUUsQ0FBQyxNQUFNZSxhQUNUUyxNQUFNO1FBRVQsSUFBSUQsWUFBWTtZQUNkLE1BQU0sSUFBSUgsTUFBTSw4QkFBaUQsT0FBbkJHLFdBQVdFLE9BQU87UUFDbEU7UUFFQSxNQUFNQyxXQUFXVixXQUFXTSxVQUFVYixhQUFhO1FBQ25ELE1BQU1rQixhQUFhRCxXQUFXO1FBRTlCLDJCQUEyQjtRQUMzQixJQUFJVixXQUFXTSxVQUFVTSxhQUFhLElBQUlOLFVBQVVNLGFBQWEsR0FBRyxHQUFHO2dCQUNjTjtZQUFuRixNQUFNLElBQUlGLE1BQU0sK0NBQXdDRSxVQUFVTSxhQUFhLEVBQUMsS0FBOEIsUUFBM0JOLHdCQUFBQSxVQUFVTyxVQUFVLGNBQXBCUCw0Q0FBQUEsc0JBQXNCUSxJQUFJO1FBQy9HO1FBRUEsbUVBQW1FO1FBQ25FLElBQUliLGlCQUFpQixTQUFTRCxXQUFXLEdBQUc7WUFDMUMsTUFBTSxJQUFJSSxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtnQkEwQlFFLHdCQUVxQ0Esd0JBQWlDQTtZQTNCaEYsc0RBQXNEO1lBQ3RELE1BQU1TLFlBQVksSUFBSUMsT0FBT0MsV0FBVztZQUV4QywwQkFBMEI7WUFDMUIsTUFBTSxFQUFFcEMsT0FBT3FDLFdBQVcsRUFBRSxHQUFHLE1BQU0xQywrQ0FBUUEsQ0FDMUNNLElBQUksQ0FBQyxhQUNMcUMsTUFBTSxDQUFDO2dCQUNOMUIsZUFBZU87Z0JBQ2ZvQixZQUFZTDtnQkFDWk0sbUJBQW1CVixhQUFhSSxZQUFZVCxVQUFVZSxpQkFBaUI7WUFDekUsR0FDQ3JDLEVBQUUsQ0FBQyxNQUFNZTtZQUVaLElBQUltQixhQUFhO2dCQUNmLE1BQU0sSUFBSWQsTUFBTSwrQkFBbUQsT0FBcEJjLFlBQVlULE9BQU87WUFDcEU7WUFFQSw4Q0FBOEM7WUFDOUMsTUFBTSxFQUFFNUIsT0FBT3lDLGFBQWEsRUFBRSxHQUFHLE1BQU05QywrQ0FBUUEsQ0FDNUNNLElBQUksQ0FBQyxtQkFDTHlDLE1BQU0sQ0FBQztnQkFDTkMsY0FBY2xCLFVBQVVrQixZQUFZO2dCQUNwQ0MsZUFBZW5CLFVBQVVtQixhQUFhO2dCQUN0Q0MsZUFBZXpCO2dCQUNmUyxVQUFVaUIsS0FBS0MsR0FBRyxDQUFDbEI7Z0JBQ25CSSxNQUFNUixFQUFBQSx5QkFBQUEsVUFBVU8sVUFBVSxjQUFwQlAsNkNBQUFBLHVCQUFzQlEsSUFBSSxLQUFJO2dCQUNwQ2UsZ0JBQWdCO2dCQUNoQjNCLE9BQU9BLFNBQVMsZ0JBQVNELGNBQWEsZUFBS0sseUJBQUFBLFVBQVVPLFVBQVUsY0FBcEJQLDZDQUFBQSx1QkFBc0J3QixJQUFJLEVBQUMsUUFBZ0MsUUFBMUJ4Qix1QkFBQUEsVUFBVXlCLFNBQVMsY0FBbkJ6QiwyQ0FBQUEscUJBQXFCd0IsSUFBSTtnQkFDckdFLGNBQWM3QjtnQkFDZDhCLFlBQVlsQjtZQUNkO1lBRUYsSUFBSU8sZUFBZTtnQkFDakIsMkNBQTJDO2dCQUMzQyxNQUFNOUMsK0NBQVFBLENBQ1hNLElBQUksQ0FBQyxhQUNMcUMsTUFBTSxDQUFDO29CQUNOMUIsZUFBZWEsVUFBVWIsYUFBYTtvQkFDdEMyQixZQUFZZCxVQUFVYyxVQUFVO2dCQUNsQyxHQUNDcEMsRUFBRSxDQUFDLE1BQU1lO2dCQUVaLE1BQU0sSUFBSUssTUFBTSxvQ0FBMEQsT0FBdEJrQixjQUFjYixPQUFPO1lBQzNFO1lBRUEsT0FBTztnQkFDTHlCLFNBQVM7Z0JBQ1RDLGVBQWU3QixVQUFVYixhQUFhO2dCQUN0Q08sVUFBVUE7Z0JBQ1ZVLFVBQVVpQixLQUFLQyxHQUFHLENBQUNsQjtnQkFDbkJUO2dCQUNBYztZQUNGO1FBQ0YsRUFBRSxPQUFPbEMsT0FBTztZQUNkLE1BQU0sSUFBSXVCLE1BQU0sd0JBQWlGLE9BQXpEdkIsaUJBQWlCdUIsUUFBUXZCLE1BQU00QixPQUFPLEdBQUc7UUFDbkY7SUFDRjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNMkIsdUJBQXNCekQsV0FBbUIsRUFBRTBELFlBQW9CLEVBQUVDLFlBQW9CLEVBQUVwQyxLQUFjO1FBQ3pHLE1BQU0sRUFBRXRCLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsYUFDTHlDLE1BQU0sQ0FBQztZQUNOQyxjQUFjN0M7WUFDZDhDLGVBQWVZO1lBQ2Y1QyxlQUFlNkM7WUFDZjVDLGVBQWU7WUFDZmtCLGVBQWUwQixlQUFlO1lBQzlCQyxlQUFlRCxlQUFlO1FBQ2hDLEdBQ0N2RCxNQUFNLEdBQ055QixNQUFNO1FBRVQsSUFBSTNCLE9BQU8sTUFBTUE7UUFFakIsZ0NBQWdDO1FBQ2hDLE1BQU0sRUFBRUQsTUFBTWlDLFVBQVUsRUFBRSxHQUFHLE1BQU1yQywrQ0FBUUEsQ0FDeENNLElBQUksQ0FBQyxlQUNMQyxNQUFNLENBQUMsUUFDUEMsRUFBRSxDQUFDLE1BQU1xRCxjQUNUN0IsTUFBTTtRQUVULE1BQU1oQywrQ0FBUUEsQ0FDWE0sSUFBSSxDQUFDLG1CQUNMeUMsTUFBTSxDQUFDO1lBQ05DLGNBQWM3QztZQUNkOEMsZUFBZVk7WUFDZlgsZUFBZTtZQUNmaEIsVUFBVTRCO1lBQ1Z4QixNQUFNRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlDLElBQUksS0FBSTtZQUMxQmUsZ0JBQWdCO1lBQ2hCM0IsT0FBT0EsU0FBUztRQUNsQjtRQUVGLE9BQU90QjtJQUNUO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU00RCxtQkFBa0I3RCxXQUFtQjtZQUFFOEQsUUFBQUEsaUVBQVE7UUFDbkQsTUFBTSxFQUFFN0QsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyxtQkFDTEMsTUFBTSxDQUFFLHFJQU1SQyxFQUFFLENBQUMsZ0JBQWdCTCxhQUNuQk0sS0FBSyxDQUFDLGNBQWM7WUFBRUMsV0FBVztRQUFNLEdBQ3ZDdUQsS0FBSyxDQUFDQTtRQUVULElBQUk1RCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTThELGdCQUFlQyxlQUF1QixFQUFFQyxhQUFxQixFQUFFQyxLQUFZLEVBQUVDLFdBQW1CLEVBQUU1QyxLQUFjO1FBQ3BILHlCQUF5QjtRQUN6QixNQUFNLEVBQUV0QixNQUFNbUUsUUFBUSxFQUFFbEUsT0FBT21FLGFBQWEsRUFBRSxHQUFHLE1BQU14RSwrQ0FBUUEsQ0FDNURNLElBQUksQ0FBQyx1QkFDTHlDLE1BQU0sQ0FBQztZQUNOMEIsbUJBQW1CTjtZQUNuQk8saUJBQWlCTjtZQUNqQk8sY0FBY0w7WUFDZE0sYUFBYVAsTUFBTVEsTUFBTTtZQUN6Qm5EO1lBQ0FvRCxRQUFRO1FBQ1YsR0FDQ3ZFLE1BQU0sR0FDTnlCLE1BQU07UUFFVCxJQUFJd0MsZUFBZSxNQUFNQTtRQUV6Qix3QkFBd0I7UUFDeEIsTUFBTU8sZ0JBQWdCVixNQUFNVyxHQUFHLENBQUNoRSxDQUFBQSxPQUFTO2dCQUN2Q2lFLGFBQWFWLFNBQVNXLEVBQUU7Z0JBQ3hCakMsZUFBZWpDLEtBQUtpQyxhQUFhO2dCQUNqQ2tDLG9CQUFvQm5FLEtBQUtrQixRQUFRO2dCQUNqQ0ksTUFBTXRCLEtBQUtzQixJQUFJO2dCQUNmWixPQUFPVixLQUFLVSxLQUFLO1lBQ25CO1FBRUEsTUFBTSxFQUFFckIsT0FBTytFLFVBQVUsRUFBRSxHQUFHLE1BQU1wRiwrQ0FBUUEsQ0FDekNNLElBQUksQ0FBQyxrQkFDTHlDLE1BQU0sQ0FBQ2dDO1FBRVYsSUFBSUssWUFBWSxNQUFNQTtRQUV0QixPQUFPYjtJQUNUO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1jLHFCQUFvQmxGLFdBQW9CO1FBQzVDLElBQUlVLFFBQVFiLCtDQUFRQSxDQUNqQk0sSUFBSSxDQUFDLHVCQUNMQyxNQUFNLENBQUUseU9BT1JDLEVBQUUsQ0FBQyxVQUFVO1FBRWhCLElBQUlMLGFBQWE7WUFDZlUsUUFBUUEsTUFBTXlFLEVBQUUsQ0FBQyx3QkFBMERuRixPQUFsQ0EsYUFBWSx3QkFBa0MsT0FBWkE7UUFDN0U7UUFFQSxNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTVEsTUFBTUosS0FBSyxDQUFDLGdCQUFnQjtZQUFFQyxXQUFXO1FBQU07UUFFN0UsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1tRixpQkFBZ0JDLFVBQWtCLEVBQUVDLFVBQWtCLEVBQUVDLGFBQW9CO1FBQ2hGLElBQUk7WUFDRixvREFBb0Q7WUFDcEQsTUFBTSxFQUFFdEYsTUFBTW1FLFFBQVEsRUFBRWxFLE9BQU8wQixVQUFVLEVBQUUsR0FBRyxNQUFNL0IsK0NBQVFBLENBQ3pETSxJQUFJLENBQUMsdUJBQ0xDLE1BQU0sQ0FBRSxzTUFNUkMsRUFBRSxDQUFDLE1BQU1nRixZQUNUeEQsTUFBTTtZQUVULElBQUlELFlBQVk7Z0JBQ2QsTUFBTSxJQUFJSCxNQUFNLHVCQUEwQyxPQUFuQkcsV0FBV0UsT0FBTztZQUMzRDtZQUVBLElBQUlzQyxTQUFTTyxNQUFNLEtBQUssV0FBVztnQkFDakMsTUFBTSxJQUFJbEQsTUFBTSx3Q0FBd0QsT0FBaEIyQyxTQUFTTyxNQUFNO1lBQ3pFO1lBRUEsc0RBQXNEO1lBQ3RELEtBQUssTUFBTWEsZ0JBQWdCRCxjQUFlO2dCQUN4QyxNQUFNRSxlQUFlckIsU0FBU3NCLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDLENBQUM5RSxPQUFjQSxLQUFLa0UsRUFBRSxLQUFLUyxhQUFhVCxFQUFFO2dCQUM1RixJQUFJLENBQUNVLGNBQWM7b0JBQ2pCLE1BQU0sSUFBSWhFLE1BQU0sNEJBQTRDLE9BQWhCK0QsYUFBYVQsRUFBRTtnQkFDN0Q7Z0JBRUEsMENBQTBDO2dCQUMxQyxNQUFNLEVBQUU5RSxNQUFNMEIsU0FBUyxFQUFFekIsT0FBTzBGLGNBQWMsRUFBRSxHQUFHLE1BQU0vRiwrQ0FBUUEsQ0FDOURNLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUMsaUJBQ1BDLEVBQUUsQ0FBQyxnQkFBZ0IrRCxTQUFTRSxpQkFBaUIsRUFDN0NqRSxFQUFFLENBQUMsaUJBQWlCb0YsYUFBYTNDLGFBQWEsRUFDOUNqQixNQUFNO2dCQUVULElBQUkrRCxnQkFBZ0I7b0JBQ2xCLE1BQU0sSUFBSW5FLE1BQU0sdUNBQW9FLE9BQTdCZ0UsYUFBYXZELFVBQVUsQ0FBQ2lCLElBQUk7Z0JBQ3JGO2dCQUVBLElBQUl4QixVQUFVYixhQUFhLEdBQUcwRSxhQUFhSyxpQkFBaUIsRUFBRTtvQkFDNUQsTUFBTSxJQUFJcEUsTUFBTSwwQkFBc0VFLE9BQTVDOEQsYUFBYXZELFVBQVUsQ0FBQ2lCLElBQUksRUFBQyxpQkFBc0RxQyxPQUF2QzdELFVBQVViLGFBQWEsRUFBQyxpQkFBOEMsT0FBL0IwRSxhQUFhSyxpQkFBaUI7Z0JBQzdKO1lBQ0Y7WUFFQSxNQUFNekQsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1lBRXhDLHlCQUF5QjtZQUN6QixNQUFNLEVBQUVwQyxPQUFPbUUsYUFBYSxFQUFFLEdBQUcsTUFBTXhFLCtDQUFRQSxDQUM1Q00sSUFBSSxDQUFDLHVCQUNMcUMsTUFBTSxDQUFDO2dCQUNObUMsUUFBUTtnQkFDUm1CLGFBQWFSO2dCQUNiUyxhQUFhM0Q7WUFDZixHQUNDL0IsRUFBRSxDQUFDLE1BQU1nRjtZQUVaLElBQUloQixlQUFlO2dCQUNqQixNQUFNLElBQUk1QyxNQUFNLCtCQUFxRCxPQUF0QjRDLGNBQWN2QyxPQUFPO1lBQ3RFO1lBRUEsdUNBQXVDO1lBQ3ZDLEtBQUssTUFBTWpCLFFBQVEwRSxjQUFlO2dCQUNoQyxNQUFNLEVBQUVyRixPQUFPOEYsU0FBUyxFQUFFLEdBQUcsTUFBTW5HLCtDQUFRQSxDQUN4Q00sSUFBSSxDQUFDLGtCQUNMcUMsTUFBTSxDQUFDO29CQUNOcUQsbUJBQW1CaEYsS0FBS2dGLGlCQUFpQjtnQkFDM0MsR0FDQ3hGLEVBQUUsQ0FBQyxNQUFNUSxLQUFLa0UsRUFBRTtnQkFFbkIsSUFBSWlCLFdBQVc7b0JBQ2IsTUFBTSxJQUFJdkUsTUFBTSx1Q0FBeUQsT0FBbEJ1RSxVQUFVbEUsT0FBTztnQkFDMUU7WUFDRjtZQUVBLE9BQU87Z0JBQ0x5QixTQUFTO2dCQUNUOEI7Z0JBQ0FZLFlBQVk3RDtnQkFDWm1ELGVBQWVBLGNBQWNiLE1BQU07WUFDckM7UUFFRixFQUFFLE9BQU94RSxPQUFPO1lBQ2QsTUFBTSxJQUFJdUIsTUFBTSw2QkFBc0YsT0FBekR2QixpQkFBaUJ1QixRQUFRdkIsTUFBTTRCLE9BQU8sR0FBRztRQUN4RjtJQUNGO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1vRSxrQkFBaUJiLFVBQWtCLEVBQUVjLFdBQW1CO1FBQzVELElBQUk7WUFDRixrQ0FBa0M7WUFDbEMsTUFBTSxFQUFFbEcsTUFBTW1FLFFBQVEsRUFBRWxFLE9BQU8wQixVQUFVLEVBQUUsR0FBRyxNQUFNL0IsK0NBQVFBLENBQ3pETSxJQUFJLENBQUMsdUJBQ0xDLE1BQU0sQ0FBRSxzTUFNUkMsRUFBRSxDQUFDLE1BQU1nRixZQUNUeEQsTUFBTTtZQUVULElBQUlELFlBQVk7Z0JBQ2QsTUFBTSxJQUFJSCxNQUFNLHVCQUEwQyxPQUFuQkcsV0FBV0UsT0FBTztZQUMzRDtZQUVBLElBQUlzQyxTQUFTTyxNQUFNLEtBQUssY0FBY1AsU0FBU08sTUFBTSxLQUFLLGNBQWM7Z0JBQ3RFLE1BQU0sSUFBSWxELE1BQU0seUNBQXlELE9BQWhCMkMsU0FBU08sTUFBTTtZQUMxRTtZQUVBLE1BQU12QyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7WUFDeEMsTUFBTThELGlCQUF3QixFQUFFO1lBRWhDLDZCQUE2QjtZQUM3QixLQUFLLE1BQU12RixRQUFRdUQsU0FBU3NCLGNBQWMsQ0FBRTtnQkFDMUMsTUFBTVcsY0FBY3hGLEtBQUtnRixpQkFBaUIsSUFBSWhGLEtBQUttRSxrQkFBa0I7Z0JBRXJFLHFDQUFxQztnQkFDckMsTUFBTSxFQUFFL0UsTUFBTXFHLGVBQWUsRUFBRXBHLE9BQU9xRyxXQUFXLEVBQUUsR0FBRyxNQUFNMUcsK0NBQVFBLENBQ2pFTSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxnQkFBZ0IrRCxTQUFTRSxpQkFBaUIsRUFDN0NqRSxFQUFFLENBQUMsaUJBQWlCUSxLQUFLaUMsYUFBYSxFQUN0Q2pCLE1BQU07Z0JBRVQsSUFBSTBFLGFBQWE7b0JBQ2YsTUFBTSxJQUFJOUUsTUFBTSxrQ0FBdUQsT0FBckJaLEtBQUtxQixVQUFVLENBQUNpQixJQUFJO2dCQUN4RTtnQkFFQSxJQUFJbUQsZ0JBQWdCeEYsYUFBYSxHQUFHdUYsYUFBYTtvQkFDL0MsTUFBTSxJQUFJNUUsTUFBTSwwQkFBOEQ2RSxPQUFwQ3pGLEtBQUtxQixVQUFVLENBQUNpQixJQUFJLEVBQUMsaUJBQTJEa0QsT0FBNUNDLGdCQUFnQnhGLGFBQWEsRUFBQyxnQkFBMEIsT0FBWnVGO2dCQUM1SDtnQkFFQSwwQkFBMEI7Z0JBQzFCLE1BQU0sRUFBRW5HLE9BQU9zRyxpQkFBaUIsRUFBRSxHQUFHLE1BQU0zRywrQ0FBUUEsQ0FDaERNLElBQUksQ0FBQyxhQUNMcUMsTUFBTSxDQUFDO29CQUNOMUIsZUFBZXdGLGdCQUFnQnhGLGFBQWEsR0FBR3VGO29CQUMvQzVELFlBQVlMO2dCQUNkLEdBQ0MvQixFQUFFLENBQUMsTUFBTWlHLGdCQUFnQnZCLEVBQUU7Z0JBRTlCLElBQUl5QixtQkFBbUI7b0JBQ3JCLE1BQU0sSUFBSS9FLE1BQU0sc0NBQWdFLE9BQTFCK0Usa0JBQWtCMUUsT0FBTztnQkFDakY7Z0JBRUEsMkNBQTJDO2dCQUMzQ3NFLGVBQWVLLElBQUksQ0FBQztvQkFDbEI1RCxjQUFjdUIsU0FBU0UsaUJBQWlCO29CQUN4Q3hCLGVBQWVqQyxLQUFLaUMsYUFBYTtvQkFDakNDLGVBQWU7b0JBQ2ZoQixVQUFVc0U7b0JBQ1ZsRSxNQUFNdEIsS0FBS3NCLElBQUk7b0JBQ2ZlLGdCQUFnQjtvQkFDaEJ3RCxjQUFjckI7b0JBQ2Q5RCxPQUFPLG1CQUFtRFYsT0FBaEN1RCxTQUFTdUMsWUFBWSxDQUFDeEQsSUFBSSxFQUFDLE9BQTBCLE9BQXJCdEMsS0FBS3FCLFVBQVUsQ0FBQ2lCLElBQUk7b0JBQzlFRSxjQUFjOEM7b0JBQ2Q3QyxZQUFZbEI7Z0JBQ2Q7Z0JBRUEsd0NBQXdDO2dCQUN4QyxNQUFNLEVBQUVuQyxNQUFNMkcsYUFBYSxFQUFFMUcsT0FBTzJHLGNBQWMsRUFBRSxHQUFHLE1BQU1oSCwrQ0FBUUEsQ0FDbEVNLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGdCQUFnQitELFNBQVNHLGVBQWUsRUFDM0NsRSxFQUFFLENBQUMsaUJBQWlCUSxLQUFLaUMsYUFBYSxFQUN0Q2dFLFdBQVc7Z0JBRWQsSUFBSUQsZ0JBQWdCO29CQUNsQixNQUFNLElBQUlwRixNQUFNLHlDQUFnRSxPQUF2Qm9GLGVBQWUvRSxPQUFPO2dCQUNqRjtnQkFFQSxJQUFJOEUsZUFBZTtvQkFDakIsd0NBQXdDO29CQUN4QyxNQUFNLEVBQUUxRyxPQUFPNkcsZUFBZSxFQUFFLEdBQUcsTUFBTWxILCtDQUFRQSxDQUM5Q00sSUFBSSxDQUFDLGFBQ0xxQyxNQUFNLENBQUM7d0JBQ04xQixlQUFlOEYsY0FBYzlGLGFBQWEsR0FBR3VGO3dCQUM3QzVELFlBQVlMO3dCQUNaTSxtQkFBbUJOO29CQUNyQixHQUNDL0IsRUFBRSxDQUFDLE1BQU11RyxjQUFjN0IsRUFBRTtvQkFFNUIsSUFBSWdDLGlCQUFpQjt3QkFDbkIsTUFBTSxJQUFJdEYsTUFBTSwyQ0FBbUUsT0FBeEJzRixnQkFBZ0JqRixPQUFPO29CQUNwRjtnQkFDRixPQUFPO29CQUNMLDBDQUEwQztvQkFDMUMsTUFBTSxFQUFFNUIsT0FBTzhHLGVBQWUsRUFBRSxHQUFHLE1BQU1uSCwrQ0FBUUEsQ0FDOUNNLElBQUksQ0FBQyxhQUNMeUMsTUFBTSxDQUFDO3dCQUNOQyxjQUFjdUIsU0FBU0csZUFBZTt3QkFDdEN6QixlQUFlakMsS0FBS2lDLGFBQWE7d0JBQ2pDaEMsZUFBZXVGO3dCQUNmdEYsZUFBZTt3QkFDZmtCLGVBQWVvRSxjQUFjO3dCQUM3QnpDLGVBQWV5QyxjQUFjO3dCQUM3QjNELG1CQUFtQk47b0JBQ3JCO29CQUVGLElBQUk0RSxpQkFBaUI7d0JBQ25CLE1BQU0sSUFBSXZGLE1BQU0sMkNBQW1FLE9BQXhCdUYsZ0JBQWdCbEYsT0FBTztvQkFDcEY7Z0JBQ0Y7Z0JBRUEsK0NBQStDO2dCQUMvQ3NFLGVBQWVLLElBQUksQ0FBQztvQkFDbEI1RCxjQUFjdUIsU0FBU0csZUFBZTtvQkFDdEN6QixlQUFlakMsS0FBS2lDLGFBQWE7b0JBQ2pDQyxlQUFlO29CQUNmaEIsVUFBVXNFO29CQUNWbEUsTUFBTXRCLEtBQUtzQixJQUFJO29CQUNmZSxnQkFBZ0I7b0JBQ2hCd0QsY0FBY3JCO29CQUNkOUQsT0FBTyxvQkFBc0RWLE9BQWxDdUQsU0FBUzZDLGNBQWMsQ0FBQzlELElBQUksRUFBQyxPQUEwQixPQUFyQnRDLEtBQUtxQixVQUFVLENBQUNpQixJQUFJO29CQUNqRkUsY0FBYzhDO29CQUNkN0MsWUFBWWxCO2dCQUNkO1lBQ0Y7WUFFQSw2QkFBNkI7WUFDN0IsTUFBTSxFQUFFbEMsT0FBT3lDLGFBQWEsRUFBRSxHQUFHLE1BQU05QywrQ0FBUUEsQ0FDNUNNLElBQUksQ0FBQyxtQkFDTHlDLE1BQU0sQ0FBQ3dEO1lBRVYsSUFBSXpELGVBQWU7Z0JBQ2pCLE1BQU0sSUFBSWxCLE1BQU0scUNBQTJELE9BQXRCa0IsY0FBY2IsT0FBTztZQUM1RTtZQUVBLHNDQUFzQztZQUN0QyxNQUFNLEVBQUU1QixPQUFPZ0gsYUFBYSxFQUFFLEdBQUcsTUFBTXJILCtDQUFRQSxDQUM1Q00sSUFBSSxDQUFDLHVCQUNMcUMsTUFBTSxDQUFDO2dCQUNObUMsUUFBUTtnQkFDUndDLGNBQWMvRTtZQUNoQixHQUNDL0IsRUFBRSxDQUFDLE1BQU1nRjtZQUVaLElBQUk2QixlQUFlO2dCQUNqQixNQUFNLElBQUl6RixNQUFNLGdDQUFzRCxPQUF0QnlGLGNBQWNwRixPQUFPO1lBQ3ZFO1lBRUEsT0FBTztnQkFDTHlCLFNBQVM7Z0JBQ1Q4QjtnQkFDQStCLGFBQWFoRjtnQkFDYmlGLGtCQUFrQmpELFNBQVNzQixjQUFjLENBQUNoQixNQUFNO2dCQUNoRDBCLGdCQUFnQkEsZUFBZTFCLE1BQU07WUFDdkM7UUFFRixFQUFFLE9BQU94RSxPQUFPO1lBQ2QsTUFBTSxJQUFJdUIsTUFBTSwrQkFBd0YsT0FBekR2QixpQkFBaUJ1QixRQUFRdkIsTUFBTTRCLE9BQU8sR0FBRztRQUMxRjtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU13RixnQkFBZWpDLFVBQWtCLEVBQUVrQyxXQUFtQixFQUFFQyxNQUFlO1FBQzNFLElBQUk7WUFDRixnREFBZ0Q7WUFDaEQsTUFBTSxFQUFFdkgsTUFBTW1FLFFBQVEsRUFBRWxFLE9BQU8wQixVQUFVLEVBQUUsR0FBRyxNQUFNL0IsK0NBQVFBLENBQ3pETSxJQUFJLENBQUMsdUJBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTWdGLFlBQ1R4RCxNQUFNO1lBRVQsSUFBSUQsWUFBWTtnQkFDZCxNQUFNLElBQUlILE1BQU0sdUJBQTBDLE9BQW5CRyxXQUFXRSxPQUFPO1lBQzNEO1lBRUEsSUFBSSxDQUFDO2dCQUFDO2dCQUFXO2dCQUFZO2FBQWEsQ0FBQ0osUUFBUSxDQUFDMEMsU0FBU08sTUFBTSxHQUFHO2dCQUNwRSxNQUFNLElBQUlsRCxNQUFNLHVDQUF1RCxPQUFoQjJDLFNBQVNPLE1BQU07WUFDeEU7WUFFQSxNQUFNdkMsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1lBRXhDLHlCQUF5QjtZQUN6QixNQUFNLEVBQUVwQyxPQUFPdUgsV0FBVyxFQUFFLEdBQUcsTUFBTTVILCtDQUFRQSxDQUMxQ00sSUFBSSxDQUFDLHVCQUNMcUMsTUFBTSxDQUFDO2dCQUNObUMsUUFBUTtnQkFDUnBELE9BQU82QyxTQUFTN0MsS0FBSyxHQUFHLEdBQW1DaUcsT0FBaENwRCxTQUFTN0MsS0FBSyxFQUFDLG1CQUFnRCxPQUEvQmlHLFVBQVUsd0JBQXlCLGNBQTZDLE9BQS9CQSxVQUFVO2dCQUN0SEwsY0FBYy9FO1lBQ2hCLEdBQ0MvQixFQUFFLENBQUMsTUFBTWdGO1lBRVosSUFBSW9DLGFBQWE7Z0JBQ2YsTUFBTSxJQUFJaEcsTUFBTSw4QkFBa0QsT0FBcEJnRyxZQUFZM0YsT0FBTztZQUNuRTtZQUVBLE9BQU87Z0JBQ0x5QixTQUFTO2dCQUNUOEI7Z0JBQ0FxQyxhQUFhdEY7Z0JBQ2JvRixRQUFRQSxVQUFVO1lBQ3BCO1FBRUYsRUFBRSxPQUFPdEgsT0FBTztZQUNkLE1BQU0sSUFBSXVCLE1BQU0saUNBQTBGLE9BQXpEdkIsaUJBQWlCdUIsUUFBUXZCLE1BQU00QixPQUFPLEdBQUc7UUFDNUY7SUFDRjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNNkYsaUJBQWdCQyxPQUtwQixFQUFFcEcsV0FBb0I7UUFDdEIsSUFBSSxDQUFDb0csV0FBV0EsUUFBUWxELE1BQU0sS0FBSyxHQUFHO1lBQ3BDLE1BQU0sSUFBSWpELE1BQU07UUFDbEI7UUFFQSxNQUFNb0csVUFNRCxFQUFFO1FBRVAsTUFBTXpGLFlBQVksSUFBSUMsT0FBT0MsV0FBVztRQUN4QyxNQUFNOEQsaUJBQXdCLEVBQUU7UUFFaEMsSUFBSTtZQUNGLHNCQUFzQjtZQUN0QixLQUFLLE1BQU01RCxVQUFVb0YsUUFBUztnQkFDNUIsSUFBSTt3QkFvRU1qRyx1QkFFd0RBO29CQXJFaEUsaUJBQWlCO29CQUNqQixJQUFJLENBQUNhLE9BQU9wQixXQUFXLElBQUlvQixPQUFPbkIsUUFBUSxHQUFHLEdBQUc7d0JBQzlDd0csUUFBUXBCLElBQUksQ0FBQzs0QkFDWHJGLGFBQWFvQixPQUFPcEIsV0FBVzs0QkFDL0JtQyxTQUFTOzRCQUNUckQsT0FBTzt3QkFDVDt3QkFDQTtvQkFDRjtvQkFFQSwwQkFBMEI7b0JBQzFCLE1BQU0sRUFBRUQsTUFBTTBCLFNBQVMsRUFBRXpCLE9BQU8wQixVQUFVLEVBQUUsR0FBRyxNQUFNL0IsK0NBQVFBLENBQzFETSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFFLHVJQUtSQyxFQUFFLENBQUMsTUFBTW1DLE9BQU9wQixXQUFXLEVBQzNCUyxNQUFNO29CQUVULElBQUlELFlBQVk7d0JBQ2RpRyxRQUFRcEIsSUFBSSxDQUFDOzRCQUNYckYsYUFBYW9CLE9BQU9wQixXQUFXOzRCQUMvQm1DLFNBQVM7NEJBQ1RyRCxPQUFPLDhCQUFpRCxPQUFuQjBCLFdBQVdFLE9BQU87d0JBQ3pEO3dCQUNBO29CQUNGO29CQUVBLE1BQU1DLFdBQVdTLE9BQU9uQixRQUFRLEdBQUdNLFVBQVViLGFBQWE7b0JBRTFELDJCQUEyQjtvQkFDM0IsSUFBSTBCLE9BQU9uQixRQUFRLEdBQUdNLFVBQVVNLGFBQWEsSUFBSU4sVUFBVU0sYUFBYSxHQUFHLEdBQUc7NEJBSUFOO3dCQUg1RWtHLFFBQVFwQixJQUFJLENBQUM7NEJBQ1hyRixhQUFhb0IsT0FBT3BCLFdBQVc7NEJBQy9CbUMsU0FBUzs0QkFDVHJELE9BQU8sK0NBQXdDeUIsVUFBVU0sYUFBYSxFQUFDLEtBQThCLFFBQTNCTix5QkFBQUEsVUFBVU8sVUFBVSxjQUFwQlAsNkNBQUFBLHVCQUFzQlEsSUFBSTt3QkFDdEc7d0JBQ0E7b0JBQ0Y7b0JBRUEsbUJBQW1CO29CQUNuQixNQUFNLEVBQUVqQyxPQUFPcUMsV0FBVyxFQUFFLEdBQUcsTUFBTTFDLCtDQUFRQSxDQUMxQ00sSUFBSSxDQUFDLGFBQ0xxQyxNQUFNLENBQUM7d0JBQ04xQixlQUFlMEIsT0FBT25CLFFBQVE7d0JBQzlCb0IsWUFBWUw7d0JBQ1pNLG1CQUFtQlgsV0FBVyxJQUFJSyxZQUFZVCxVQUFVZSxpQkFBaUI7b0JBQzNFLEdBQ0NyQyxFQUFFLENBQUMsTUFBTW1DLE9BQU9wQixXQUFXO29CQUU5QixJQUFJbUIsYUFBYTt3QkFDZnNGLFFBQVFwQixJQUFJLENBQUM7NEJBQ1hyRixhQUFhb0IsT0FBT3BCLFdBQVc7NEJBQy9CbUMsU0FBUzs0QkFDVHJELE9BQU8sK0JBQW1ELE9BQXBCcUMsWUFBWVQsT0FBTzt3QkFDM0Q7d0JBQ0E7b0JBQ0Y7b0JBRUEsZ0NBQWdDO29CQUNoQ3NFLGVBQWVLLElBQUksQ0FBQzt3QkFDbEI1RCxjQUFjbEIsVUFBVWtCLFlBQVk7d0JBQ3BDQyxlQUFlbkIsVUFBVW1CLGFBQWE7d0JBQ3RDQyxlQUFlUCxPQUFPbEIsWUFBWTt3QkFDbENTLFVBQVVpQixLQUFLQyxHQUFHLENBQUNsQjt3QkFDbkJJLE1BQU1SLEVBQUFBLHdCQUFBQSxVQUFVTyxVQUFVLGNBQXBCUCw0Q0FBQUEsc0JBQXNCUSxJQUFJLEtBQUk7d0JBQ3BDZSxnQkFBZ0I7d0JBQ2hCM0IsT0FBT2lCLE9BQU9qQixLQUFLLElBQUkscUJBQWNpQixPQUFPbEIsWUFBWSxFQUFDLE9BQWdDLFFBQTNCSyx5QkFBQUEsVUFBVU8sVUFBVSxjQUFwQlAsNkNBQUFBLHVCQUFzQndCLElBQUk7d0JBQ3hGRSxjQUFjN0I7d0JBQ2Q4QixZQUFZbEI7b0JBQ2Q7b0JBRUF5RixRQUFRcEIsSUFBSSxDQUFDO3dCQUNYckYsYUFBYW9CLE9BQU9wQixXQUFXO3dCQUMvQm1DLFNBQVM7d0JBQ1RDLGVBQWU3QixVQUFVYixhQUFhO3dCQUN0Q08sVUFBVW1CLE9BQU9uQixRQUFRO29CQUMzQjtnQkFFRixFQUFFLE9BQU9uQixPQUFPO29CQUNkMkgsUUFBUXBCLElBQUksQ0FBQzt3QkFDWHJGLGFBQWFvQixPQUFPcEIsV0FBVzt3QkFDL0JtQyxTQUFTO3dCQUNUckQsT0FBT0EsaUJBQWlCdUIsUUFBUXZCLE1BQU00QixPQUFPLEdBQUc7b0JBQ2xEO2dCQUNGO1lBQ0Y7WUFFQSxzQ0FBc0M7WUFDdEMsSUFBSXNFLGVBQWUxQixNQUFNLEdBQUcsR0FBRztnQkFDN0IsTUFBTSxFQUFFeEUsT0FBT3lDLGFBQWEsRUFBRSxHQUFHLE1BQU05QywrQ0FBUUEsQ0FDNUNNLElBQUksQ0FBQyxtQkFDTHlDLE1BQU0sQ0FBQ3dEO2dCQUVWLElBQUl6RCxlQUFlO29CQUNqQixNQUFNLElBQUlsQixNQUFNLHFDQUEyRCxPQUF0QmtCLGNBQWNiLE9BQU87Z0JBQzVFO1lBQ0Y7WUFFQSxNQUFNZ0csZUFBZUQsUUFBUWpILE1BQU0sQ0FBQ21ILENBQUFBLElBQUtBLEVBQUV4RSxPQUFPLEVBQUVtQixNQUFNO1lBQzFELE1BQU1zRCxlQUFlSCxRQUFRakgsTUFBTSxDQUFDbUgsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFeEUsT0FBTyxFQUFFbUIsTUFBTTtZQUUzRCxPQUFPO2dCQUNMbkIsU0FBU3lFLGlCQUFpQjtnQkFDMUJDLGNBQWNMLFFBQVFsRCxNQUFNO2dCQUM1Qm9EO2dCQUNBRTtnQkFDQUg7Z0JBQ0F6RjtZQUNGO1FBRUYsRUFBRSxPQUFPbEMsT0FBTztZQUNkLE1BQU0sSUFBSXVCLE1BQU0sdUJBQWdGLE9BQXpEdkIsaUJBQWlCdUIsUUFBUXZCLE1BQU00QixPQUFPLEdBQUc7UUFDbEY7SUFDRjtBQUNGLEVBQUM7QUFFRCw4QkFBOEI7QUFDOUIsaUNBQWlDO0FBQ2pDLHdCQUF3QjtBQUN4Qiw2QkFBNkI7QUFDN0IsNkNBQTZDO0FBQzdDLDBCQUEwQjtBQUMxQixrQkFBa0I7QUFDbEIsYUFBYTtBQUNiLDhCQUE4QjtBQUM5QixXQUFXO0FBQ1gsK0JBQStCO0FBQy9CLHVCQUF1QjtBQUV2Qiw2QkFBNkI7QUFDN0Isa0JBQWtCO0FBQ2xCLE9BQU87QUFFUCwrQkFBK0I7QUFDL0IsK0NBQStDO0FBQy9DLDZDQUE2QztBQUM3QywwQkFBMEI7QUFDMUIsa0JBQWtCO0FBQ2xCLGFBQWE7QUFDYiwrQkFBK0I7QUFDL0IseUJBQXlCO0FBQ3pCLHNCQUFzQjtBQUN0QixXQUFXO0FBQ1gsNEJBQTRCO0FBQzVCLGtCQUFrQjtBQUVsQiw2QkFBNkI7QUFDN0Isa0JBQWtCO0FBQ2xCLE1BQU07QUFDTixJQUFJO0FBQ0csTUFBTW9HLGdCQUFnQjtJQUMzQixtQkFBbUI7SUFDbkIsTUFBTUM7UUFDSixNQUFNLEVBQUVsSSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBRSx5RUFJUkMsRUFBRSxDQUFDLGFBQWEsTUFDaEJDLEtBQUssQ0FBQztRQUVULElBQUlKLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLDBCQUEwQjtJQUMxQixNQUFNbUksa0JBQWlCM0gsUUFBZ0I7UUFDckMsTUFBTSxFQUFFUixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBRSx1SEFNUkMsRUFBRSxDQUFDLE1BQU1JLFVBQ1RvQixNQUFNO1FBRVQsSUFBSTNCLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtBQUNGLEVBQUM7QUFFRCxpQ0FBaUM7QUFDMUIsTUFBTW9JLG1CQUFtQjtJQUM5QixxQkFBcUI7SUFDckIsTUFBTUM7UUFDSixNQUFNLEVBQUVySSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSxrRkFLUkMsRUFBRSxDQUFDLGFBQWEsTUFDaEJDLEtBQUssQ0FBQztRQUVULElBQUlKLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLDJCQUEyQjtJQUMzQixNQUFNc0ksdUJBQXNCOUgsUUFBZ0I7UUFDMUMsTUFBTSxFQUFFUixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBRSxxREFJUkMsRUFBRSxDQUFDLGFBQWFJLFVBQ2hCSixFQUFFLENBQUMsYUFBYSxNQUNoQkMsS0FBSyxDQUFDO1FBRVQsSUFBSUosT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0FBQ0YsRUFBQztBQUVELGtDQUFrQztBQUMzQixNQUFNdUksb0JBQW9CO0lBQy9CLHNCQUFzQjtJQUN0QixNQUFNQztRQUNKLE1BQU0sRUFBRXhJLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFFLG1FQUlSQyxFQUFFLENBQUMsYUFBYSxNQUNoQkMsS0FBSyxDQUFDO1FBRVQsSUFBSUosT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsOEJBQThCO0lBQzlCLE1BQU15SSwwQkFBeUJDLFVBQWtCO1FBQy9DLE1BQU0sRUFBRTFJLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFFLG1FQUlSQyxFQUFFLENBQUMsZUFBZXNJLFlBQ2xCdEksRUFBRSxDQUFDLGFBQWEsTUFDaEJDLEtBQUssQ0FBQztRQUVULElBQUlKLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtBQUNGLEVBQUM7QUFFRCxrQ0FBa0M7QUFDM0IsTUFBTTJJLG9CQUFvQjtJQUMvQixrQkFBa0I7SUFDbEIsTUFBTUM7UUFDSixNQUFNLEVBQUU1SSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLFdBQ0xDLE1BQU0sQ0FBRSxtSEFPUkMsRUFBRSxDQUFDLGFBQWEsTUFDaEJDLEtBQUssQ0FBQztRQUVULElBQUlKLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLHFCQUFxQjtJQUNyQixNQUFNNkksa0JBQWlCQyxRQUFnQjtRQUNyQyxNQUFNLEVBQUU5SSxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ00sSUFBSSxDQUFDLFdBQ0xDLE1BQU0sQ0FBRSxtSEFPUkMsRUFBRSxDQUFDLE1BQU0wSSxVQUNUbEgsTUFBTTtRQUVULElBQUkzQixPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTStJLHVCQUFzQkMsU0FBaUIsRUFBRUYsUUFBZ0IsRUFBRUcsZUFBdUIsRUFBRUMsU0FBaUI7UUFDekcsTUFBTUMsY0FBYyxTQUFvQixPQUFYL0csS0FBS2dILEdBQUc7UUFFckMsTUFBTSxFQUFFcEosSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyxzQkFDTHlDLE1BQU0sQ0FBQztZQUNOMEcsWUFBWUw7WUFDWk0sV0FBV1I7WUFDWFMsY0FBY0o7WUFDZEssa0JBQWtCUDtZQUNsQnZFLFFBQVE7WUFDUitFLFlBQVlQO1lBQ1pRLG9CQUFvQixJQUFJdEgsT0FBT0MsV0FBVztRQUM1QyxHQUNDbEMsTUFBTSxHQUNOeUIsTUFBTTtRQUVULElBQUkzQixPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7SUFFQSx5QkFBeUI7SUFDekIsTUFBTTJKLHNCQUFxQlgsU0FBa0IsRUFBRXRFLE1BQWU7UUFDNUQsSUFBSWpFLFFBQVFiLCtDQUFRQSxDQUNqQk0sSUFBSSxDQUFDLHNCQUNMQyxNQUFNLENBQUU7UUFRWCxJQUFJNkksV0FBVztZQUNidkksUUFBUUEsTUFBTUwsRUFBRSxDQUFDLGNBQWM0STtRQUNqQztRQUVBLElBQUl0RSxRQUFRO1lBQ1ZqRSxRQUFRQSxNQUFNTCxFQUFFLENBQUMsVUFBVXNFO1FBQzdCO1FBRUEsTUFBTSxFQUFFMUUsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUSxNQUFNSixLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFM0UsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU00SixtQkFBa0JDLE9BQWUsRUFBRW5GLE1BQWMsRUFBRW9GLE1BQWUsRUFBRUMsY0FBdUIsRUFBRUMsWUFBcUIsRUFBRUMsWUFBcUI7UUFDN0ksTUFBTXRDLFVBQWU7WUFDbkJqRDtZQUNBbEMsWUFBWSxJQUFJSixPQUFPQyxXQUFXO1FBQ3BDO1FBRUEsSUFBSXFDLFdBQVcsZUFBZTtZQUM1QmlELFFBQVF1QyxpQkFBaUIsR0FBRyxJQUFJOUgsT0FBT0MsV0FBVztRQUNwRCxPQUFPLElBQUlxQyxXQUFXLGFBQWE7WUFDakNpRCxRQUFRd0MsZUFBZSxHQUFHLElBQUkvSCxPQUFPQyxXQUFXO1lBQ2hEc0YsUUFBUXlDLFlBQVksR0FBR047WUFDdkIsSUFBSUMsZ0JBQWdCcEMsUUFBUTBDLGVBQWUsR0FBR047WUFDOUMsSUFBSUMsY0FBY3JDLFFBQVEyQyxhQUFhLEdBQUdOO1lBQzFDLElBQUlDLGNBQWN0QyxRQUFRNEMsYUFBYSxHQUFHTjtRQUM1QztRQUVBLE1BQU0sRUFBRWhLLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUM3Qk0sSUFBSSxDQUFDLHNCQUNMcUMsTUFBTSxDQUFDb0YsU0FDUHZILEVBQUUsQ0FBQyxNQUFNeUo7UUFFWixJQUFJNUosT0FBTyxNQUFNQTtRQUNqQixPQUFPO1lBQUVxRCxTQUFTO1FBQUs7SUFDekI7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTWtILHVCQUFzQlgsT0FBZSxFQUFFWSxlQUFzQjtRQUNqRSxNQUFNQyxlQUFlRCxnQkFBZ0I3RixHQUFHLENBQUMrRixDQUFBQSxRQUFVO2dCQUNqREMsVUFBVWY7Z0JBQ1ZoSCxlQUFlOEgsTUFBTTlILGFBQWE7Z0JBQ2xDMkcsa0JBQWtCbUIsTUFBTW5CLGdCQUFnQjtnQkFDeENhLGlCQUFpQk0sTUFBTU4sZUFBZTtnQkFDdENuSSxNQUFNeUksTUFBTXpJLElBQUk7Z0JBQ2hCMkksZUFBZUYsTUFBTUUsYUFBYTtnQkFDbENDLFlBQVlILE1BQU1OLGVBQWUsR0FBR00sTUFBTUUsYUFBYTtZQUN6RDtRQUVBLE1BQU0sRUFBRTVLLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUM3Qk0sSUFBSSxDQUFDLDBCQUNMeUMsTUFBTSxDQUFDK0g7UUFFVixJQUFJekssT0FBTyxNQUFNQTtRQUNqQixPQUFPO1lBQUVxRCxTQUFTO1FBQUs7SUFDekI7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTXlILDZCQUE0Qi9CLFNBQWlCLEVBQUVnQyxTQUFrQixFQUFFQyxPQUFnQjtRQUN2RixJQUFJeEssUUFBUWIsK0NBQVFBLENBQ2pCTSxJQUFJLENBQUMsc0JBQ0xDLE1BQU0sQ0FBRSxzREFJUkMsRUFBRSxDQUFDLGNBQWM0STtRQUVwQixJQUFJZ0MsV0FBVztZQUNidkssUUFBUUEsTUFBTXlLLEdBQUcsQ0FBQyxjQUFjRjtRQUNsQztRQUVBLElBQUlDLFNBQVM7WUFDWHhLLFFBQVFBLE1BQU0wSyxHQUFHLENBQUMsY0FBY0Y7UUFDbEM7UUFFQSxNQUFNLEVBQUVqTCxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1RLE1BQU1KLEtBQUssQ0FBQyxjQUFjO1lBQUVDLFdBQVc7UUFBTTtRQUUzRSxJQUFJTCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7QUFDRixFQUFDO0FBRUQsK0JBQStCO0FBQ3hCLE1BQU1vTCxpQkFBaUI7SUFDNUIsbUJBQW1CO0lBQ25CLE1BQU1DO1FBQ0osTUFBTSxFQUFFckwsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUUsc0hBTVJDLEVBQUUsQ0FBQyxhQUFhLE1BQ2hCQyxLQUFLLENBQUM7UUFFVCxJQUFJSixPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTXNMLG1CQUFrQnRDLFNBQWlCO1FBQ3ZDLE1BQU0sRUFBRWhKLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsWUFDTEMsTUFBTSxDQUFFLHNIQU1SQyxFQUFFLENBQUMsTUFBTTRJLFdBQ1RwSCxNQUFNO1FBRVQsSUFBSTNCLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtBQUNGLEVBQUM7QUFFRCw2QkFBNkI7QUFDdEIsTUFBTXVMLGVBQWU7SUFDMUIseUJBQXlCO0lBQ3pCLE1BQU1DLHNCQUFxQmhMLFFBQWlCLEVBQUV3SyxTQUFrQixFQUFFQyxPQUFnQjtRQUNoRixJQUFJeEssUUFBUWIsK0NBQVFBLENBQ2pCTSxJQUFJLENBQUMsc0JBQ0xDLE1BQU0sQ0FBRTtRQVVYLElBQUlLLFVBQVU7WUFDWkMsUUFBUUEsTUFBTUwsRUFBRSxDQUFDLGFBQWFJO1FBQ2hDO1FBRUEsSUFBSXdLLFdBQVc7WUFDYnZLLFFBQVFBLE1BQU15SyxHQUFHLENBQUMsY0FBY0Y7UUFDbEM7UUFFQSxJQUFJQyxTQUFTO1lBQ1h4SyxRQUFRQSxNQUFNMEssR0FBRyxDQUFDLGNBQWNGO1FBQ2xDO1FBRUEsTUFBTSxFQUFFakwsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNUSxNQUFNSixLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU07UUFFM0UsSUFBSUwsT0FBTyxNQUFNQTtRQUNqQixPQUFPRDtJQUNUO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU15TCxzQkFBcUJqTCxRQUFnQixFQUFFa0wsSUFBYTtRQUN4RCxNQUFNQyxhQUFhRCxRQUFRLElBQUl0SixPQUFPQyxXQUFXLEdBQUd1SixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFFakUsTUFBTSxFQUFFNUwsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyx5QkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxhQUFhSSxVQUNoQkosRUFBRSxDQUFDLFFBQVF1TCxZQUNYL0osTUFBTTtRQUVULElBQUkzQixTQUFTQSxNQUFNNEwsSUFBSSxLQUFLLFlBQVksTUFBTTVMO1FBQzlDLE9BQU9EO0lBQ1Q7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTThMLHdCQUF1QnRMLFFBQWdCLEVBQUV5RCxLQUFZLEVBQUU4SCxZQUFpQixFQUFFQyxXQUFnQixFQUFFQyxRQUFnQjtRQUNoSCxNQUFNQyxvQkFBb0IsT0FBa0IsT0FBWDlKLEtBQUtnSCxHQUFHO1FBQ3pDLE1BQU0rQyxjQUFjbEksTUFBTW1JLE1BQU0sQ0FBQyxDQUFDQyxLQUFLekwsT0FBU3lMLE1BQU96TCxLQUFLa0IsUUFBUSxHQUFHbEIsS0FBSzBMLFVBQVUsRUFBRztRQUV6RixxQkFBcUI7UUFDckIsTUFBTSxFQUFFdE0sTUFBTXVNLFdBQVcsRUFBRXRNLE9BQU91TSxnQkFBZ0IsRUFBRSxHQUFHLE1BQU01TSwrQ0FBUUEsQ0FDbEVNLElBQUksQ0FBQyxzQkFDTHlDLE1BQU0sQ0FBQztZQUNOOEosV0FBV2pNO1lBQ1hrTSxvQkFBb0JSO1lBQ3BCUyxlQUFlWixhQUFhN0ksSUFBSTtZQUNoQzBKLGdCQUFnQmIsYUFBYWMsS0FBSztZQUNsQ0MsY0FBY1g7WUFDZFksWUFBWWYsWUFBWWUsVUFBVSxJQUFJO1lBQ3RDQyxpQkFBaUJoQixZQUFZZ0IsZUFBZSxJQUFJO1lBQ2hEQyxnQkFBZ0JqQixZQUFZa0IsTUFBTTtZQUNsQ0MsZ0JBQWdCO1lBQ2hCQyxXQUFXbkI7UUFDYixHQUNDOUwsTUFBTSxHQUNOeUIsTUFBTTtRQUVULElBQUk0SyxrQkFBa0IsTUFBTUE7UUFFNUIsMkJBQTJCO1FBQzNCLE1BQU1hLG1CQUFtQnBKLE1BQU1XLEdBQUcsQ0FBQ2hFLENBQUFBLE9BQVM7Z0JBQzFDME0sZ0JBQWdCZixZQUFZekgsRUFBRTtnQkFDOUJ5SSxjQUFjM00sS0FBSzJNLFlBQVk7Z0JBQy9CekwsVUFBVWxCLEtBQUtrQixRQUFRO2dCQUN2QndLLFlBQVkxTCxLQUFLMEwsVUFBVTtnQkFDM0JrQixhQUFhNU0sS0FBS2tCLFFBQVEsR0FBR2xCLEtBQUswTCxVQUFVO2dCQUM1Q21CLHNCQUFzQjdNLEtBQUs2TSxvQkFBb0I7WUFDakQ7UUFFQSxNQUFNLEVBQUV4TixPQUFPK0UsVUFBVSxFQUFFLEdBQUcsTUFBTXBGLCtDQUFRQSxDQUN6Q00sSUFBSSxDQUFDLDJCQUNMeUMsTUFBTSxDQUFDMEs7UUFFVixJQUFJckksWUFBWSxNQUFNQTtRQUV0QixPQUFPdUg7SUFDVDtBQUNGLEVBQUM7QUFFRCw0QkFBNEI7QUFDckIsTUFBTW1CLGNBQWM7SUFDekIsaUJBQWlCO0lBQ2pCLE1BQU1DO1FBQ0osTUFBTSxFQUFFM04sSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDbkNNLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUUsbURBSVJDLEVBQUUsQ0FBQyxnQkFBZ0IsTUFDbkJDLEtBQUssQ0FBQyxZQUFZO1lBQUVDLFdBQVc7UUFBSyxHQUNwQ0QsS0FBSyxDQUFDLFFBQVE7WUFBRUMsV0FBVztRQUFLO1FBRW5DLElBQUlMLE9BQU8sTUFBTUE7UUFDakIsT0FBT0Q7SUFDVDtJQUVBLCtCQUErQjtJQUMvQiwwQ0FBMEM7SUFDMUMsMkNBQTJDO0lBQzNDLG1DQUFtQztJQUNuQyxnQkFBZ0I7SUFDaEIsV0FBVztJQUNYLDhCQUE4QjtJQUM5QixhQUFhO0lBQ2IsNEJBQTRCO0lBQzVCLFVBQVU7SUFDVixTQUFTO0lBQ1QsaUNBQWlDO0lBQ2pDLGdDQUFnQztJQUNoQyx3REFBd0Q7SUFFeEQsMkJBQTJCO0lBQzNCLGdCQUFnQjtJQUNoQixLQUFLO0lBQ0wsTUFBTTROLGVBQWNwTixRQUFnQjtRQUNsQyxNQUFNLEVBQUVSLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsdUJBQ0xDLE1BQU0sQ0FBRSw2R0FPUkMsRUFBRSxDQUFDLGFBQWFJLFVBQ2hCSixFQUFFLENBQUMsZ0JBQWdCLE1BQ25CQyxLQUFLLENBQUMsdUJBQXVCO1lBQUVDLFdBQVc7UUFBSztRQUVsRCxJQUFJTCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTTZOLDRCQUEyQnJOLFFBQWdCLEVBQUVzTixVQUFrQixFQUFFQyxXQUFvQjtRQUN6RixNQUFNLEVBQUU5TixLQUFLLEVBQUUsR0FBRyxNQUFNTCwrQ0FBUUEsQ0FDN0JNLElBQUksQ0FBQyx1QkFDTHFDLE1BQU0sQ0FBQztZQUFFeUwsY0FBY0Q7UUFBWSxHQUNuQzNOLEVBQUUsQ0FBQyxhQUFhSSxVQUNoQkosRUFBRSxDQUFDLGdCQUFnQjBOO1FBRXRCLElBQUk3TixPQUFPLE1BQU1BO1FBQ2pCLE9BQU87WUFBRXFELFNBQVM7UUFBSztJQUN6QjtBQUNGLEVBQUM7QUFFRCx1Q0FBdUM7QUFDdkMsb0NBQW9DO0FBQ3BDLHNDQUFzQztBQUN0Qyx5RkFBeUY7QUFDekYsK0ZBQStGO0FBQy9GLHNEQUFzRDtBQUV0RCx3QkFBd0I7QUFDeEIsb0VBQW9FO0FBQ3BFLG9DQUFvQztBQUNwQyw0REFBNEQ7QUFDNUQsbUNBQW1DO0FBQ25DLGtDQUFrQztBQUNsQyxnQ0FBZ0M7QUFFaEMsdUNBQXVDO0FBRXZDLDRCQUE0QjtBQUM1Qiw0RUFBNEU7QUFDNUUsMkJBQTJCO0FBQzNCLGtCQUFrQjtBQUNsQix5QkFBeUI7QUFDekIseUJBQXlCO0FBQ3pCLGdEQUFnRDtBQUNoRCxXQUFXO0FBQ1gsNEJBQTRCO0FBQzVCLG1CQUFtQjtBQUNuQixnQ0FBZ0M7QUFDaEMsMEJBQTBCO0FBQzFCLHVDQUF1QztBQUN2QyxVQUFVO0FBRVYsK0NBQStDO0FBRS9DLDJCQUEyQjtBQUMzQix1RkFBdUY7QUFDdkYsaURBQWlEO0FBQ2pELDhGQUE4RjtBQUM5RixpRUFBaUU7QUFDakUsOEVBQThFO0FBQzlFLFFBQVE7QUFDUix5REFBeUQ7QUFDekQsaURBQWlEO0FBQ2pELGVBQWU7QUFFZixlQUFlO0FBQ2Ysc0JBQXNCO0FBQ3RCLDJCQUEyQjtBQUMzQiw0QkFBNEI7QUFDNUIsd0JBQXdCO0FBQ3hCLHVCQUF1QjtBQUN2QixtQkFBbUI7QUFDbkIsc0JBQXNCO0FBQ3RCLFFBQVE7QUFDUixPQUFPO0FBRVAsNkJBQTZCO0FBQzdCLG1HQUFtRztBQUNuRywrRkFBK0Y7QUFDL0Ysc0RBQXNEO0FBRXRELDZDQUE2QztBQUM3Qyx5Q0FBeUM7QUFDekMsa0JBQWtCO0FBQ2xCLG9CQUFvQjtBQUNwQix1QkFBdUI7QUFDdkIsZ0RBQWdEO0FBQ2hELHNFQUFzRTtBQUN0RSxXQUFXO0FBQ1gsK0NBQStDO0FBQy9DLDhDQUE4QztBQUM5Qyw0Q0FBNEM7QUFFNUMsNkJBQTZCO0FBRTdCLGdDQUFnQztBQUNoQywwREFBMEQ7QUFDMUQsNkNBQTZDO0FBQzdDLDhCQUE4QjtBQUM5Qiw0QkFBNEI7QUFDNUIsNEJBQTRCO0FBQzVCLCtDQUErQztBQUMvQyw4QkFBOEI7QUFDOUIsNEJBQTRCO0FBQzVCLFlBQVk7QUFDWixVQUFVO0FBQ1YscURBQXFEO0FBQ3JELHVEQUF1RDtBQUN2RCxtQkFBbUI7QUFDbkIsYUFBYTtBQUViLHNDQUFzQztBQUN0QyxxRUFBcUU7QUFDckUseUJBQXlCO0FBQ3pCLE1BQU07QUFDTixJQUFJO0FBQ0osb0NBQW9DO0FBQzdCLE1BQU0ySyxtQkFBbUI7SUFDOUIsaUNBQWlDO0lBQ2pDLE1BQU1DLHNCQUFxQjFOLFFBQWdCLEVBQUV3SyxTQUFrQixFQUFFQyxPQUFnQjtRQUMvRSxNQUFNa0QsUUFBUW5ELGFBQWEsSUFBSTVJLEtBQUtBLEtBQUtnSCxHQUFHLEtBQUssS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNL0csV0FBVztRQUN0RixNQUFNK0wsTUFBTW5ELFdBQVcsSUFBSTdJLE9BQU9DLFdBQVc7UUFFN0MsaUJBQWlCO1FBQ2pCLE1BQU0sRUFBRXJDLE1BQU1xTyxTQUFTLEVBQUVwTyxPQUFPcU8sVUFBVSxFQUFFLEdBQUcsTUFBTTFPLCtDQUFRQSxDQUMxRE0sSUFBSSxDQUFDLHNCQUNMQyxNQUFNLENBQUMsNENBQ1BDLEVBQUUsQ0FBQyxhQUFhSSxVQUNoQjBLLEdBQUcsQ0FBQyxjQUFjaUQsT0FDbEJoRCxHQUFHLENBQUMsY0FBY2lEO1FBRXJCLElBQUlFLFlBQVksTUFBTUE7UUFFdEIseUNBQXlDO1FBQ3pDLE1BQU0sRUFBRXRPLE1BQU11TyxZQUFZLEVBQUV0TyxPQUFPdU8sY0FBYyxFQUFFLEdBQUcsTUFBTTVPLCtDQUFRQSxDQUNqRU0sSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsYUFBYUk7UUFFbkIsSUFBSWdPLGdCQUFnQixNQUFNQTtRQUUxQix5Q0FBeUM7UUFDekMsTUFBTUMsa0JBQWtCRixhQUFhM0osR0FBRyxDQUFDOEosQ0FBQUEsSUFBS0EsRUFBRTVKLEVBQUU7UUFDbEQsTUFBTSxFQUFFOUUsTUFBTTJPLGFBQWEsRUFBRTFPLE9BQU8wRixjQUFjLEVBQUUsR0FBRyxNQUFNL0YsK0NBQVFBLENBQ2xFTSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFFLDJHQUtSeU8sRUFBRSxDQUFDLGdCQUFnQkg7UUFFdEIsSUFBSTlJLGdCQUFnQixNQUFNQTtRQUUxQixvQkFBb0I7UUFDcEIsTUFBTWtKLGVBQWVSLFVBQVVqQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS3lDLE9BQVN6QyxNQUFNeUMsS0FBS2hDLFlBQVksRUFBRTtRQUM5RSxNQUFNaUMsb0JBQW9CVixVQUFVNUosTUFBTTtRQUMxQyxNQUFNdUsscUJBQXFCRCxvQkFBb0IsSUFBSUYsZUFBZUUsb0JBQW9CO1FBQ3RGLE1BQU1FLGlCQUFpQk4sY0FBY3ZDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLekw7Z0JBQ25CQTttQkFBN0J5TCxNQUFPekwsS0FBS0MsYUFBYSxHQUFJRCxDQUFBQSxFQUFBQSxtQkFBQUEsS0FBS3FCLFVBQVUsY0FBZnJCLHVDQUFBQSxpQkFBaUJpSyxhQUFhLEtBQUk7V0FBSztRQUV0RSxNQUFNbkssZ0JBQWdCaU8sY0FBY2hPLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FDekNBLEtBQUtDLGFBQWEsSUFBSUQsS0FBS0UsYUFBYSxFQUN4QzJELE1BQU07UUFFUixPQUFPO1lBQ0xvSztZQUNBRTtZQUNBQztZQUNBQztZQUNBdk87WUFDQTJOO1lBQ0FNO1FBQ0Y7SUFDRjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNTyxvQkFBbUIxTyxRQUFnQjtZQUFFcUQsUUFBQUEsaUVBQVEsSUFBSW1ILDBEQUFvQkM7UUFDekUsTUFBTWtELFFBQVFuRCxhQUFhLElBQUk1SSxLQUFLQSxLQUFLZ0gsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUssTUFBTS9HLFdBQVc7UUFDdEYsTUFBTStMLE1BQU1uRCxXQUFXLElBQUk3SSxPQUFPQyxXQUFXO1FBRTdDLE1BQU0sRUFBRXJDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsK0NBQVFBLENBQ25DTSxJQUFJLENBQUMsMkJBQ0xDLE1BQU0sQ0FBRSw2SkFNUkMsRUFBRSxDQUFDLGdDQUFnQ0ksVUFDbkMwSyxHQUFHLENBQUMsaUNBQWlDaUQsT0FDckNoRCxHQUFHLENBQUMsaUNBQWlDaUQ7UUFFeEMsSUFBSW5PLE9BQU8sTUFBTUE7UUFFakIseUJBQXlCO1FBQ3pCLE1BQU1rUCxZQUFZblAsS0FBS29NLE1BQU0sQ0FBQyxDQUFDZ0QsS0FBVXhPO1lBQ3ZDLE1BQU15TyxXQUFXek8sS0FBSzBPLFNBQVMsQ0FBQ3BNLElBQUk7WUFDcEMsSUFBSSxDQUFDa00sR0FBRyxDQUFDQyxTQUFTLEVBQUU7Z0JBQ2xCRCxHQUFHLENBQUNDLFNBQVMsR0FBRztvQkFDZG5NLE1BQU1tTTtvQkFDTkUsVUFBVTNPLEtBQUswTyxTQUFTLENBQUNDLFFBQVE7b0JBQ2pDQyxlQUFlO29CQUNmWCxjQUFjO2dCQUNoQjtZQUNGO1lBQ0FPLEdBQUcsQ0FBQ0MsU0FBUyxDQUFDRyxhQUFhLElBQUk1TyxLQUFLa0IsUUFBUTtZQUM1Q3NOLEdBQUcsQ0FBQ0MsU0FBUyxDQUFDUixZQUFZLElBQUlqTyxLQUFLNE0sV0FBVztZQUM5QyxPQUFPNEI7UUFDVCxHQUFHLENBQUM7UUFFSixPQUFPSyxPQUFPQyxNQUFNLENBQUNQLFdBQ2xCcE8sSUFBSSxDQUFDLENBQUNDLEdBQVFDLElBQVdBLEVBQUV1TyxhQUFhLEdBQUd4TyxFQUFFd08sYUFBYSxFQUMxREcsS0FBSyxDQUFDLEdBQUc5TDtJQUNkO0lBRUEsaUZBQWlGO0lBQ2pGLE1BQU0rTCx1QkFBc0JwUCxRQUFnQjtZQUFFcUQsUUFBQUEsaUVBQVEsSUFBSW1ILDBEQUFvQkM7UUFDNUUsTUFBTWtELFFBQVFuRCxhQUFhLElBQUk1SSxLQUFLQSxLQUFLZ0gsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUssTUFBTS9HLFdBQVc7UUFDdEYsTUFBTStMLE1BQU1uRCxXQUFXLElBQUk3SSxPQUFPQyxXQUFXO1FBRTdDLDJEQUEyRDtRQUMzRCxNQUFNLEVBQUVyQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1MLCtDQUFRQSxDQUNuQ2lRLEdBQUcsQ0FBQyx5QkFBeUI7WUFDNUJwRCxXQUFXak07WUFDWHNQLFlBQVkzQjtZQUNaNEIsVUFBVTNCO1lBQ1Y0QixZQUFZbk07UUFDZDtRQUVGLElBQUk1RCxPQUFPLE1BQU1BO1FBQ2pCLE9BQU9EO0lBQ1Q7QUFDRixFQUFDIiwic291cmNlcyI6WyJEOlxccGFkYW5naHViX3N1cGFiYXNlXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXHNyY1xcbGliXFxkYXRhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJy4vc3VwYWJhc2UnXG5cbi8vIEludmVudG9yeSBNYW5hZ2VtZW50IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGludmVudG9yeVNlcnZpY2UgPSB7XG4gIC8vIEdldCBhbGwgaW52ZW50b3J5IGl0ZW1zIGZvciBhIHdhcmVob3VzZVxuICBhc3luYyBnZXRXYXJlaG91c2VJbnZlbnRvcnkod2FyZWhvdXNlSWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBpbmdyZWRpZW50OmluZ3JlZGllbnRzKCopLFxuICAgICAgICB3YXJlaG91c2U6d2FyZWhvdXNlcygqKVxuICAgICAgYClcbiAgICAgIC5lcSgnd2FyZWhvdXNlX2lkJywgd2FyZWhvdXNlSWQpXG4gICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCBsb3cgc3RvY2sgaXRlbXMgYWNyb3NzIGFsbCB3YXJlaG91c2VzXG4gIC8vIGFzeW5jIGdldExvd1N0b2NrSXRlbXMoYnJhbmNoSWQ/OiBzdHJpbmcpIHtcbiAgLy8gICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAvLyAgICAgLnJwYygnZ2V0X2xvd19zdG9ja19pdGVtcycsIHsgYnJhbmNoX2lkOiBicmFuY2hJZCB9KVxuICBcbiAgLy8gICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gIC8vICAgcmV0dXJuIGRhdGFcbiAgLy8gfSxcbiAgYXN5bmMgZ2V0TG93U3RvY2tJdGVtcyhicmFuY2hJZD86IHN0cmluZykge1xuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBpbmdyZWRpZW50OmluZ3JlZGllbnRzKCopLFxuICAgICAgICB3YXJlaG91c2U6d2FyZWhvdXNlcygqKVxuICAgICAgYClcbiAgXG4gICAgaWYgKGJyYW5jaElkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCd3YXJlaG91c2VzLmJyYW5jaF9pZCcsIGJyYW5jaElkKVxuICAgIH1cbiAgXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgcXVlcnlcbiAgXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIFxuICAgIC8vIEZpbHRlciBsb3cgc3RvY2sgaXRlbXMgaW4gSmF2YVNjcmlwdFxuICAgIGNvbnN0IGxvd1N0b2NrSXRlbXMgPSBkYXRhPy5maWx0ZXIoaXRlbSA9PiBcbiAgICAgIGl0ZW0uY3VycmVudF9zdG9jayA8IGl0ZW0ubWluaW11bV9zdG9ja1xuICAgICkuc29ydCgoYSwgYikgPT4gYS5jdXJyZW50X3N0b2NrIC0gYi5jdXJyZW50X3N0b2NrKVxuICBcbiAgICByZXR1cm4gbG93U3RvY2tJdGVtc1xuICB9LFxuXG4gIC8vIGFzeW5jIGdldExvd1N0b2NrSXRlbXMoYnJhbmNoSWQ/OiBzdHJpbmcpIHtcbiAgLy8gICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAvLyAgICAgLmZyb20oJ2ludmVudG9yeScpXG4gIC8vICAgICAuc2VsZWN0KGBcbiAgLy8gICAgICAgKixcbiAgLy8gICAgICAgaW5ncmVkaWVudDppbmdyZWRpZW50cygqKSxcbiAgLy8gICAgICAgd2FyZWhvdXNlOndhcmVob3VzZXMoKilcbiAgLy8gICAgIGApXG4gIC8vICAgICAuZmlsdGVyKCdjdXJyZW50X3N0b2NrJywgJ2x0JywgMTApXG4gIFxuICAvLyAgIGlmIChicmFuY2hJZCkge1xuICAvLyAgICAgcXVlcnkgPSBxdWVyeS5lcSgnd2FyZWhvdXNlcy5icmFuY2hfaWQnLCBicmFuY2hJZClcbiAgLy8gICB9XG4gIFxuICAvLyAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdjdXJyZW50X3N0b2NrJywgeyBhc2NlbmRpbmc6IHRydWUgfSlcbiAgXG4gIC8vICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAvLyAgIHJldHVybiBkYXRhXG4gIC8vIH0sXG5cbiAgLy8gVXBkYXRlIHN0b2NrIGxldmVscyB3aXRoIGVuaGFuY2VkIHZhbGlkYXRpb24gYW5kIHRyYW5zYWN0aW9uIHNhZmV0eVxuICBhc3luYyB1cGRhdGVTdG9jayhpbnZlbnRvcnlJZDogc3RyaW5nLCBuZXdTdG9jazogbnVtYmVyLCBtb3ZlbWVudFR5cGU6IHN0cmluZywgbm90ZXM/OiBzdHJpbmcsIHBlcmZvcm1lZEJ5Pzogc3RyaW5nKSB7XG4gICAgLy8gSW5wdXQgdmFsaWRhdGlvblxuICAgIGlmICghaW52ZW50b3J5SWQgfHwgbmV3U3RvY2sgPCAwKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgaW5wdXQ6IGludmVudG9yeSBJRCBpcyByZXF1aXJlZCBhbmQgc3RvY2sgY2Fubm90IGJlIG5lZ2F0aXZlJylcbiAgICB9XG5cbiAgICBpZiAoIVsnaW4nLCAnb3V0JywgJ2FkanVzdG1lbnQnLCAnd2FzdGUnXS5pbmNsdWRlcyhtb3ZlbWVudFR5cGUpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgbW92ZW1lbnQgdHlwZScpXG4gICAgfVxuXG4gICAgLy8gRmV0Y2ggY3VycmVudCBpbnZlbnRvcnkgd2l0aCBkZXRhaWxlZCBpbmZvcm1hdGlvblxuICAgIGNvbnN0IHsgZGF0YTogaW52ZW50b3J5LCBlcnJvcjogZmV0Y2hFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdpbnZlbnRvcnknKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGluZ3JlZGllbnQ6aW5ncmVkaWVudHModW5pdCwgbmFtZSksXG4gICAgICAgIHdhcmVob3VzZTp3YXJlaG91c2VzKG5hbWUsIGNvZGUpXG4gICAgICBgKVxuICAgICAgLmVxKCdpZCcsIGludmVudG9yeUlkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZmV0Y2hFcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggaW52ZW50b3J5OiAke2ZldGNoRXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIGNvbnN0IHF1YW50aXR5ID0gbmV3U3RvY2sgLSBpbnZlbnRvcnkuY3VycmVudF9zdG9ja1xuICAgIGNvbnN0IGlzSW5jcmVhc2UgPSBxdWFudGl0eSA+IDBcblxuICAgIC8vIEJ1c2luZXNzIHJ1bGUgdmFsaWRhdGlvblxuICAgIGlmIChuZXdTdG9jayA+IGludmVudG9yeS5tYXhpbXVtX3N0b2NrICYmIGludmVudG9yeS5tYXhpbXVtX3N0b2NrID4gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBTdG9jayBjYW5ub3QgZXhjZWVkIG1heGltdW0gbGltaXQgb2YgJHtpbnZlbnRvcnkubWF4aW11bV9zdG9ja30gJHtpbnZlbnRvcnkuaW5ncmVkaWVudD8udW5pdH1gKVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRoaXMgd291bGQgY3JlYXRlIG5lZ2F0aXZlIHN0b2NrIGZvciBvdXRib3VuZCBtb3ZlbWVudHNcbiAgICBpZiAobW92ZW1lbnRUeXBlID09PSAnb3V0JyAmJiBuZXdTdG9jayA8IDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2Fubm90IHJlZHVjZSBzdG9jayBiZWxvdyB6ZXJvJylcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gVXNlIGEgdHJhbnNhY3Rpb24tbGlrZSBhcHByb2FjaCB3aXRoIGVycm9yIGhhbmRsaW5nXG4gICAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcblxuICAgICAgLy8gVXBkYXRlIGludmVudG9yeSByZWNvcmRcbiAgICAgIGNvbnN0IHsgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgLnVwZGF0ZSh7XG4gICAgICAgICAgY3VycmVudF9zdG9jazogbmV3U3RvY2ssXG4gICAgICAgICAgdXBkYXRlZF9hdDogdGltZXN0YW1wLFxuICAgICAgICAgIGxhc3RfcmVzdG9ja2VkX2F0OiBpc0luY3JlYXNlID8gdGltZXN0YW1wIDogaW52ZW50b3J5Lmxhc3RfcmVzdG9ja2VkX2F0XG4gICAgICAgIH0pXG4gICAgICAgIC5lcSgnaWQnLCBpbnZlbnRvcnlJZClcblxuICAgICAgaWYgKHVwZGF0ZUVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSBpbnZlbnRvcnk6ICR7dXBkYXRlRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICAvLyBSZWNvcmQgc3RvY2sgbW92ZW1lbnQgd2l0aCBlbmhhbmNlZCBkZXRhaWxzXG4gICAgICBjb25zdCB7IGVycm9yOiBtb3ZlbWVudEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc3RvY2tfbW92ZW1lbnRzJylcbiAgICAgICAgLmluc2VydCh7XG4gICAgICAgICAgd2FyZWhvdXNlX2lkOiBpbnZlbnRvcnkud2FyZWhvdXNlX2lkLFxuICAgICAgICAgIGluZ3JlZGllbnRfaWQ6IGludmVudG9yeS5pbmdyZWRpZW50X2lkLFxuICAgICAgICAgIG1vdmVtZW50X3R5cGU6IG1vdmVtZW50VHlwZSxcbiAgICAgICAgICBxdWFudGl0eTogTWF0aC5hYnMocXVhbnRpdHkpLFxuICAgICAgICAgIHVuaXQ6IGludmVudG9yeS5pbmdyZWRpZW50Py51bml0IHx8ICdrZycsXG4gICAgICAgICAgcmVmZXJlbmNlX3R5cGU6ICdtYW51YWxfYWRqdXN0bWVudCcsXG4gICAgICAgICAgbm90ZXM6IG5vdGVzIHx8IGBTdG9jayAke21vdmVtZW50VHlwZX0gLSAke2ludmVudG9yeS5pbmdyZWRpZW50Py5uYW1lfSBhdCAke2ludmVudG9yeS53YXJlaG91c2U/Lm5hbWV9YCxcbiAgICAgICAgICBwZXJmb3JtZWRfYnk6IHBlcmZvcm1lZEJ5LFxuICAgICAgICAgIGNyZWF0ZWRfYXQ6IHRpbWVzdGFtcFxuICAgICAgICB9KVxuXG4gICAgICBpZiAobW92ZW1lbnRFcnJvcikge1xuICAgICAgICAvLyBBdHRlbXB0IHRvIHJvbGxiYWNrIHRoZSBpbnZlbnRvcnkgdXBkYXRlXG4gICAgICAgIGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ2ludmVudG9yeScpXG4gICAgICAgICAgLnVwZGF0ZSh7XG4gICAgICAgICAgICBjdXJyZW50X3N0b2NrOiBpbnZlbnRvcnkuY3VycmVudF9zdG9jayxcbiAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IGludmVudG9yeS51cGRhdGVkX2F0XG4gICAgICAgICAgfSlcbiAgICAgICAgICAuZXEoJ2lkJywgaW52ZW50b3J5SWQpXG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gcmVjb3JkIHN0b2NrIG1vdmVtZW50OiAke21vdmVtZW50RXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBwcmV2aW91c1N0b2NrOiBpbnZlbnRvcnkuY3VycmVudF9zdG9jayxcbiAgICAgICAgbmV3U3RvY2s6IG5ld1N0b2NrLFxuICAgICAgICBxdWFudGl0eTogTWF0aC5hYnMocXVhbnRpdHkpLFxuICAgICAgICBtb3ZlbWVudFR5cGUsXG4gICAgICAgIHRpbWVzdGFtcFxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFN0b2NrIHVwZGF0ZSBmYWlsZWQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YClcbiAgICB9XG4gIH0sXG5cbiAgLy8gQ3JlYXRlIG5ldyBpbnZlbnRvcnkgcmVjb3JkXG4gIGFzeW5jIGNyZWF0ZUludmVudG9yeVJlY29yZCh3YXJlaG91c2VJZDogc3RyaW5nLCBpbmdyZWRpZW50SWQ6IHN0cmluZywgaW5pdGlhbFN0b2NrOiBudW1iZXIsIG5vdGVzPzogc3RyaW5nKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdpbnZlbnRvcnknKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIHdhcmVob3VzZV9pZDogd2FyZWhvdXNlSWQsXG4gICAgICAgIGluZ3JlZGllbnRfaWQ6IGluZ3JlZGllbnRJZCxcbiAgICAgICAgY3VycmVudF9zdG9jazogaW5pdGlhbFN0b2NrLFxuICAgICAgICBtaW5pbXVtX3N0b2NrOiAwLFxuICAgICAgICBtYXhpbXVtX3N0b2NrOiBpbml0aWFsU3RvY2sgKiAxMCwgLy8gRGVmYXVsdCB0byAxMHggaW5pdGlhbCBzdG9ja1xuICAgICAgICByZW9yZGVyX3BvaW50OiBpbml0aWFsU3RvY2sgKiAwLjIsIC8vIERlZmF1bHQgdG8gMjAlIG9mIGluaXRpYWwgc3RvY2tcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuXG4gICAgLy8gUmVjb3JkIGluaXRpYWwgc3RvY2sgbW92ZW1lbnRcbiAgICBjb25zdCB7IGRhdGE6IGluZ3JlZGllbnQgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW5ncmVkaWVudHMnKVxuICAgICAgLnNlbGVjdCgndW5pdCcpXG4gICAgICAuZXEoJ2lkJywgaW5ncmVkaWVudElkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3N0b2NrX21vdmVtZW50cycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgd2FyZWhvdXNlX2lkOiB3YXJlaG91c2VJZCxcbiAgICAgICAgaW5ncmVkaWVudF9pZDogaW5ncmVkaWVudElkLFxuICAgICAgICBtb3ZlbWVudF90eXBlOiAnaW4nLFxuICAgICAgICBxdWFudGl0eTogaW5pdGlhbFN0b2NrLFxuICAgICAgICB1bml0OiBpbmdyZWRpZW50Py51bml0IHx8ICdrZycsXG4gICAgICAgIHJlZmVyZW5jZV90eXBlOiAnaW5pdGlhbF9zdG9jaycsXG4gICAgICAgIG5vdGVzOiBub3RlcyB8fCAnSW5pdGlhbCBzdG9jayBlbnRyeSdcbiAgICAgIH0pXG5cbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCBzdG9jayBtb3ZlbWVudHMgZm9yIGEgd2FyZWhvdXNlXG4gIGFzeW5jIGdldFN0b2NrTW92ZW1lbnRzKHdhcmVob3VzZUlkOiBzdHJpbmcsIGxpbWl0ID0gNTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3N0b2NrX21vdmVtZW50cycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgaW5ncmVkaWVudDppbmdyZWRpZW50cygqKSxcbiAgICAgICAgd2FyZWhvdXNlOndhcmVob3VzZXMoKiksXG4gICAgICAgIHBlcmZvcm1lcjpwcm9maWxlcyhmdWxsX25hbWUpXG4gICAgICBgKVxuICAgICAgLmVxKCd3YXJlaG91c2VfaWQnLCB3YXJlaG91c2VJZClcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgLmxpbWl0KGxpbWl0KVxuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIHJldHVybiBkYXRhXG4gIH0sXG5cbiAgLy8gQ3JlYXRlIHdhcmVob3VzZSB0cmFuc2ZlclxuICBhc3luYyBjcmVhdGVUcmFuc2Zlcihmcm9tV2FyZWhvdXNlSWQ6IHN0cmluZywgdG9XYXJlaG91c2VJZDogc3RyaW5nLCBpdGVtczogYW55W10sIHJlcXVlc3RlZEJ5OiBzdHJpbmcsIG5vdGVzPzogc3RyaW5nKSB7XG4gICAgLy8gQ3JlYXRlIHRyYW5zZmVyIHJlY29yZFxuICAgIGNvbnN0IHsgZGF0YTogdHJhbnNmZXIsIGVycm9yOiB0cmFuc2ZlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3dhcmVob3VzZV90cmFuc2ZlcnMnKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIGZyb21fd2FyZWhvdXNlX2lkOiBmcm9tV2FyZWhvdXNlSWQsXG4gICAgICAgIHRvX3dhcmVob3VzZV9pZDogdG9XYXJlaG91c2VJZCxcbiAgICAgICAgcmVxdWVzdGVkX2J5OiByZXF1ZXN0ZWRCeSxcbiAgICAgICAgdG90YWxfaXRlbXM6IGl0ZW1zLmxlbmd0aCxcbiAgICAgICAgbm90ZXMsXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnXG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICh0cmFuc2ZlckVycm9yKSB0aHJvdyB0cmFuc2ZlckVycm9yXG5cbiAgICAvLyBDcmVhdGUgdHJhbnNmZXIgaXRlbXNcbiAgICBjb25zdCB0cmFuc2Zlckl0ZW1zID0gaXRlbXMubWFwKGl0ZW0gPT4gKHtcbiAgICAgIHRyYW5zZmVyX2lkOiB0cmFuc2Zlci5pZCxcbiAgICAgIGluZ3JlZGllbnRfaWQ6IGl0ZW0uaW5ncmVkaWVudF9pZCxcbiAgICAgIHJlcXVlc3RlZF9xdWFudGl0eTogaXRlbS5xdWFudGl0eSxcbiAgICAgIHVuaXQ6IGl0ZW0udW5pdCxcbiAgICAgIG5vdGVzOiBpdGVtLm5vdGVzXG4gICAgfSkpXG5cbiAgICBjb25zdCB7IGVycm9yOiBpdGVtc0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3RyYW5zZmVyX2l0ZW1zJylcbiAgICAgIC5pbnNlcnQodHJhbnNmZXJJdGVtcylcblxuICAgIGlmIChpdGVtc0Vycm9yKSB0aHJvdyBpdGVtc0Vycm9yXG5cbiAgICByZXR1cm4gdHJhbnNmZXJcbiAgfSxcblxuICAvLyBHZXQgcGVuZGluZyB0cmFuc2ZlcnNcbiAgYXN5bmMgZ2V0UGVuZGluZ1RyYW5zZmVycyh3YXJlaG91c2VJZD86IHN0cmluZykge1xuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnd2FyZWhvdXNlX3RyYW5zZmVycycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgZnJvbV93YXJlaG91c2U6d2FyZWhvdXNlcyFmcm9tX3dhcmVob3VzZV9pZCgqKSxcbiAgICAgICAgdG9fd2FyZWhvdXNlOndhcmVob3VzZXMhdG9fd2FyZWhvdXNlX2lkKCopLFxuICAgICAgICByZXF1ZXN0ZXI6cHJvZmlsZXMhcmVxdWVzdGVkX2J5KCopLFxuICAgICAgICB0cmFuc2Zlcl9pdGVtcygqLCBpbmdyZWRpZW50OmluZ3JlZGllbnRzKCopKVxuICAgICAgYClcbiAgICAgIC5lcSgnc3RhdHVzJywgJ3BlbmRpbmcnKVxuXG4gICAgaWYgKHdhcmVob3VzZUlkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yKGBmcm9tX3dhcmVob3VzZV9pZC5lcS4ke3dhcmVob3VzZUlkfSx0b193YXJlaG91c2VfaWQuZXEuJHt3YXJlaG91c2VJZH1gKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdyZXF1ZXN0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEFwcHJvdmUgdHJhbnNmZXIgd2l0aCBlbmhhbmNlZCB2YWxpZGF0aW9uXG4gIGFzeW5jIGFwcHJvdmVUcmFuc2Zlcih0cmFuc2ZlcklkOiBzdHJpbmcsIGFwcHJvdmVkQnk6IHN0cmluZywgYXBwcm92ZWRJdGVtczogYW55W10pIHtcbiAgICB0cnkge1xuICAgICAgLy8gVmFsaWRhdGUgdHJhbnNmZXIgZXhpc3RzIGFuZCBpcyBpbiBwZW5kaW5nIHN0YXR1c1xuICAgICAgY29uc3QgeyBkYXRhOiB0cmFuc2ZlciwgZXJyb3I6IGZldGNoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCd3YXJlaG91c2VfdHJhbnNmZXJzJylcbiAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgKixcbiAgICAgICAgICBmcm9tX3dhcmVob3VzZTp3YXJlaG91c2VzIWZyb21fd2FyZWhvdXNlX2lkKCopLFxuICAgICAgICAgIHRvX3dhcmVob3VzZTp3YXJlaG91c2VzIXRvX3dhcmVob3VzZV9pZCgqKSxcbiAgICAgICAgICB0cmFuc2Zlcl9pdGVtcygqLCBpbmdyZWRpZW50OmluZ3JlZGllbnRzKCopKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ2lkJywgdHJhbnNmZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChmZXRjaEVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVHJhbnNmZXIgbm90IGZvdW5kOiAke2ZldGNoRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAodHJhbnNmZXIuc3RhdHVzICE9PSAncGVuZGluZycpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgYXBwcm92ZSB0cmFuc2ZlciB3aXRoIHN0YXR1czogJHt0cmFuc2Zlci5zdGF0dXN9YClcbiAgICAgIH1cblxuICAgICAgLy8gVmFsaWRhdGUgc3RvY2sgYXZhaWxhYmlsaXR5IGZvciBhcHByb3ZlZCBxdWFudGl0aWVzXG4gICAgICBmb3IgKGNvbnN0IGFwcHJvdmVkSXRlbSBvZiBhcHByb3ZlZEl0ZW1zKSB7XG4gICAgICAgIGNvbnN0IHRyYW5zZmVySXRlbSA9IHRyYW5zZmVyLnRyYW5zZmVyX2l0ZW1zLmZpbmQoKGl0ZW06IGFueSkgPT4gaXRlbS5pZCA9PT0gYXBwcm92ZWRJdGVtLmlkKVxuICAgICAgICBpZiAoIXRyYW5zZmVySXRlbSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVHJhbnNmZXIgaXRlbSBub3QgZm91bmQ6ICR7YXBwcm92ZWRJdGVtLmlkfWApXG4gICAgICAgIH1cblxuICAgICAgICAvLyBDaGVjayBjdXJyZW50IHN0b2NrIGluIHNvdXJjZSB3YXJlaG91c2VcbiAgICAgICAgY29uc3QgeyBkYXRhOiBpbnZlbnRvcnksIGVycm9yOiBpbnZlbnRvcnlFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAuc2VsZWN0KCdjdXJyZW50X3N0b2NrJylcbiAgICAgICAgICAuZXEoJ3dhcmVob3VzZV9pZCcsIHRyYW5zZmVyLmZyb21fd2FyZWhvdXNlX2lkKVxuICAgICAgICAgIC5lcSgnaW5ncmVkaWVudF9pZCcsIHRyYW5zZmVySXRlbS5pbmdyZWRpZW50X2lkKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChpbnZlbnRvcnlFcnJvcikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQ2Fubm90IHZlcmlmeSBzdG9jayBmb3IgaW5ncmVkaWVudDogJHt0cmFuc2Zlckl0ZW0uaW5ncmVkaWVudC5uYW1lfWApXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoaW52ZW50b3J5LmN1cnJlbnRfc3RvY2sgPCBhcHByb3ZlZEl0ZW0uYXBwcm92ZWRfcXVhbnRpdHkpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEluc3VmZmljaWVudCBzdG9jayBmb3IgJHt0cmFuc2Zlckl0ZW0uaW5ncmVkaWVudC5uYW1lfS4gQXZhaWxhYmxlOiAke2ludmVudG9yeS5jdXJyZW50X3N0b2NrfSwgUmVxdWVzdGVkOiAke2FwcHJvdmVkSXRlbS5hcHByb3ZlZF9xdWFudGl0eX1gKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuXG4gICAgICAvLyBVcGRhdGUgdHJhbnNmZXIgc3RhdHVzXG4gICAgICBjb25zdCB7IGVycm9yOiB0cmFuc2ZlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnd2FyZWhvdXNlX3RyYW5zZmVycycpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIHN0YXR1czogJ2FwcHJvdmVkJyxcbiAgICAgICAgICBhcHByb3ZlZF9ieTogYXBwcm92ZWRCeSxcbiAgICAgICAgICBhcHByb3ZlZF9hdDogdGltZXN0YW1wXG4gICAgICAgIH0pXG4gICAgICAgIC5lcSgnaWQnLCB0cmFuc2ZlcklkKVxuXG4gICAgICBpZiAodHJhbnNmZXJFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBhcHByb3ZlIHRyYW5zZmVyOiAke3RyYW5zZmVyRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgYXBwcm92ZWQgcXVhbnRpdGllcyBmb3IgaXRlbXNcbiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBhcHByb3ZlZEl0ZW1zKSB7XG4gICAgICAgIGNvbnN0IHsgZXJyb3I6IGl0ZW1FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgndHJhbnNmZXJfaXRlbXMnKVxuICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgYXBwcm92ZWRfcXVhbnRpdHk6IGl0ZW0uYXBwcm92ZWRfcXVhbnRpdHlcbiAgICAgICAgICB9KVxuICAgICAgICAgIC5lcSgnaWQnLCBpdGVtLmlkKVxuXG4gICAgICAgIGlmIChpdGVtRXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgYXBwcm92ZWQgcXVhbnRpdHk6ICR7aXRlbUVycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB0cmFuc2ZlcklkLFxuICAgICAgICBhcHByb3ZlZEF0OiB0aW1lc3RhbXAsXG4gICAgICAgIGFwcHJvdmVkSXRlbXM6IGFwcHJvdmVkSXRlbXMubGVuZ3RoXG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUcmFuc2ZlciBhcHByb3ZhbCBmYWlsZWQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YClcbiAgICB9XG4gIH0sXG5cbiAgLy8gQ29tcGxldGUgdHJhbnNmZXIgYW5kIHVwZGF0ZSBpbnZlbnRvcnkgbGV2ZWxzXG4gIGFzeW5jIGNvbXBsZXRlVHJhbnNmZXIodHJhbnNmZXJJZDogc3RyaW5nLCBjb21wbGV0ZWRCeTogc3RyaW5nKSB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZldGNoIHRyYW5zZmVyIHdpdGggYWxsIGRldGFpbHNcbiAgICAgIGNvbnN0IHsgZGF0YTogdHJhbnNmZXIsIGVycm9yOiBmZXRjaEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnd2FyZWhvdXNlX3RyYW5zZmVycycpXG4gICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICosXG4gICAgICAgICAgZnJvbV93YXJlaG91c2U6d2FyZWhvdXNlcyFmcm9tX3dhcmVob3VzZV9pZCgqKSxcbiAgICAgICAgICB0b193YXJlaG91c2U6d2FyZWhvdXNlcyF0b193YXJlaG91c2VfaWQoKiksXG4gICAgICAgICAgdHJhbnNmZXJfaXRlbXMoKiwgaW5ncmVkaWVudDppbmdyZWRpZW50cygqKSlcbiAgICAgICAgYClcbiAgICAgICAgLmVxKCdpZCcsIHRyYW5zZmVySWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAoZmV0Y2hFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRyYW5zZmVyIG5vdCBmb3VuZDogJHtmZXRjaEVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgaWYgKHRyYW5zZmVyLnN0YXR1cyAhPT0gJ2FwcHJvdmVkJyAmJiB0cmFuc2Zlci5zdGF0dXMgIT09ICdpbl90cmFuc2l0Jykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBjb21wbGV0ZSB0cmFuc2ZlciB3aXRoIHN0YXR1czogJHt0cmFuc2Zlci5zdGF0dXN9YClcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICBjb25zdCBzdG9ja01vdmVtZW50czogYW55W10gPSBbXVxuXG4gICAgICAvLyBQcm9jZXNzIGVhY2ggdHJhbnNmZXIgaXRlbVxuICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRyYW5zZmVyLnRyYW5zZmVyX2l0ZW1zKSB7XG4gICAgICAgIGNvbnN0IGFwcHJvdmVkUXR5ID0gaXRlbS5hcHByb3ZlZF9xdWFudGl0eSB8fCBpdGVtLnJlcXVlc3RlZF9xdWFudGl0eVxuXG4gICAgICAgIC8vIFJlZHVjZSBzdG9jayBmcm9tIHNvdXJjZSB3YXJlaG91c2VcbiAgICAgICAgY29uc3QgeyBkYXRhOiBzb3VyY2VJbnZlbnRvcnksIGVycm9yOiBzb3VyY2VFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgICAuZXEoJ3dhcmVob3VzZV9pZCcsIHRyYW5zZmVyLmZyb21fd2FyZWhvdXNlX2lkKVxuICAgICAgICAgIC5lcSgnaW5ncmVkaWVudF9pZCcsIGl0ZW0uaW5ncmVkaWVudF9pZClcbiAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICBpZiAoc291cmNlRXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFNvdXJjZSBpbnZlbnRvcnkgbm90IGZvdW5kIGZvciAke2l0ZW0uaW5ncmVkaWVudC5uYW1lfWApXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoc291cmNlSW52ZW50b3J5LmN1cnJlbnRfc3RvY2sgPCBhcHByb3ZlZFF0eSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW5zdWZmaWNpZW50IHN0b2NrIGZvciAke2l0ZW0uaW5ncmVkaWVudC5uYW1lfS4gQXZhaWxhYmxlOiAke3NvdXJjZUludmVudG9yeS5jdXJyZW50X3N0b2NrfSwgUmVxdWlyZWQ6ICR7YXBwcm92ZWRRdHl9YClcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFVwZGF0ZSBzb3VyY2UgaW52ZW50b3J5XG4gICAgICAgIGNvbnN0IHsgZXJyb3I6IHNvdXJjZVVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCdpbnZlbnRvcnknKVxuICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgY3VycmVudF9zdG9jazogc291cmNlSW52ZW50b3J5LmN1cnJlbnRfc3RvY2sgLSBhcHByb3ZlZFF0eSxcbiAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IHRpbWVzdGFtcFxuICAgICAgICAgIH0pXG4gICAgICAgICAgLmVxKCdpZCcsIHNvdXJjZUludmVudG9yeS5pZClcblxuICAgICAgICBpZiAoc291cmNlVXBkYXRlRXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgc291cmNlIGludmVudG9yeTogJHtzb3VyY2VVcGRhdGVFcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGQgc3RvY2sgbW92ZW1lbnQgZm9yIHNvdXJjZSAob3V0Ym91bmQpXG4gICAgICAgIHN0b2NrTW92ZW1lbnRzLnB1c2goe1xuICAgICAgICAgIHdhcmVob3VzZV9pZDogdHJhbnNmZXIuZnJvbV93YXJlaG91c2VfaWQsXG4gICAgICAgICAgaW5ncmVkaWVudF9pZDogaXRlbS5pbmdyZWRpZW50X2lkLFxuICAgICAgICAgIG1vdmVtZW50X3R5cGU6ICd0cmFuc2ZlcicsXG4gICAgICAgICAgcXVhbnRpdHk6IGFwcHJvdmVkUXR5LFxuICAgICAgICAgIHVuaXQ6IGl0ZW0udW5pdCxcbiAgICAgICAgICByZWZlcmVuY2VfdHlwZTogJ3RyYW5zZmVyX291dCcsXG4gICAgICAgICAgcmVmZXJlbmNlX2lkOiB0cmFuc2ZlcklkLFxuICAgICAgICAgIG5vdGVzOiBgVHJhbnNmZXIgb3V0IHRvICR7dHJhbnNmZXIudG9fd2FyZWhvdXNlLm5hbWV9IC0gJHtpdGVtLmluZ3JlZGllbnQubmFtZX1gLFxuICAgICAgICAgIHBlcmZvcm1lZF9ieTogY29tcGxldGVkQnksXG4gICAgICAgICAgY3JlYXRlZF9hdDogdGltZXN0YW1wXG4gICAgICAgIH0pXG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgZGVzdGluYXRpb24gaW52ZW50b3J5IGV4aXN0c1xuICAgICAgICBjb25zdCB7IGRhdGE6IGRlc3RJbnZlbnRvcnksIGVycm9yOiBkZXN0RmV0Y2hFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgICAuZXEoJ3dhcmVob3VzZV9pZCcsIHRyYW5zZmVyLnRvX3dhcmVob3VzZV9pZClcbiAgICAgICAgICAuZXEoJ2luZ3JlZGllbnRfaWQnLCBpdGVtLmluZ3JlZGllbnRfaWQpXG4gICAgICAgICAgLm1heWJlU2luZ2xlKClcblxuICAgICAgICBpZiAoZGVzdEZldGNoRXJyb3IpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm9yIGNoZWNraW5nIGRlc3RpbmF0aW9uIGludmVudG9yeTogJHtkZXN0RmV0Y2hFcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoZGVzdEludmVudG9yeSkge1xuICAgICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBkZXN0aW5hdGlvbiBpbnZlbnRvcnlcbiAgICAgICAgICBjb25zdCB7IGVycm9yOiBkZXN0VXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgICBjdXJyZW50X3N0b2NrOiBkZXN0SW52ZW50b3J5LmN1cnJlbnRfc3RvY2sgKyBhcHByb3ZlZFF0eSxcbiAgICAgICAgICAgICAgdXBkYXRlZF9hdDogdGltZXN0YW1wLFxuICAgICAgICAgICAgICBsYXN0X3Jlc3RvY2tlZF9hdDogdGltZXN0YW1wXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgLmVxKCdpZCcsIGRlc3RJbnZlbnRvcnkuaWQpXG5cbiAgICAgICAgICBpZiAoZGVzdFVwZGF0ZUVycm9yKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cGRhdGUgZGVzdGluYXRpb24gaW52ZW50b3J5OiAke2Rlc3RVcGRhdGVFcnJvci5tZXNzYWdlfWApXG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIENyZWF0ZSBuZXcgZGVzdGluYXRpb24gaW52ZW50b3J5IHJlY29yZFxuICAgICAgICAgIGNvbnN0IHsgZXJyb3I6IGRlc3RDcmVhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKCdpbnZlbnRvcnknKVxuICAgICAgICAgICAgLmluc2VydCh7XG4gICAgICAgICAgICAgIHdhcmVob3VzZV9pZDogdHJhbnNmZXIudG9fd2FyZWhvdXNlX2lkLFxuICAgICAgICAgICAgICBpbmdyZWRpZW50X2lkOiBpdGVtLmluZ3JlZGllbnRfaWQsXG4gICAgICAgICAgICAgIGN1cnJlbnRfc3RvY2s6IGFwcHJvdmVkUXR5LFxuICAgICAgICAgICAgICBtaW5pbXVtX3N0b2NrOiAwLFxuICAgICAgICAgICAgICBtYXhpbXVtX3N0b2NrOiBhcHByb3ZlZFF0eSAqIDEwLFxuICAgICAgICAgICAgICByZW9yZGVyX3BvaW50OiBhcHByb3ZlZFF0eSAqIDAuMixcbiAgICAgICAgICAgICAgbGFzdF9yZXN0b2NrZWRfYXQ6IHRpbWVzdGFtcFxuICAgICAgICAgICAgfSlcblxuICAgICAgICAgIGlmIChkZXN0Q3JlYXRlRXJyb3IpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBkZXN0aW5hdGlvbiBpbnZlbnRvcnk6ICR7ZGVzdENyZWF0ZUVycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGQgc3RvY2sgbW92ZW1lbnQgZm9yIGRlc3RpbmF0aW9uIChpbmJvdW5kKVxuICAgICAgICBzdG9ja01vdmVtZW50cy5wdXNoKHtcbiAgICAgICAgICB3YXJlaG91c2VfaWQ6IHRyYW5zZmVyLnRvX3dhcmVob3VzZV9pZCxcbiAgICAgICAgICBpbmdyZWRpZW50X2lkOiBpdGVtLmluZ3JlZGllbnRfaWQsXG4gICAgICAgICAgbW92ZW1lbnRfdHlwZTogJ3RyYW5zZmVyJyxcbiAgICAgICAgICBxdWFudGl0eTogYXBwcm92ZWRRdHksXG4gICAgICAgICAgdW5pdDogaXRlbS51bml0LFxuICAgICAgICAgIHJlZmVyZW5jZV90eXBlOiAndHJhbnNmZXJfaW4nLFxuICAgICAgICAgIHJlZmVyZW5jZV9pZDogdHJhbnNmZXJJZCxcbiAgICAgICAgICBub3RlczogYFRyYW5zZmVyIGluIGZyb20gJHt0cmFuc2Zlci5mcm9tX3dhcmVob3VzZS5uYW1lfSAtICR7aXRlbS5pbmdyZWRpZW50Lm5hbWV9YCxcbiAgICAgICAgICBwZXJmb3JtZWRfYnk6IGNvbXBsZXRlZEJ5LFxuICAgICAgICAgIGNyZWF0ZWRfYXQ6IHRpbWVzdGFtcFxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICAvLyBJbnNlcnQgYWxsIHN0b2NrIG1vdmVtZW50c1xuICAgICAgY29uc3QgeyBlcnJvcjogbW92ZW1lbnRFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3N0b2NrX21vdmVtZW50cycpXG4gICAgICAgIC5pbnNlcnQoc3RvY2tNb3ZlbWVudHMpXG5cbiAgICAgIGlmIChtb3ZlbWVudEVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHJlY29yZCBzdG9jayBtb3ZlbWVudHM6ICR7bW92ZW1lbnRFcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSB0cmFuc2ZlciBzdGF0dXMgdG8gY29tcGxldGVkXG4gICAgICBjb25zdCB7IGVycm9yOiBjb21wbGV0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnd2FyZWhvdXNlX3RyYW5zZmVycycpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgY29tcGxldGVkX2F0OiB0aW1lc3RhbXBcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCdpZCcsIHRyYW5zZmVySWQpXG5cbiAgICAgIGlmIChjb21wbGV0ZUVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNvbXBsZXRlIHRyYW5zZmVyOiAke2NvbXBsZXRlRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB0cmFuc2ZlcklkLFxuICAgICAgICBjb21wbGV0ZWRBdDogdGltZXN0YW1wLFxuICAgICAgICBpdGVtc1RyYW5zZmVycmVkOiB0cmFuc2Zlci50cmFuc2Zlcl9pdGVtcy5sZW5ndGgsXG4gICAgICAgIHN0b2NrTW92ZW1lbnRzOiBzdG9ja01vdmVtZW50cy5sZW5ndGhcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRyYW5zZmVyIGNvbXBsZXRpb24gZmFpbGVkOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApXG4gICAgfVxuICB9LFxuXG4gIC8vIENhbmNlbCB0cmFuc2ZlclxuICBhc3luYyBjYW5jZWxUcmFuc2Zlcih0cmFuc2ZlcklkOiBzdHJpbmcsIGNhbmNlbGxlZEJ5OiBzdHJpbmcsIHJlYXNvbj86IHN0cmluZykge1xuICAgIHRyeSB7XG4gICAgICAvLyBWYWxpZGF0ZSB0cmFuc2ZlciBleGlzdHMgYW5kIGNhbiBiZSBjYW5jZWxsZWRcbiAgICAgIGNvbnN0IHsgZGF0YTogdHJhbnNmZXIsIGVycm9yOiBmZXRjaEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnd2FyZWhvdXNlX3RyYW5zZmVycycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ2lkJywgdHJhbnNmZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChmZXRjaEVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVHJhbnNmZXIgbm90IGZvdW5kOiAke2ZldGNoRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBpZiAoIVsncGVuZGluZycsICdhcHByb3ZlZCcsICdpbl90cmFuc2l0J10uaW5jbHVkZXModHJhbnNmZXIuc3RhdHVzKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBjYW5jZWwgdHJhbnNmZXIgd2l0aCBzdGF0dXM6ICR7dHJhbnNmZXIuc3RhdHVzfWApXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuXG4gICAgICAvLyBVcGRhdGUgdHJhbnNmZXIgc3RhdHVzXG4gICAgICBjb25zdCB7IGVycm9yOiBjYW5jZWxFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3dhcmVob3VzZV90cmFuc2ZlcnMnKVxuICAgICAgICAudXBkYXRlKHtcbiAgICAgICAgICBzdGF0dXM6ICdjYW5jZWxsZWQnLFxuICAgICAgICAgIG5vdGVzOiB0cmFuc2Zlci5ub3RlcyA/IGAke3RyYW5zZmVyLm5vdGVzfVxcblxcbkNhbmNlbGxlZDogJHtyZWFzb24gfHwgJ05vIHJlYXNvbiBwcm92aWRlZCd9YCA6IGBDYW5jZWxsZWQ6ICR7cmVhc29uIHx8ICdObyByZWFzb24gcHJvdmlkZWQnfWAsXG4gICAgICAgICAgY29tcGxldGVkX2F0OiB0aW1lc3RhbXBcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCdpZCcsIHRyYW5zZmVySWQpXG5cbiAgICAgIGlmIChjYW5jZWxFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjYW5jZWwgdHJhbnNmZXI6ICR7Y2FuY2VsRXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB0cmFuc2ZlcklkLFxuICAgICAgICBjYW5jZWxsZWRBdDogdGltZXN0YW1wLFxuICAgICAgICByZWFzb246IHJlYXNvbiB8fCAnTm8gcmVhc29uIHByb3ZpZGVkJ1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVHJhbnNmZXIgY2FuY2VsbGF0aW9uIGZhaWxlZDogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ31gKVxuICAgIH1cbiAgfSxcblxuICAvLyBCdWxrIHVwZGF0ZSBzdG9jayBsZXZlbHMgZm9yIG11bHRpcGxlIGl0ZW1zXG4gIGFzeW5jIGJ1bGtVcGRhdGVTdG9jayh1cGRhdGVzOiBBcnJheTx7XG4gICAgaW52ZW50b3J5SWQ6IHN0cmluZztcbiAgICBuZXdTdG9jazogbnVtYmVyO1xuICAgIG1vdmVtZW50VHlwZTogc3RyaW5nO1xuICAgIG5vdGVzPzogc3RyaW5nO1xuICB9PiwgcGVyZm9ybWVkQnk/OiBzdHJpbmcpIHtcbiAgICBpZiAoIXVwZGF0ZXMgfHwgdXBkYXRlcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTm8gdXBkYXRlcyBwcm92aWRlZCcpXG4gICAgfVxuXG4gICAgY29uc3QgcmVzdWx0czogQXJyYXk8e1xuICAgICAgaW52ZW50b3J5SWQ6IHN0cmluZztcbiAgICAgIHN1Y2Nlc3M6IGJvb2xlYW47XG4gICAgICBlcnJvcj86IHN0cmluZztcbiAgICAgIHByZXZpb3VzU3RvY2s/OiBudW1iZXI7XG4gICAgICBuZXdTdG9jaz86IG51bWJlcjtcbiAgICB9PiA9IFtdXG5cbiAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICBjb25zdCBzdG9ja01vdmVtZW50czogYW55W10gPSBbXVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByb2Nlc3MgZWFjaCB1cGRhdGVcbiAgICAgIGZvciAoY29uc3QgdXBkYXRlIG9mIHVwZGF0ZXMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyBWYWxpZGF0ZSBpbnB1dFxuICAgICAgICAgIGlmICghdXBkYXRlLmludmVudG9yeUlkIHx8IHVwZGF0ZS5uZXdTdG9jayA8IDApIHtcbiAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7XG4gICAgICAgICAgICAgIGludmVudG9yeUlkOiB1cGRhdGUuaW52ZW50b3J5SWQsXG4gICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICBlcnJvcjogJ0ludmFsaWQgaW5wdXQ6IGludmVudG9yeSBJRCBpcyByZXF1aXJlZCBhbmQgc3RvY2sgY2Fubm90IGJlIG5lZ2F0aXZlJ1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gRmV0Y2ggY3VycmVudCBpbnZlbnRvcnlcbiAgICAgICAgICBjb25zdCB7IGRhdGE6IGludmVudG9yeSwgZXJyb3I6IGZldGNoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICAgICAqLFxuICAgICAgICAgICAgICBpbmdyZWRpZW50OmluZ3JlZGllbnRzKHVuaXQsIG5hbWUpLFxuICAgICAgICAgICAgICB3YXJlaG91c2U6d2FyZWhvdXNlcyhuYW1lLCBjb2RlKVxuICAgICAgICAgICAgYClcbiAgICAgICAgICAgIC5lcSgnaWQnLCB1cGRhdGUuaW52ZW50b3J5SWQpXG4gICAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICAgIGlmIChmZXRjaEVycm9yKSB7XG4gICAgICAgICAgICByZXN1bHRzLnB1c2goe1xuICAgICAgICAgICAgICBpbnZlbnRvcnlJZDogdXBkYXRlLmludmVudG9yeUlkLFxuICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6IGBGYWlsZWQgdG8gZmV0Y2ggaW52ZW50b3J5OiAke2ZldGNoRXJyb3IubWVzc2FnZX1gXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCBxdWFudGl0eSA9IHVwZGF0ZS5uZXdTdG9jayAtIGludmVudG9yeS5jdXJyZW50X3N0b2NrXG5cbiAgICAgICAgICAvLyBCdXNpbmVzcyBydWxlIHZhbGlkYXRpb25cbiAgICAgICAgICBpZiAodXBkYXRlLm5ld1N0b2NrID4gaW52ZW50b3J5Lm1heGltdW1fc3RvY2sgJiYgaW52ZW50b3J5Lm1heGltdW1fc3RvY2sgPiAwKSB7XG4gICAgICAgICAgICByZXN1bHRzLnB1c2goe1xuICAgICAgICAgICAgICBpbnZlbnRvcnlJZDogdXBkYXRlLmludmVudG9yeUlkLFxuICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6IGBTdG9jayBjYW5ub3QgZXhjZWVkIG1heGltdW0gbGltaXQgb2YgJHtpbnZlbnRvcnkubWF4aW11bV9zdG9ja30gJHtpbnZlbnRvcnkuaW5ncmVkaWVudD8udW5pdH1gXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBVcGRhdGUgaW52ZW50b3J5XG4gICAgICAgICAgY29uc3QgeyBlcnJvcjogdXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgICAuZnJvbSgnaW52ZW50b3J5JylcbiAgICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgICBjdXJyZW50X3N0b2NrOiB1cGRhdGUubmV3U3RvY2ssXG4gICAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IHRpbWVzdGFtcCxcbiAgICAgICAgICAgICAgbGFzdF9yZXN0b2NrZWRfYXQ6IHF1YW50aXR5ID4gMCA/IHRpbWVzdGFtcCA6IGludmVudG9yeS5sYXN0X3Jlc3RvY2tlZF9hdFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5lcSgnaWQnLCB1cGRhdGUuaW52ZW50b3J5SWQpXG5cbiAgICAgICAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7XG4gICAgICAgICAgICAgIGludmVudG9yeUlkOiB1cGRhdGUuaW52ZW50b3J5SWQsXG4gICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgICBlcnJvcjogYEZhaWxlZCB0byB1cGRhdGUgaW52ZW50b3J5OiAke3VwZGF0ZUVycm9yLm1lc3NhZ2V9YFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gUHJlcGFyZSBzdG9jayBtb3ZlbWVudCByZWNvcmRcbiAgICAgICAgICBzdG9ja01vdmVtZW50cy5wdXNoKHtcbiAgICAgICAgICAgIHdhcmVob3VzZV9pZDogaW52ZW50b3J5LndhcmVob3VzZV9pZCxcbiAgICAgICAgICAgIGluZ3JlZGllbnRfaWQ6IGludmVudG9yeS5pbmdyZWRpZW50X2lkLFxuICAgICAgICAgICAgbW92ZW1lbnRfdHlwZTogdXBkYXRlLm1vdmVtZW50VHlwZSxcbiAgICAgICAgICAgIHF1YW50aXR5OiBNYXRoLmFicyhxdWFudGl0eSksXG4gICAgICAgICAgICB1bml0OiBpbnZlbnRvcnkuaW5ncmVkaWVudD8udW5pdCB8fCAna2cnLFxuICAgICAgICAgICAgcmVmZXJlbmNlX3R5cGU6ICdidWxrX2FkanVzdG1lbnQnLFxuICAgICAgICAgICAgbm90ZXM6IHVwZGF0ZS5ub3RlcyB8fCBgQnVsayBzdG9jayAke3VwZGF0ZS5tb3ZlbWVudFR5cGV9IC0gJHtpbnZlbnRvcnkuaW5ncmVkaWVudD8ubmFtZX1gLFxuICAgICAgICAgICAgcGVyZm9ybWVkX2J5OiBwZXJmb3JtZWRCeSxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6IHRpbWVzdGFtcFxuICAgICAgICAgIH0pXG5cbiAgICAgICAgICByZXN1bHRzLnB1c2goe1xuICAgICAgICAgICAgaW52ZW50b3J5SWQ6IHVwZGF0ZS5pbnZlbnRvcnlJZCxcbiAgICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgICBwcmV2aW91c1N0b2NrOiBpbnZlbnRvcnkuY3VycmVudF9zdG9jayxcbiAgICAgICAgICAgIG5ld1N0b2NrOiB1cGRhdGUubmV3U3RvY2tcbiAgICAgICAgICB9KVxuXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgcmVzdWx0cy5wdXNoKHtcbiAgICAgICAgICAgIGludmVudG9yeUlkOiB1cGRhdGUuaW52ZW50b3J5SWQsXG4gICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ1xuICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gSW5zZXJ0IGFsbCBzdG9jayBtb3ZlbWVudHMgaW4gYmF0Y2hcbiAgICAgIGlmIChzdG9ja01vdmVtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IHsgZXJyb3I6IG1vdmVtZW50RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ3N0b2NrX21vdmVtZW50cycpXG4gICAgICAgICAgLmluc2VydChzdG9ja01vdmVtZW50cylcblxuICAgICAgICBpZiAobW92ZW1lbnRFcnJvcikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHJlY29yZCBzdG9jayBtb3ZlbWVudHM6ICR7bW92ZW1lbnRFcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3Qgc3VjY2Vzc0NvdW50ID0gcmVzdWx0cy5maWx0ZXIociA9PiByLnN1Y2Nlc3MpLmxlbmd0aFxuICAgICAgY29uc3QgZmFpbHVyZUNvdW50ID0gcmVzdWx0cy5maWx0ZXIociA9PiAhci5zdWNjZXNzKS5sZW5ndGhcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFpbHVyZUNvdW50ID09PSAwLFxuICAgICAgICB0b3RhbFVwZGF0ZXM6IHVwZGF0ZXMubGVuZ3RoLFxuICAgICAgICBzdWNjZXNzQ291bnQsXG4gICAgICAgIGZhaWx1cmVDb3VudCxcbiAgICAgICAgcmVzdWx0cyxcbiAgICAgICAgdGltZXN0YW1wXG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBCdWxrIHVwZGF0ZSBmYWlsZWQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YClcbiAgICB9XG4gIH1cbn1cblxuLy8gQnJhbmNoIE1hbmFnZW1lbnQgRnVuY3Rpb25zXG4vLyBleHBvcnQgY29uc3QgYnJhbmNoU2VydmljZSA9IHtcbi8vICAgLy8gR2V0IGFsbCBicmFuY2hlc1xuLy8gICBhc3luYyBnZXRBbGxCcmFuY2hlcygpIHtcbi8vICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuLy8gICAgICAgLmZyb20oJ2JyYW5jaGVzJylcbi8vICAgICAgIC5zZWxlY3QoYFxuLy8gICAgICAgICAqLFxuLy8gICAgICAgICBtYW5hZ2VyOnByb2ZpbGVzKCopXG4vLyAgICAgICBgKVxuLy8gICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuLy8gICAgICAgLm9yZGVyKCduYW1lJylcblxuLy8gICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3Jcbi8vICAgICByZXR1cm4gZGF0YVxuLy8gICB9LFxuXG4vLyAgIC8vIEdldCBicmFuY2ggd2l0aCBkZXRhaWxzXG4vLyAgIGFzeW5jIGdldEJyYW5jaERldGFpbHMoYnJhbmNoSWQ6IHN0cmluZykge1xuLy8gICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4vLyAgICAgICAuZnJvbSgnYnJhbmNoZXMnKVxuLy8gICAgICAgLnNlbGVjdChgXG4vLyAgICAgICAgICosXG4vLyAgICAgICAgIG1hbmFnZXI6cHJvZmlsZXMoKiksXG4vLyAgICAgICAgIHdhcmVob3VzZXMoKiksXG4vLyAgICAgICAgIGtpdGNoZW5zKCopXG4vLyAgICAgICBgKVxuLy8gICAgICAgLmVxKCdpZCcsIGJyYW5jaElkKVxuLy8gICAgICAgLnNpbmdsZSgpXG5cbi8vICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4vLyAgICAgcmV0dXJuIGRhdGFcbi8vICAgfVxuLy8gfVxuZXhwb3J0IGNvbnN0IGJyYW5jaFNlcnZpY2UgPSB7XG4gIC8vIEdldCBhbGwgYnJhbmNoZXNcbiAgYXN5bmMgZ2V0QWxsQnJhbmNoZXMoKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdicmFuY2hlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgbWFuYWdlcjpwcm9maWxlcyFma19icmFuY2hlc19tYW5hZ2VyKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuICAgICAgLm9yZGVyKCduYW1lJylcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCBicmFuY2ggd2l0aCBkZXRhaWxzXG4gIGFzeW5jIGdldEJyYW5jaERldGFpbHMoYnJhbmNoSWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYnJhbmNoZXMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIG1hbmFnZXI6cHJvZmlsZXMhZmtfYnJhbmNoZXNfbWFuYWdlcigqKSxcbiAgICAgICAgd2FyZWhvdXNlcygqKSxcbiAgICAgICAga2l0Y2hlbnMoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ2lkJywgYnJhbmNoSWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9XG59XG5cbi8vIFdhcmVob3VzZSBNYW5hZ2VtZW50IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IHdhcmVob3VzZVNlcnZpY2UgPSB7XG4gIC8vIEdldCBhbGwgd2FyZWhvdXNlc1xuICBhc3luYyBnZXRBbGxXYXJlaG91c2VzKCkge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnd2FyZWhvdXNlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgYnJhbmNoOmJyYW5jaGVzKCopLFxuICAgICAgICBtYW5hZ2VyOnByb2ZpbGVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuICAgICAgLm9yZGVyKCduYW1lJylcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCB3YXJlaG91c2VzIGJ5IGJyYW5jaFxuICBhc3luYyBnZXRXYXJlaG91c2VzQnlCcmFuY2goYnJhbmNoSWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnd2FyZWhvdXNlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgbWFuYWdlcjpwcm9maWxlcygqKVxuICAgICAgYClcbiAgICAgIC5lcSgnYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4gICAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgICAub3JkZXIoJ25hbWUnKVxuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIHJldHVybiBkYXRhXG4gIH1cbn1cblxuLy8gSW5ncmVkaWVudCBNYW5hZ2VtZW50IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGluZ3JlZGllbnRTZXJ2aWNlID0ge1xuICAvLyBHZXQgYWxsIGluZ3JlZGllbnRzXG4gIGFzeW5jIGdldEFsbEluZ3JlZGllbnRzKCkge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW5ncmVkaWVudHMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGNhdGVnb3J5OmluZ3JlZGllbnRfY2F0ZWdvcmllcygqKVxuICAgICAgYClcbiAgICAgIC5lcSgnaXNfYWN0aXZlJywgdHJ1ZSlcbiAgICAgIC5vcmRlcignbmFtZScpXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBHZXQgaW5ncmVkaWVudHMgYnkgY2F0ZWdvcnlcbiAgYXN5bmMgZ2V0SW5ncmVkaWVudHNCeUNhdGVnb3J5KGNhdGVnb3J5SWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnaW5ncmVkaWVudHMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGNhdGVnb3J5OmluZ3JlZGllbnRfY2F0ZWdvcmllcygqKVxuICAgICAgYClcbiAgICAgIC5lcSgnY2F0ZWdvcnlfaWQnLCBjYXRlZ29yeUlkKVxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuICAgICAgLm9yZGVyKCduYW1lJylcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9XG59XG5cbi8vIFByb2R1Y3Rpb24gTWFuYWdlbWVudCBGdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBwcm9kdWN0aW9uU2VydmljZSA9IHtcbiAgLy8gR2V0IGFsbCByZWNpcGVzXG4gIGFzeW5jIGdldEFsbFJlY2lwZXMoKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdyZWNpcGVzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICByZWNpcGVfaW5ncmVkaWVudHMoXG4gICAgICAgICAgKixcbiAgICAgICAgICBpbmdyZWRpZW50OmluZ3JlZGllbnRzKCopXG4gICAgICAgIClcbiAgICAgIGApXG4gICAgICAuZXEoJ2lzX2FjdGl2ZScsIHRydWUpXG4gICAgICAub3JkZXIoJ25hbWUnKVxuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIHJldHVybiBkYXRhXG4gIH0sXG5cbiAgLy8gR2V0IHJlY2lwZSBkZXRhaWxzXG4gIGFzeW5jIGdldFJlY2lwZURldGFpbHMocmVjaXBlSWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgncmVjaXBlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcmVjaXBlX2luZ3JlZGllbnRzKFxuICAgICAgICAgICosXG4gICAgICAgICAgaW5ncmVkaWVudDppbmdyZWRpZW50cygqKVxuICAgICAgICApXG4gICAgICBgKVxuICAgICAgLmVxKCdpZCcsIHJlY2lwZUlkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBDcmVhdGUgcHJvZHVjdGlvbiBiYXRjaFxuICBhc3luYyBjcmVhdGVQcm9kdWN0aW9uQmF0Y2goa2l0Y2hlbklkOiBzdHJpbmcsIHJlY2lwZUlkOiBzdHJpbmcsIHBsYW5uZWRRdWFudGl0eTogbnVtYmVyLCBzdGFydGVkQnk6IHN0cmluZykge1xuICAgIGNvbnN0IGJhdGNoTnVtYmVyID0gYEJBVENILSR7RGF0ZS5ub3coKX1gXG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2R1Y3Rpb25fYmF0Y2hlcycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAga2l0Y2hlbl9pZDoga2l0Y2hlbklkLFxuICAgICAgICByZWNpcGVfaWQ6IHJlY2lwZUlkLFxuICAgICAgICBiYXRjaF9udW1iZXI6IGJhdGNoTnVtYmVyLFxuICAgICAgICBwbGFubmVkX3F1YW50aXR5OiBwbGFubmVkUXVhbnRpdHksXG4gICAgICAgIHN0YXR1czogJ3BsYW5uZWQnLFxuICAgICAgICBzdGFydGVkX2J5OiBzdGFydGVkQnksXG4gICAgICAgIHBsYW5uZWRfc3RhcnRfdGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCBwcm9kdWN0aW9uIGJhdGNoZXNcbiAgYXN5bmMgZ2V0UHJvZHVjdGlvbkJhdGNoZXMoa2l0Y2hlbklkPzogc3RyaW5nLCBzdGF0dXM/OiBzdHJpbmcpIHtcbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2R1Y3Rpb25fYmF0Y2hlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcmVjaXBlOnJlY2lwZXMoKiksXG4gICAgICAgIGtpdGNoZW46a2l0Y2hlbnMoKiksXG4gICAgICAgIHN0YXJ0ZXI6cHJvZmlsZXMhc3RhcnRlZF9ieSgqKSxcbiAgICAgICAgY29tcGxldGVyOnByb2ZpbGVzIWNvbXBsZXRlZF9ieSgqKVxuICAgICAgYClcblxuICAgIGlmIChraXRjaGVuSWQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2tpdGNoZW5faWQnLCBraXRjaGVuSWQpXG4gICAgfVxuXG4gICAgaWYgKHN0YXR1cykge1xuICAgICAgcXVlcnkgPSBxdWVyeS5lcSgnc3RhdHVzJywgc3RhdHVzKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBVcGRhdGUgYmF0Y2ggc3RhdHVzXG4gIGFzeW5jIHVwZGF0ZUJhdGNoU3RhdHVzKGJhdGNoSWQ6IHN0cmluZywgc3RhdHVzOiBzdHJpbmcsIHVzZXJJZD86IHN0cmluZywgYWN0dWFsUXVhbnRpdHk/OiBudW1iZXIsIHF1YWxpdHlTY29yZT86IG51bWJlciwgcXVhbGl0eU5vdGVzPzogc3RyaW5nKSB7XG4gICAgY29uc3QgdXBkYXRlczogYW55ID0ge1xuICAgICAgc3RhdHVzLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuXG4gICAgaWYgKHN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJykge1xuICAgICAgdXBkYXRlcy5hY3R1YWxfc3RhcnRfdGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAnY29tcGxldGVkJykge1xuICAgICAgdXBkYXRlcy5hY3R1YWxfZW5kX3RpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIHVwZGF0ZXMuY29tcGxldGVkX2J5ID0gdXNlcklkXG4gICAgICBpZiAoYWN0dWFsUXVhbnRpdHkpIHVwZGF0ZXMuYWN0dWFsX3F1YW50aXR5ID0gYWN0dWFsUXVhbnRpdHlcbiAgICAgIGlmIChxdWFsaXR5U2NvcmUpIHVwZGF0ZXMucXVhbGl0eV9zY29yZSA9IHF1YWxpdHlTY29yZVxuICAgICAgaWYgKHF1YWxpdHlOb3RlcykgdXBkYXRlcy5xdWFsaXR5X25vdGVzID0gcXVhbGl0eU5vdGVzXG4gICAgfVxuXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdwcm9kdWN0aW9uX2JhdGNoZXMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVzKVxuICAgICAgLmVxKCdpZCcsIGJhdGNoSWQpXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9XG4gIH0sXG5cbiAgLy8gUmVjb3JkIGluZ3JlZGllbnQgdXNhZ2UgZm9yIGJhdGNoXG4gIGFzeW5jIHJlY29yZEluZ3JlZGllbnRVc2FnZShiYXRjaElkOiBzdHJpbmcsIGluZ3JlZGllbnRVc2FnZTogYW55W10pIHtcbiAgICBjb25zdCB1c2FnZVJlY29yZHMgPSBpbmdyZWRpZW50VXNhZ2UubWFwKHVzYWdlID0+ICh7XG4gICAgICBiYXRjaF9pZDogYmF0Y2hJZCxcbiAgICAgIGluZ3JlZGllbnRfaWQ6IHVzYWdlLmluZ3JlZGllbnRfaWQsXG4gICAgICBwbGFubmVkX3F1YW50aXR5OiB1c2FnZS5wbGFubmVkX3F1YW50aXR5LFxuICAgICAgYWN0dWFsX3F1YW50aXR5OiB1c2FnZS5hY3R1YWxfcXVhbnRpdHksXG4gICAgICB1bml0OiB1c2FnZS51bml0LFxuICAgICAgY29zdF9wZXJfdW5pdDogdXNhZ2UuY29zdF9wZXJfdW5pdCxcbiAgICAgIHRvdGFsX2Nvc3Q6IHVzYWdlLmFjdHVhbF9xdWFudGl0eSAqIHVzYWdlLmNvc3RfcGVyX3VuaXRcbiAgICB9KSlcblxuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnYmF0Y2hfaW5ncmVkaWVudHNfdXNlZCcpXG4gICAgICAuaW5zZXJ0KHVzYWdlUmVjb3JkcylcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH1cbiAgfSxcblxuICAvLyBHZXQga2l0Y2hlbiBwcm9kdWN0aW9uIHN1bW1hcnlcbiAgYXN5bmMgZ2V0S2l0Y2hlblByb2R1Y3Rpb25TdW1tYXJ5KGtpdGNoZW5JZDogc3RyaW5nLCBzdGFydERhdGU/OiBzdHJpbmcsIGVuZERhdGU/OiBzdHJpbmcpIHtcbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2R1Y3Rpb25fYmF0Y2hlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcmVjaXBlOnJlY2lwZXMobmFtZSlcbiAgICAgIGApXG4gICAgICAuZXEoJ2tpdGNoZW5faWQnLCBraXRjaGVuSWQpXG5cbiAgICBpZiAoc3RhcnREYXRlKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lmd0ZSgnY3JlYXRlZF9hdCcsIHN0YXJ0RGF0ZSlcbiAgICB9XG5cbiAgICBpZiAoZW5kRGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5sdGUoJ2NyZWF0ZWRfYXQnLCBlbmREYXRlKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfVxufVxuXG4vLyBLaXRjaGVuIE1hbmFnZW1lbnQgRnVuY3Rpb25zXG5leHBvcnQgY29uc3Qga2l0Y2hlblNlcnZpY2UgPSB7XG4gIC8vIEdldCBhbGwga2l0Y2hlbnNcbiAgYXN5bmMgZ2V0QWxsS2l0Y2hlbnMoKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdraXRjaGVucycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgYnJhbmNoOmJyYW5jaGVzKCopLFxuICAgICAgICB3YXJlaG91c2U6d2FyZWhvdXNlcygqKSxcbiAgICAgICAgaGVhZF9jaGVmOnByb2ZpbGVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxuICAgICAgLm9yZGVyKCduYW1lJylcblxuICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICByZXR1cm4gZGF0YVxuICB9LFxuXG4gIC8vIEdldCBraXRjaGVuIGRldGFpbHNcbiAgYXN5bmMgZ2V0S2l0Y2hlbkRldGFpbHMoa2l0Y2hlbklkOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2tpdGNoZW5zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBicmFuY2g6YnJhbmNoZXMoKiksXG4gICAgICAgIHdhcmVob3VzZTp3YXJlaG91c2VzKCopLFxuICAgICAgICBoZWFkX2NoZWY6cHJvZmlsZXMoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ2lkJywga2l0Y2hlbklkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfVxufVxuXG4vLyBTYWxlcyBNYW5hZ2VtZW50IEZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IHNhbGVzU2VydmljZSA9IHtcbiAgLy8gR2V0IHNhbGVzIHRyYW5zYWN0aW9uc1xuICBhc3luYyBnZXRTYWxlc1RyYW5zYWN0aW9ucyhicmFuY2hJZD86IHN0cmluZywgc3RhcnREYXRlPzogc3RyaW5nLCBlbmREYXRlPzogc3RyaW5nKSB7XG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdzYWxlc190cmFuc2FjdGlvbnMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGJyYW5jaDpicmFuY2hlcygqKSxcbiAgICAgICAgc2VydmVyOnByb2ZpbGVzIXNlcnZlZF9ieSgqKSxcbiAgICAgICAgc2FsZXNfdHJhbnNhY3Rpb25faXRlbXMoXG4gICAgICAgICAgKixcbiAgICAgICAgICBtZW51X2l0ZW06bWVudV9pdGVtcygqKVxuICAgICAgICApXG4gICAgICBgKVxuXG4gICAgaWYgKGJyYW5jaElkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdicmFuY2hfaWQnLCBicmFuY2hJZClcbiAgICB9XG5cbiAgICBpZiAoc3RhcnREYXRlKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lmd0ZSgnY3JlYXRlZF9hdCcsIHN0YXJ0RGF0ZSlcbiAgICB9XG5cbiAgICBpZiAoZW5kRGF0ZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5sdGUoJ2NyZWF0ZWRfYXQnLCBlbmREYXRlKVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBHZXQgZGFpbHkgc2FsZXMgc3VtbWFyeVxuICBhc3luYyBnZXREYWlseVNhbGVzU3VtbWFyeShicmFuY2hJZDogc3RyaW5nLCBkYXRlPzogc3RyaW5nKSB7XG4gICAgY29uc3QgdGFyZ2V0RGF0ZSA9IGRhdGUgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnZGFpbHlfc2FsZXNfc3VtbWFyaWVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdicmFuY2hfaWQnLCBicmFuY2hJZClcbiAgICAgIC5lcSgnZGF0ZScsIHRhcmdldERhdGUpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB0aHJvdyBlcnJvclxuICAgIHJldHVybiBkYXRhXG4gIH0sXG5cbiAgLy8gQ3JlYXRlIHNhbGVzIHRyYW5zYWN0aW9uXG4gIGFzeW5jIGNyZWF0ZVNhbGVzVHJhbnNhY3Rpb24oYnJhbmNoSWQ6IHN0cmluZywgaXRlbXM6IGFueVtdLCBjdXN0b21lckluZm86IGFueSwgcGF5bWVudEluZm86IGFueSwgc2VydmVkQnk6IHN0cmluZykge1xuICAgIGNvbnN0IHRyYW5zYWN0aW9uTnVtYmVyID0gYFRYTi0ke0RhdGUubm93KCl9YFxuICAgIGNvbnN0IHRvdGFsQW1vdW50ID0gaXRlbXMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIChpdGVtLnF1YW50aXR5ICogaXRlbS51bml0X3ByaWNlKSwgMClcblxuICAgIC8vIENyZWF0ZSB0cmFuc2FjdGlvblxuICAgIGNvbnN0IHsgZGF0YTogdHJhbnNhY3Rpb24sIGVycm9yOiB0cmFuc2FjdGlvbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3NhbGVzX3RyYW5zYWN0aW9ucycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgYnJhbmNoX2lkOiBicmFuY2hJZCxcbiAgICAgICAgdHJhbnNhY3Rpb25fbnVtYmVyOiB0cmFuc2FjdGlvbk51bWJlcixcbiAgICAgICAgY3VzdG9tZXJfbmFtZTogY3VzdG9tZXJJbmZvLm5hbWUsXG4gICAgICAgIGN1c3RvbWVyX3Bob25lOiBjdXN0b21lckluZm8ucGhvbmUsXG4gICAgICAgIHRvdGFsX2Ftb3VudDogdG90YWxBbW91bnQsXG4gICAgICAgIHRheF9hbW91bnQ6IHBheW1lbnRJbmZvLnRheF9hbW91bnQgfHwgMCxcbiAgICAgICAgZGlzY291bnRfYW1vdW50OiBwYXltZW50SW5mby5kaXNjb3VudF9hbW91bnQgfHwgMCxcbiAgICAgICAgcGF5bWVudF9tZXRob2Q6IHBheW1lbnRJbmZvLm1ldGhvZCxcbiAgICAgICAgcGF5bWVudF9zdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICBzZXJ2ZWRfYnk6IHNlcnZlZEJ5XG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmICh0cmFuc2FjdGlvbkVycm9yKSB0aHJvdyB0cmFuc2FjdGlvbkVycm9yXG5cbiAgICAvLyBDcmVhdGUgdHJhbnNhY3Rpb24gaXRlbXNcbiAgICBjb25zdCB0cmFuc2FjdGlvbkl0ZW1zID0gaXRlbXMubWFwKGl0ZW0gPT4gKHtcbiAgICAgIHRyYW5zYWN0aW9uX2lkOiB0cmFuc2FjdGlvbi5pZCxcbiAgICAgIG1lbnVfaXRlbV9pZDogaXRlbS5tZW51X2l0ZW1faWQsXG4gICAgICBxdWFudGl0eTogaXRlbS5xdWFudGl0eSxcbiAgICAgIHVuaXRfcHJpY2U6IGl0ZW0udW5pdF9wcmljZSxcbiAgICAgIHRvdGFsX3ByaWNlOiBpdGVtLnF1YW50aXR5ICogaXRlbS51bml0X3ByaWNlLFxuICAgICAgc3BlY2lhbF9pbnN0cnVjdGlvbnM6IGl0ZW0uc3BlY2lhbF9pbnN0cnVjdGlvbnNcbiAgICB9KSlcblxuICAgIGNvbnN0IHsgZXJyb3I6IGl0ZW1zRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnc2FsZXNfdHJhbnNhY3Rpb25faXRlbXMnKVxuICAgICAgLmluc2VydCh0cmFuc2FjdGlvbkl0ZW1zKVxuXG4gICAgaWYgKGl0ZW1zRXJyb3IpIHRocm93IGl0ZW1zRXJyb3JcblxuICAgIHJldHVybiB0cmFuc2FjdGlvblxuICB9XG59XG5cbi8vIE1lbnUgTWFuYWdlbWVudCBGdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBtZW51U2VydmljZSA9IHtcbiAgLy8gR2V0IG1lbnUgaXRlbXNcbiAgYXN5bmMgZ2V0TWVudUl0ZW1zKCkge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnbWVudV9pdGVtcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcmVjaXBlOnJlY2lwZXMoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ2lzX2F2YWlsYWJsZScsIHRydWUpXG4gICAgICAub3JkZXIoJ2NhdGVnb3J5JywgeyBhc2NlbmRpbmc6IHRydWUgfSlcbiAgICAgIC5vcmRlcignbmFtZScsIHsgYXNjZW5kaW5nOiB0cnVlIH0pXG5cbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBHZXQgYnJhbmNoIG1lbnUgd2l0aCBwcmljaW5nXG4gIC8vIGFzeW5jIGdldEJyYW5jaE1lbnUoYnJhbmNoSWQ6IHN0cmluZykge1xuICAvLyAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gIC8vICAgICAuZnJvbSgnYnJhbmNoX21lbnVfcHJpY2luZycpXG4gIC8vICAgICAuc2VsZWN0KGBcbiAgLy8gICAgICAgKixcbiAgLy8gICAgICAgbWVudV9pdGVtOm1lbnVfaXRlbXMoXG4gIC8vICAgICAgICAgKixcbiAgLy8gICAgICAgICByZWNpcGU6cmVjaXBlcygqKVxuICAvLyAgICAgICApXG4gIC8vICAgICBgKVxuICAvLyAgICAgLmVxKCdicmFuY2hfaWQnLCBicmFuY2hJZClcbiAgLy8gICAgIC5lcSgnaXNfYXZhaWxhYmxlJywgdHJ1ZSlcbiAgLy8gICAgIC5vcmRlcignbWVudV9pdGVtLmNhdGVnb3J5JywgeyBhc2NlbmRpbmc6IHRydWUgfSlcblxuICAvLyAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgLy8gICByZXR1cm4gZGF0YVxuICAvLyB9LFxuICBhc3luYyBnZXRCcmFuY2hNZW51KGJyYW5jaElkOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2JyYW5jaF9tZW51X3ByaWNpbmcnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIG1lbnVfaXRlbTptZW51X2l0ZW1zKFxuICAgICAgICAgICosXG4gICAgICAgICAgcmVjaXBlOnJlY2lwZXMoKilcbiAgICAgICAgKVxuICAgICAgYClcbiAgICAgIC5lcSgnYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4gICAgICAuZXEoJ2lzX2F2YWlsYWJsZScsIHRydWUpXG4gICAgICAub3JkZXIoJ21lbnVfaXRlbShjYXRlZ29yeSknLCB7IGFzY2VuZGluZzogdHJ1ZSB9KVxuICBcbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfSxcblxuICAvLyBVcGRhdGUgbWVudSBpdGVtIGF2YWlsYWJpbGl0eVxuICBhc3luYyB1cGRhdGVNZW51SXRlbUF2YWlsYWJpbGl0eShicmFuY2hJZDogc3RyaW5nLCBtZW51SXRlbUlkOiBzdHJpbmcsIGlzQXZhaWxhYmxlOiBib29sZWFuKSB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdicmFuY2hfbWVudV9wcmljaW5nJylcbiAgICAgIC51cGRhdGUoeyBpc19hdmFpbGFibGU6IGlzQXZhaWxhYmxlIH0pXG4gICAgICAuZXEoJ2JyYW5jaF9pZCcsIGJyYW5jaElkKVxuICAgICAgLmVxKCdtZW51X2l0ZW1faWQnLCBtZW51SXRlbUlkKVxuXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfVxuICB9XG59XG5cbi8vIC8vIEFuYWx5dGljcyBhbmQgUmVwb3J0aW5nIEZ1bmN0aW9uc1xuLy8gZXhwb3J0IGNvbnN0IGFuYWx5dGljc1NlcnZpY2UgPSB7XG4vLyAgIC8vIEdldCBicmFuY2ggcGVyZm9ybWFuY2UgbWV0cmljc1xuLy8gICBhc3luYyBnZXRCcmFuY2hQZXJmb3JtYW5jZShicmFuY2hJZDogc3RyaW5nLCBzdGFydERhdGU/OiBzdHJpbmcsIGVuZERhdGU/OiBzdHJpbmcpIHtcbi8vICAgICBjb25zdCBzdGFydCA9IHN0YXJ0RGF0ZSB8fCBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpXG4vLyAgICAgY29uc3QgZW5kID0gZW5kRGF0ZSB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcblxuLy8gICAgIC8vIEdldCBzYWxlcyBkYXRhXG4vLyAgICAgY29uc3QgeyBkYXRhOiBzYWxlc0RhdGEsIGVycm9yOiBzYWxlc0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuLy8gICAgICAgLmZyb20oJ3NhbGVzX3RyYW5zYWN0aW9ucycpXG4vLyAgICAgICAuc2VsZWN0KCd0b3RhbF9hbW91bnQsIGNyZWF0ZWRfYXQsIHBheW1lbnRfbWV0aG9kJylcbi8vICAgICAgIC5lcSgnYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4vLyAgICAgICAuZ3RlKCdjcmVhdGVkX2F0Jywgc3RhcnQpXG4vLyAgICAgICAubHRlKCdjcmVhdGVkX2F0JywgZW5kKVxuXG4vLyAgICAgaWYgKHNhbGVzRXJyb3IpIHRocm93IHNhbGVzRXJyb3JcblxuLy8gICAgIC8vIEdldCBpbnZlbnRvcnkgZGF0YVxuLy8gICAgIGNvbnN0IHsgZGF0YTogaW52ZW50b3J5RGF0YSwgZXJyb3I6IGludmVudG9yeUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuLy8gICAgICAgLmZyb20oJ2ludmVudG9yeScpXG4vLyAgICAgICAuc2VsZWN0KGBcbi8vICAgICAgICAgY3VycmVudF9zdG9jayxcbi8vICAgICAgICAgbWluaW11bV9zdG9jayxcbi8vICAgICAgICAgaW5ncmVkaWVudDppbmdyZWRpZW50cyhjb3N0X3Blcl91bml0KVxuLy8gICAgICAgYClcbi8vICAgICAgIC5pbignd2FyZWhvdXNlX2lkJyxcbi8vICAgICAgICAgc3VwYWJhc2Vcbi8vICAgICAgICAgICAuZnJvbSgnd2FyZWhvdXNlcycpXG4vLyAgICAgICAgICAgLnNlbGVjdCgnaWQnKVxuLy8gICAgICAgICAgIC5lcSgnYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4vLyAgICAgICApXG5cbi8vICAgICBpZiAoaW52ZW50b3J5RXJyb3IpIHRocm93IGludmVudG9yeUVycm9yXG5cbi8vICAgICAvLyBDYWxjdWxhdGUgbWV0cmljc1xuLy8gICAgIGNvbnN0IHRvdGFsUmV2ZW51ZSA9IHNhbGVzRGF0YS5yZWR1Y2UoKHN1bSwgc2FsZSkgPT4gc3VtICsgc2FsZS50b3RhbF9hbW91bnQsIDApXG4vLyAgICAgY29uc3QgdG90YWxUcmFuc2FjdGlvbnMgPSBzYWxlc0RhdGEubGVuZ3RoXG4vLyAgICAgY29uc3QgYXZlcmFnZVRyYW5zYWN0aW9uID0gdG90YWxUcmFuc2FjdGlvbnMgPiAwID8gdG90YWxSZXZlbnVlIC8gdG90YWxUcmFuc2FjdGlvbnMgOiAwXG4vLyAgICAgY29uc3QgaW52ZW50b3J5VmFsdWUgPSBpbnZlbnRvcnlEYXRhLnJlZHVjZSgoc3VtLCBpdGVtKSA9PlxuLy8gICAgICAgc3VtICsgKGl0ZW0uY3VycmVudF9zdG9jayAqIChpdGVtLmluZ3JlZGllbnQ/LmNvc3RfcGVyX3VuaXQgfHwgMCkpLCAwXG4vLyAgICAgKVxuLy8gICAgIGNvbnN0IGxvd1N0b2NrSXRlbXMgPSBpbnZlbnRvcnlEYXRhLmZpbHRlcihpdGVtID0+XG4vLyAgICAgICBpdGVtLmN1cnJlbnRfc3RvY2sgPD0gaXRlbS5taW5pbXVtX3N0b2NrXG4vLyAgICAgKS5sZW5ndGhcblxuLy8gICAgIHJldHVybiB7XG4vLyAgICAgICB0b3RhbFJldmVudWUsXG4vLyAgICAgICB0b3RhbFRyYW5zYWN0aW9ucyxcbi8vICAgICAgIGF2ZXJhZ2VUcmFuc2FjdGlvbixcbi8vICAgICAgIGludmVudG9yeVZhbHVlLFxuLy8gICAgICAgbG93U3RvY2tJdGVtcyxcbi8vICAgICAgIHNhbGVzRGF0YSxcbi8vICAgICAgIGludmVudG9yeURhdGFcbi8vICAgICB9XG4vLyAgIH0sXG5cbi8vICAgLy8gR2V0IHRvcCBzZWxsaW5nIGl0ZW1zXG4vLyAgIGFzeW5jIGdldFRvcFNlbGxpbmdJdGVtcyhicmFuY2hJZDogc3RyaW5nLCBsaW1pdCA9IDEwLCBzdGFydERhdGU/OiBzdHJpbmcsIGVuZERhdGU/OiBzdHJpbmcpIHtcbi8vICAgICBjb25zdCBzdGFydCA9IHN0YXJ0RGF0ZSB8fCBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpXG4vLyAgICAgY29uc3QgZW5kID0gZW5kRGF0ZSB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcblxuLy8gICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4vLyAgICAgICAuZnJvbSgnc2FsZXNfdHJhbnNhY3Rpb25faXRlbXMnKVxuLy8gICAgICAgLnNlbGVjdChgXG4vLyAgICAgICAgIHF1YW50aXR5LFxuLy8gICAgICAgICB0b3RhbF9wcmljZSxcbi8vICAgICAgICAgbWVudV9pdGVtOm1lbnVfaXRlbXMobmFtZSwgY2F0ZWdvcnkpLFxuLy8gICAgICAgICB0cmFuc2FjdGlvbjpzYWxlc190cmFuc2FjdGlvbnMhaW5uZXIoY3JlYXRlZF9hdCwgYnJhbmNoX2lkKVxuLy8gICAgICAgYClcbi8vICAgICAgIC5lcSgndHJhbnNhY3Rpb24uYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4vLyAgICAgICAuZ3RlKCd0cmFuc2FjdGlvbi5jcmVhdGVkX2F0Jywgc3RhcnQpXG4vLyAgICAgICAubHRlKCd0cmFuc2FjdGlvbi5jcmVhdGVkX2F0JywgZW5kKVxuXG4vLyAgICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuXG4vLyAgICAgLy8gQWdncmVnYXRlIGJ5IG1lbnUgaXRlbVxuLy8gICAgIGNvbnN0IGl0ZW1TdGF0cyA9IGRhdGEucmVkdWNlKChhY2M6IGFueSwgaXRlbSkgPT4ge1xuLy8gICAgICAgY29uc3QgaXRlbU5hbWUgPSBpdGVtLm1lbnVfaXRlbS5uYW1lXG4vLyAgICAgICBpZiAoIWFjY1tpdGVtTmFtZV0pIHtcbi8vICAgICAgICAgYWNjW2l0ZW1OYW1lXSA9IHtcbi8vICAgICAgICAgICBuYW1lOiBpdGVtTmFtZSxcbi8vICAgICAgICAgICBjYXRlZ29yeTogaXRlbS5tZW51X2l0ZW0uY2F0ZWdvcnksXG4vLyAgICAgICAgICAgdG90YWxRdWFudGl0eTogMCxcbi8vICAgICAgICAgICB0b3RhbFJldmVudWU6IDBcbi8vICAgICAgICAgfVxuLy8gICAgICAgfVxuLy8gICAgICAgYWNjW2l0ZW1OYW1lXS50b3RhbFF1YW50aXR5ICs9IGl0ZW0ucXVhbnRpdHlcbi8vICAgICAgIGFjY1tpdGVtTmFtZV0udG90YWxSZXZlbnVlICs9IGl0ZW0udG90YWxfcHJpY2Vcbi8vICAgICAgIHJldHVybiBhY2Ncbi8vICAgICB9LCB7fSlcblxuLy8gICAgIHJldHVybiBPYmplY3QudmFsdWVzKGl0ZW1TdGF0cylcbi8vICAgICAgIC5zb3J0KChhOiBhbnksIGI6IGFueSkgPT4gYi50b3RhbFF1YW50aXR5IC0gYS50b3RhbFF1YW50aXR5KVxuLy8gICAgICAgLnNsaWNlKDAsIGxpbWl0KVxuLy8gICB9XG4vLyB9XG4vLyBBbmFseXRpY3MgYW5kIFJlcG9ydGluZyBGdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBhbmFseXRpY3NTZXJ2aWNlID0ge1xuICAvLyBHZXQgYnJhbmNoIHBlcmZvcm1hbmNlIG1ldHJpY3NcbiAgYXN5bmMgZ2V0QnJhbmNoUGVyZm9ybWFuY2UoYnJhbmNoSWQ6IHN0cmluZywgc3RhcnREYXRlPzogc3RyaW5nLCBlbmREYXRlPzogc3RyaW5nKSB7XG4gICAgY29uc3Qgc3RhcnQgPSBzdGFydERhdGUgfHwgbmV3IERhdGUoRGF0ZS5ub3coKSAtIDMwICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKVxuICAgIGNvbnN0IGVuZCA9IGVuZERhdGUgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgXG4gICAgLy8gR2V0IHNhbGVzIGRhdGFcbiAgICBjb25zdCB7IGRhdGE6IHNhbGVzRGF0YSwgZXJyb3I6IHNhbGVzRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnc2FsZXNfdHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoJ3RvdGFsX2Ftb3VudCwgY3JlYXRlZF9hdCwgcGF5bWVudF9tZXRob2QnKVxuICAgICAgLmVxKCdicmFuY2hfaWQnLCBicmFuY2hJZClcbiAgICAgIC5ndGUoJ2NyZWF0ZWRfYXQnLCBzdGFydClcbiAgICAgIC5sdGUoJ2NyZWF0ZWRfYXQnLCBlbmQpXG4gICAgXG4gICAgaWYgKHNhbGVzRXJyb3IpIHRocm93IHNhbGVzRXJyb3JcbiAgICBcbiAgICAvLyBGaXJzdCBnZXQgd2FyZWhvdXNlIElEcyBmb3IgdGhlIGJyYW5jaFxuICAgIGNvbnN0IHsgZGF0YTogd2FyZWhvdXNlSWRzLCBlcnJvcjogd2FyZWhvdXNlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnd2FyZWhvdXNlcycpXG4gICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAuZXEoJ2JyYW5jaF9pZCcsIGJyYW5jaElkKVxuICAgIFxuICAgIGlmICh3YXJlaG91c2VFcnJvcikgdGhyb3cgd2FyZWhvdXNlRXJyb3JcbiAgICBcbiAgICAvLyBHZXQgaW52ZW50b3J5IGRhdGEgdXNpbmcgd2FyZWhvdXNlIElEc1xuICAgIGNvbnN0IHdhcmVob3VzZUlkTGlzdCA9IHdhcmVob3VzZUlkcy5tYXAodyA9PiB3LmlkKVxuICAgIGNvbnN0IHsgZGF0YTogaW52ZW50b3J5RGF0YSwgZXJyb3I6IGludmVudG9yeUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2ludmVudG9yeScpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgY3VycmVudF9zdG9jayxcbiAgICAgICAgbWluaW11bV9zdG9jayxcbiAgICAgICAgaW5ncmVkaWVudDppbmdyZWRpZW50cyhjb3N0X3Blcl91bml0KVxuICAgICAgYClcbiAgICAgIC5pbignd2FyZWhvdXNlX2lkJywgd2FyZWhvdXNlSWRMaXN0KVxuICAgIFxuICAgIGlmIChpbnZlbnRvcnlFcnJvcikgdGhyb3cgaW52ZW50b3J5RXJyb3JcbiAgICBcbiAgICAvLyBDYWxjdWxhdGUgbWV0cmljc1xuICAgIGNvbnN0IHRvdGFsUmV2ZW51ZSA9IHNhbGVzRGF0YS5yZWR1Y2UoKHN1bSwgc2FsZSkgPT4gc3VtICsgc2FsZS50b3RhbF9hbW91bnQsIDApXG4gICAgY29uc3QgdG90YWxUcmFuc2FjdGlvbnMgPSBzYWxlc0RhdGEubGVuZ3RoXG4gICAgY29uc3QgYXZlcmFnZVRyYW5zYWN0aW9uID0gdG90YWxUcmFuc2FjdGlvbnMgPiAwID8gdG90YWxSZXZlbnVlIC8gdG90YWxUcmFuc2FjdGlvbnMgOiAwXG4gICAgY29uc3QgaW52ZW50b3J5VmFsdWUgPSBpbnZlbnRvcnlEYXRhLnJlZHVjZSgoc3VtLCBpdGVtKSA9PlxuICAgICAgc3VtICsgKGl0ZW0uY3VycmVudF9zdG9jayAqIChpdGVtLmluZ3JlZGllbnQ/LmNvc3RfcGVyX3VuaXQgfHwgMCkpLCAwXG4gICAgKVxuICAgIGNvbnN0IGxvd1N0b2NrSXRlbXMgPSBpbnZlbnRvcnlEYXRhLmZpbHRlcihpdGVtID0+XG4gICAgICBpdGVtLmN1cnJlbnRfc3RvY2sgPD0gaXRlbS5taW5pbXVtX3N0b2NrXG4gICAgKS5sZW5ndGhcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxSZXZlbnVlLFxuICAgICAgdG90YWxUcmFuc2FjdGlvbnMsXG4gICAgICBhdmVyYWdlVHJhbnNhY3Rpb24sXG4gICAgICBpbnZlbnRvcnlWYWx1ZSxcbiAgICAgIGxvd1N0b2NrSXRlbXMsXG4gICAgICBzYWxlc0RhdGEsXG4gICAgICBpbnZlbnRvcnlEYXRhXG4gICAgfVxuICB9LFxuXG4gIC8vIEdldCB0b3Agc2VsbGluZyBpdGVtc1xuICBhc3luYyBnZXRUb3BTZWxsaW5nSXRlbXMoYnJhbmNoSWQ6IHN0cmluZywgbGltaXQgPSAxMCwgc3RhcnREYXRlPzogc3RyaW5nLCBlbmREYXRlPzogc3RyaW5nKSB7XG4gICAgY29uc3Qgc3RhcnQgPSBzdGFydERhdGUgfHwgbmV3IERhdGUoRGF0ZS5ub3coKSAtIDMwICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKVxuICAgIGNvbnN0IGVuZCA9IGVuZERhdGUgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdzYWxlc190cmFuc2FjdGlvbl9pdGVtcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgcXVhbnRpdHksXG4gICAgICAgIHRvdGFsX3ByaWNlLFxuICAgICAgICBtZW51X2l0ZW06bWVudV9pdGVtcyhuYW1lLCBjYXRlZ29yeSksXG4gICAgICAgIHNhbGVzX3RyYW5zYWN0aW9ucyFpbm5lcihjcmVhdGVkX2F0LCBicmFuY2hfaWQpXG4gICAgICBgKVxuICAgICAgLmVxKCdzYWxlc190cmFuc2FjdGlvbnMuYnJhbmNoX2lkJywgYnJhbmNoSWQpXG4gICAgICAuZ3RlKCdzYWxlc190cmFuc2FjdGlvbnMuY3JlYXRlZF9hdCcsIHN0YXJ0KVxuICAgICAgLmx0ZSgnc2FsZXNfdHJhbnNhY3Rpb25zLmNyZWF0ZWRfYXQnLCBlbmQpXG4gICAgXG4gICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvclxuICAgIFxuICAgIC8vIEFnZ3JlZ2F0ZSBieSBtZW51IGl0ZW1cbiAgICBjb25zdCBpdGVtU3RhdHMgPSBkYXRhLnJlZHVjZSgoYWNjOiBhbnksIGl0ZW0pID0+IHtcbiAgICAgIGNvbnN0IGl0ZW1OYW1lID0gaXRlbS5tZW51X2l0ZW0ubmFtZVxuICAgICAgaWYgKCFhY2NbaXRlbU5hbWVdKSB7XG4gICAgICAgIGFjY1tpdGVtTmFtZV0gPSB7XG4gICAgICAgICAgbmFtZTogaXRlbU5hbWUsXG4gICAgICAgICAgY2F0ZWdvcnk6IGl0ZW0ubWVudV9pdGVtLmNhdGVnb3J5LFxuICAgICAgICAgIHRvdGFsUXVhbnRpdHk6IDAsXG4gICAgICAgICAgdG90YWxSZXZlbnVlOiAwXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGFjY1tpdGVtTmFtZV0udG90YWxRdWFudGl0eSArPSBpdGVtLnF1YW50aXR5XG4gICAgICBhY2NbaXRlbU5hbWVdLnRvdGFsUmV2ZW51ZSArPSBpdGVtLnRvdGFsX3ByaWNlXG4gICAgICByZXR1cm4gYWNjXG4gICAgfSwge30pXG4gICAgXG4gICAgcmV0dXJuIE9iamVjdC52YWx1ZXMoaXRlbVN0YXRzKVxuICAgICAgLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiBiLnRvdGFsUXVhbnRpdHkgLSBhLnRvdGFsUXVhbnRpdHkpXG4gICAgICAuc2xpY2UoMCwgbGltaXQpXG4gIH0sXG5cbiAgLy8gQWx0ZXJuYXRpdmUgYXBwcm9hY2ggZm9yIHRvcCBzZWxsaW5nIGl0ZW1zIHVzaW5nIFJQQyBpZiB0aGUgYWJvdmUgZG9lc24ndCB3b3JrXG4gIGFzeW5jIGdldFRvcFNlbGxpbmdJdGVtc1JQQyhicmFuY2hJZDogc3RyaW5nLCBsaW1pdCA9IDEwLCBzdGFydERhdGU/OiBzdHJpbmcsIGVuZERhdGU/OiBzdHJpbmcpIHtcbiAgICBjb25zdCBzdGFydCA9IHN0YXJ0RGF0ZSB8fCBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpXG4gICAgY29uc3QgZW5kID0gZW5kRGF0ZSB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICBcbiAgICAvLyBDYWxsIGEgc3RvcmVkIHByb2NlZHVyZS9mdW5jdGlvbiBmb3IgY29tcGxleCBhZ2dyZWdhdGlvblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAucnBjKCdnZXRfdG9wX3NlbGxpbmdfaXRlbXMnLCB7XG4gICAgICAgIGJyYW5jaF9pZDogYnJhbmNoSWQsXG4gICAgICAgIHN0YXJ0X2RhdGU6IHN0YXJ0LFxuICAgICAgICBlbmRfZGF0ZTogZW5kLFxuICAgICAgICBpdGVtX2xpbWl0OiBsaW1pdFxuICAgICAgfSlcbiAgICBcbiAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yXG4gICAgcmV0dXJuIGRhdGFcbiAgfVxufVxuIl0sIm5hbWVzIjpbInN1cGFiYXNlIiwiaW52ZW50b3J5U2VydmljZSIsImdldFdhcmVob3VzZUludmVudG9yeSIsIndhcmVob3VzZUlkIiwiZGF0YSIsImVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwib3JkZXIiLCJhc2NlbmRpbmciLCJnZXRMb3dTdG9ja0l0ZW1zIiwiYnJhbmNoSWQiLCJxdWVyeSIsImxvd1N0b2NrSXRlbXMiLCJmaWx0ZXIiLCJpdGVtIiwiY3VycmVudF9zdG9jayIsIm1pbmltdW1fc3RvY2siLCJzb3J0IiwiYSIsImIiLCJ1cGRhdGVTdG9jayIsImludmVudG9yeUlkIiwibmV3U3RvY2siLCJtb3ZlbWVudFR5cGUiLCJub3RlcyIsInBlcmZvcm1lZEJ5IiwiRXJyb3IiLCJpbmNsdWRlcyIsImludmVudG9yeSIsImZldGNoRXJyb3IiLCJzaW5nbGUiLCJtZXNzYWdlIiwicXVhbnRpdHkiLCJpc0luY3JlYXNlIiwibWF4aW11bV9zdG9jayIsImluZ3JlZGllbnQiLCJ1bml0IiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlRXJyb3IiLCJ1cGRhdGUiLCJ1cGRhdGVkX2F0IiwibGFzdF9yZXN0b2NrZWRfYXQiLCJtb3ZlbWVudEVycm9yIiwiaW5zZXJ0Iiwid2FyZWhvdXNlX2lkIiwiaW5ncmVkaWVudF9pZCIsIm1vdmVtZW50X3R5cGUiLCJNYXRoIiwiYWJzIiwicmVmZXJlbmNlX3R5cGUiLCJuYW1lIiwid2FyZWhvdXNlIiwicGVyZm9ybWVkX2J5IiwiY3JlYXRlZF9hdCIsInN1Y2Nlc3MiLCJwcmV2aW91c1N0b2NrIiwiY3JlYXRlSW52ZW50b3J5UmVjb3JkIiwiaW5ncmVkaWVudElkIiwiaW5pdGlhbFN0b2NrIiwicmVvcmRlcl9wb2ludCIsImdldFN0b2NrTW92ZW1lbnRzIiwibGltaXQiLCJjcmVhdGVUcmFuc2ZlciIsImZyb21XYXJlaG91c2VJZCIsInRvV2FyZWhvdXNlSWQiLCJpdGVtcyIsInJlcXVlc3RlZEJ5IiwidHJhbnNmZXIiLCJ0cmFuc2ZlckVycm9yIiwiZnJvbV93YXJlaG91c2VfaWQiLCJ0b193YXJlaG91c2VfaWQiLCJyZXF1ZXN0ZWRfYnkiLCJ0b3RhbF9pdGVtcyIsImxlbmd0aCIsInN0YXR1cyIsInRyYW5zZmVySXRlbXMiLCJtYXAiLCJ0cmFuc2Zlcl9pZCIsImlkIiwicmVxdWVzdGVkX3F1YW50aXR5IiwiaXRlbXNFcnJvciIsImdldFBlbmRpbmdUcmFuc2ZlcnMiLCJvciIsImFwcHJvdmVUcmFuc2ZlciIsInRyYW5zZmVySWQiLCJhcHByb3ZlZEJ5IiwiYXBwcm92ZWRJdGVtcyIsImFwcHJvdmVkSXRlbSIsInRyYW5zZmVySXRlbSIsInRyYW5zZmVyX2l0ZW1zIiwiZmluZCIsImludmVudG9yeUVycm9yIiwiYXBwcm92ZWRfcXVhbnRpdHkiLCJhcHByb3ZlZF9ieSIsImFwcHJvdmVkX2F0IiwiaXRlbUVycm9yIiwiYXBwcm92ZWRBdCIsImNvbXBsZXRlVHJhbnNmZXIiLCJjb21wbGV0ZWRCeSIsInN0b2NrTW92ZW1lbnRzIiwiYXBwcm92ZWRRdHkiLCJzb3VyY2VJbnZlbnRvcnkiLCJzb3VyY2VFcnJvciIsInNvdXJjZVVwZGF0ZUVycm9yIiwicHVzaCIsInJlZmVyZW5jZV9pZCIsInRvX3dhcmVob3VzZSIsImRlc3RJbnZlbnRvcnkiLCJkZXN0RmV0Y2hFcnJvciIsIm1heWJlU2luZ2xlIiwiZGVzdFVwZGF0ZUVycm9yIiwiZGVzdENyZWF0ZUVycm9yIiwiZnJvbV93YXJlaG91c2UiLCJjb21wbGV0ZUVycm9yIiwiY29tcGxldGVkX2F0IiwiY29tcGxldGVkQXQiLCJpdGVtc1RyYW5zZmVycmVkIiwiY2FuY2VsVHJhbnNmZXIiLCJjYW5jZWxsZWRCeSIsInJlYXNvbiIsImNhbmNlbEVycm9yIiwiY2FuY2VsbGVkQXQiLCJidWxrVXBkYXRlU3RvY2siLCJ1cGRhdGVzIiwicmVzdWx0cyIsInN1Y2Nlc3NDb3VudCIsInIiLCJmYWlsdXJlQ291bnQiLCJ0b3RhbFVwZGF0ZXMiLCJicmFuY2hTZXJ2aWNlIiwiZ2V0QWxsQnJhbmNoZXMiLCJnZXRCcmFuY2hEZXRhaWxzIiwid2FyZWhvdXNlU2VydmljZSIsImdldEFsbFdhcmVob3VzZXMiLCJnZXRXYXJlaG91c2VzQnlCcmFuY2giLCJpbmdyZWRpZW50U2VydmljZSIsImdldEFsbEluZ3JlZGllbnRzIiwiZ2V0SW5ncmVkaWVudHNCeUNhdGVnb3J5IiwiY2F0ZWdvcnlJZCIsInByb2R1Y3Rpb25TZXJ2aWNlIiwiZ2V0QWxsUmVjaXBlcyIsImdldFJlY2lwZURldGFpbHMiLCJyZWNpcGVJZCIsImNyZWF0ZVByb2R1Y3Rpb25CYXRjaCIsImtpdGNoZW5JZCIsInBsYW5uZWRRdWFudGl0eSIsInN0YXJ0ZWRCeSIsImJhdGNoTnVtYmVyIiwibm93Iiwia2l0Y2hlbl9pZCIsInJlY2lwZV9pZCIsImJhdGNoX251bWJlciIsInBsYW5uZWRfcXVhbnRpdHkiLCJzdGFydGVkX2J5IiwicGxhbm5lZF9zdGFydF90aW1lIiwiZ2V0UHJvZHVjdGlvbkJhdGNoZXMiLCJ1cGRhdGVCYXRjaFN0YXR1cyIsImJhdGNoSWQiLCJ1c2VySWQiLCJhY3R1YWxRdWFudGl0eSIsInF1YWxpdHlTY29yZSIsInF1YWxpdHlOb3RlcyIsImFjdHVhbF9zdGFydF90aW1lIiwiYWN0dWFsX2VuZF90aW1lIiwiY29tcGxldGVkX2J5IiwiYWN0dWFsX3F1YW50aXR5IiwicXVhbGl0eV9zY29yZSIsInF1YWxpdHlfbm90ZXMiLCJyZWNvcmRJbmdyZWRpZW50VXNhZ2UiLCJpbmdyZWRpZW50VXNhZ2UiLCJ1c2FnZVJlY29yZHMiLCJ1c2FnZSIsImJhdGNoX2lkIiwiY29zdF9wZXJfdW5pdCIsInRvdGFsX2Nvc3QiLCJnZXRLaXRjaGVuUHJvZHVjdGlvblN1bW1hcnkiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZ3RlIiwibHRlIiwia2l0Y2hlblNlcnZpY2UiLCJnZXRBbGxLaXRjaGVucyIsImdldEtpdGNoZW5EZXRhaWxzIiwic2FsZXNTZXJ2aWNlIiwiZ2V0U2FsZXNUcmFuc2FjdGlvbnMiLCJnZXREYWlseVNhbGVzU3VtbWFyeSIsImRhdGUiLCJ0YXJnZXREYXRlIiwic3BsaXQiLCJjb2RlIiwiY3JlYXRlU2FsZXNUcmFuc2FjdGlvbiIsImN1c3RvbWVySW5mbyIsInBheW1lbnRJbmZvIiwic2VydmVkQnkiLCJ0cmFuc2FjdGlvbk51bWJlciIsInRvdGFsQW1vdW50IiwicmVkdWNlIiwic3VtIiwidW5pdF9wcmljZSIsInRyYW5zYWN0aW9uIiwidHJhbnNhY3Rpb25FcnJvciIsImJyYW5jaF9pZCIsInRyYW5zYWN0aW9uX251bWJlciIsImN1c3RvbWVyX25hbWUiLCJjdXN0b21lcl9waG9uZSIsInBob25lIiwidG90YWxfYW1vdW50IiwidGF4X2Ftb3VudCIsImRpc2NvdW50X2Ftb3VudCIsInBheW1lbnRfbWV0aG9kIiwibWV0aG9kIiwicGF5bWVudF9zdGF0dXMiLCJzZXJ2ZWRfYnkiLCJ0cmFuc2FjdGlvbkl0ZW1zIiwidHJhbnNhY3Rpb25faWQiLCJtZW51X2l0ZW1faWQiLCJ0b3RhbF9wcmljZSIsInNwZWNpYWxfaW5zdHJ1Y3Rpb25zIiwibWVudVNlcnZpY2UiLCJnZXRNZW51SXRlbXMiLCJnZXRCcmFuY2hNZW51IiwidXBkYXRlTWVudUl0ZW1BdmFpbGFiaWxpdHkiLCJtZW51SXRlbUlkIiwiaXNBdmFpbGFibGUiLCJpc19hdmFpbGFibGUiLCJhbmFseXRpY3NTZXJ2aWNlIiwiZ2V0QnJhbmNoUGVyZm9ybWFuY2UiLCJzdGFydCIsImVuZCIsInNhbGVzRGF0YSIsInNhbGVzRXJyb3IiLCJ3YXJlaG91c2VJZHMiLCJ3YXJlaG91c2VFcnJvciIsIndhcmVob3VzZUlkTGlzdCIsInciLCJpbnZlbnRvcnlEYXRhIiwiaW4iLCJ0b3RhbFJldmVudWUiLCJzYWxlIiwidG90YWxUcmFuc2FjdGlvbnMiLCJhdmVyYWdlVHJhbnNhY3Rpb24iLCJpbnZlbnRvcnlWYWx1ZSIsImdldFRvcFNlbGxpbmdJdGVtcyIsIml0ZW1TdGF0cyIsImFjYyIsIml0ZW1OYW1lIiwibWVudV9pdGVtIiwiY2F0ZWdvcnkiLCJ0b3RhbFF1YW50aXR5IiwiT2JqZWN0IiwidmFsdWVzIiwic2xpY2UiLCJnZXRUb3BTZWxsaW5nSXRlbXNSUEMiLCJycGMiLCJzdGFydF9kYXRlIiwiZW5kX2RhdGUiLCJpdGVtX2xpbWl0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});