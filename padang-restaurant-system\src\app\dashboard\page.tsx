'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import Link from 'next/link'
import {
  inventoryService,
  branchService,
  productionService,
  salesService,
  analyticsService
} from '@/lib/database'

interface DashboardStats {
  totalInventoryItems: number
  activeProductions: number
  activeBranches: number
  todaysSales: number
  lowStockItems: number
  inventoryValue: number
}

export default function DashboardPage() {
  const { profile } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalInventoryItems: 0,
    activeProductions: 0,
    activeBranches: 0,
    todaysSales: 0,
    lowStockItems: 0,
    inventoryValue: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Load all dashboard statistics
      const [
        branches,
        lowStockItems,
        productionBatches,
        todaysSalesData
      ] = await Promise.allSettled([
        branchService.getAllBranches(),
        inventoryService.getLowStockItems(),
        productionService.getProductionBatches(undefined, 'in_progress'),
        getTodaysSales()
      ])

      // Calculate inventory stats
      let totalInventoryItems = 0
      let inventoryValue = 0

      try {
        // Get all warehouses and their inventory
        const allWarehouses = branches.status === 'fulfilled' ? branches.value : []
        for (const branch of allWarehouses) {
          // This is a simplified approach - in a real app you'd optimize this
          try {
            const branchInventory = await inventoryService.getWarehouseInventory(branch.id)
            totalInventoryItems += branchInventory.length
            inventoryValue += branchInventory.reduce((sum, item) =>
              sum + (item.current_stock * (item.ingredient.cost_per_unit || 0)), 0
            )
          } catch (err) {
            console.warn(`Failed to load inventory for branch ${branch.id}:`, err)
          }
        }
      } catch (err) {
        console.warn('Failed to calculate inventory stats:', err)
      }

      setStats({
        totalInventoryItems,
        activeProductions: productionBatches.status === 'fulfilled' ? productionBatches.value.length : 0,
        activeBranches: branches.status === 'fulfilled' ? branches.value.length : 0,
        todaysSales: todaysSalesData.status === 'fulfilled' ? todaysSalesData.value : 0,
        lowStockItems: lowStockItems.status === 'fulfilled' ? lowStockItems.value.length : 0,
        inventoryValue
      })

    } catch (err) {
      setError('Failed to load dashboard data')
      console.error('Dashboard data loading error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const getTodaysSales = async (): Promise<number> => {
    try {
      const today = new Date().toISOString().split('T')[0]
      const endOfDay = new Date().toISOString()

      // Get sales for all branches for today
      const salesData = await salesService.getSalesTransactions(undefined, today, endOfDay)
      return salesData.reduce((sum, transaction) => sum + transaction.total_amount, 0)
    } catch (err) {
      console.warn('Failed to get today\'s sales:', err)
      return 0
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Dashboard Overview
              </h3>
              <div className="mt-2 max-w-xl text-sm text-gray-500">
                <p>
                  Welcome back, <strong>{profile?.full_name}</strong>! Here's your restaurant management overview.
                </p>
              </div>
            </div>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{error}</span>
              <button
                onClick={() => setError(null)}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
                aria-label="Close error message"
              >
                <span className="text-red-500">&times;</span>
              </button>
            </div>
          )}

          {/* Low Stock Alert */}
          {stats.lowStockItems > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="text-red-400 text-xl mr-3" aria-hidden="true">⚠️</div>
                <div>
                  <h3 className="text-lg font-medium text-red-800">Low Stock Alert</h3>
                  <p className="text-red-700">
                    {stats.lowStockItems} item{stats.lowStockItems !== 1 ? 's' : ''}
                    {stats.lowStockItems === 1 ? ' is' : ' are'} running low across all warehouses
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">📦</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Inventory Items
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.totalInventoryItems.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">👨‍🍳</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Active Productions
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.activeProductions.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">🏪</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Active Branches
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.activeBranches.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">💰</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Today's Sales
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(stats.todaysSales)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Recent Activity
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Latest updates from your restaurant operations
              </p>
            </div>
            <div className="border-t border-gray-200">
              <div className="px-4 py-4 text-center text-gray-500">
                No recent activity to display. Start using the system to see updates here.
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Quick Actions
              </h3>
            </div>
            <div className="border-t border-gray-200 p-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <Link href="/inventory" className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 text-center transition-colors">
                  Check Inventory
                </Link>
                <Link href="/production" className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center transition-colors">
                  Start Production
                </Link>
                <Link href="/reports" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-center transition-colors">
                  View Reports
                </Link>
                <Link href="/branches" className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-center transition-colors">
                  Manage Branches
                </Link>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
