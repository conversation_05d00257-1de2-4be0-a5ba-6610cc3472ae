import { UserRole } from './supabase'

export interface InventoryPermissions {
  canViewInventory: boolean
  canAddStock: boolean
  canUpdateStock: boolean
  canDeleteStock: boolean
  canTransferStock: boolean
  canApproveTransfers: boolean
  canCompleteTransfers: boolean
  canCancelTransfers: boolean
  canBulkUpdate: boolean
  canViewAnalytics: boolean
  canManageWarehouses: boolean
  canViewAllWarehouses: boolean
  canViewCosts: boolean
  canExportData: boolean
}

export interface TransferPermissions {
  canCreateTransfer: boolean
  canApproveTransfer: boolean
  canCompleteTransfer: boolean
  canCancelTransfer: boolean
  canViewAllTransfers: boolean
  canViewOwnTransfers: boolean
}

export interface ReportPermissions {
  canViewInventoryReports: boolean
  canViewFinancialReports: boolean
  canViewOperationalReports: boolean
  canExportReports: boolean
  canScheduleReports: boolean
}

/**
 * Get inventory permissions based on user role
 */
export function getInventoryPermissions(role: UserRole): InventoryPermissions {
  const basePermissions: InventoryPermissions = {
    canViewInventory: false,
    canAddStock: false,
    canUpdateStock: false,
    canDeleteStock: false,
    canTransferStock: false,
    canApproveTransfers: false,
    canCompleteTransfers: false,
    canCancelTransfers: false,
    canBulkUpdate: false,
    canViewAnalytics: false,
    canManageWarehouses: false,
    canViewAllWarehouses: false,
    canViewCosts: false,
    canExportData: false
  }

  switch (role) {
    case 'admin':
      return {
        ...basePermissions,
        canViewInventory: true,
        canAddStock: true,
        canUpdateStock: true,
        canDeleteStock: true,
        canTransferStock: true,
        canApproveTransfers: true,
        canCompleteTransfers: true,
        canCancelTransfers: true,
        canBulkUpdate: true,
        canViewAnalytics: true,
        canManageWarehouses: true,
        canViewAllWarehouses: true,
        canViewCosts: true,
        canExportData: true
      }

    case 'manager':
      return {
        ...basePermissions,
        canViewInventory: true,
        canAddStock: true,
        canUpdateStock: true,
        canDeleteStock: false, // Managers cannot delete stock records
        canTransferStock: true,
        canApproveTransfers: true,
        canCompleteTransfers: true,
        canCancelTransfers: true,
        canBulkUpdate: true,
        canViewAnalytics: true,
        canManageWarehouses: false, // Cannot create/delete warehouses
        canViewAllWarehouses: true,
        canViewCosts: true,
        canExportData: true
      }

    case 'staff':
      return {
        ...basePermissions,
        canViewInventory: true,
        canAddStock: true,
        canUpdateStock: true,
        canDeleteStock: false,
        canTransferStock: true,
        canApproveTransfers: false, // Staff cannot approve transfers
        canCompleteTransfers: true,
        canCancelTransfers: false, // Staff cannot cancel transfers
        canBulkUpdate: false, // Staff cannot perform bulk updates
        canViewAnalytics: false, // Staff cannot view analytics
        canManageWarehouses: false,
        canViewAllWarehouses: false, // Staff can only see their assigned warehouses
        canViewCosts: false, // Staff cannot view cost information
        canExportData: false
      }

    case 'cashier':
      return {
        ...basePermissions,
        canViewInventory: true, // Read-only access for POS operations
        canAddStock: false,
        canUpdateStock: false,
        canDeleteStock: false,
        canTransferStock: false,
        canApproveTransfers: false,
        canCompleteTransfers: false,
        canCancelTransfers: false,
        canBulkUpdate: false,
        canViewAnalytics: false,
        canManageWarehouses: false,
        canViewAllWarehouses: false,
        canViewCosts: false,
        canExportData: false
      }

    default:
      return basePermissions
  }
}

/**
 * Get transfer permissions based on user role
 */
export function getTransferPermissions(role: UserRole): TransferPermissions {
  const basePermissions: TransferPermissions = {
    canCreateTransfer: false,
    canApproveTransfer: false,
    canCompleteTransfer: false,
    canCancelTransfer: false,
    canViewAllTransfers: false,
    canViewOwnTransfers: false
  }

  switch (role) {
    case 'admin':
      return {
        ...basePermissions,
        canCreateTransfer: true,
        canApproveTransfer: true,
        canCompleteTransfer: true,
        canCancelTransfer: true,
        canViewAllTransfers: true,
        canViewOwnTransfers: true
      }

    case 'manager':
      return {
        ...basePermissions,
        canCreateTransfer: true,
        canApproveTransfer: true,
        canCompleteTransfer: true,
        canCancelTransfer: true,
        canViewAllTransfers: true,
        canViewOwnTransfers: true
      }

    case 'staff':
      return {
        ...basePermissions,
        canCreateTransfer: true,
        canApproveTransfer: false,
        canCompleteTransfer: true,
        canCancelTransfer: false,
        canViewAllTransfers: false,
        canViewOwnTransfers: true
      }

    case 'cashier':
      return {
        ...basePermissions,
        canCreateTransfer: false,
        canApproveTransfer: false,
        canCompleteTransfer: false,
        canCancelTransfer: false,
        canViewAllTransfers: false,
        canViewOwnTransfers: false
      }

    default:
      return basePermissions
  }
}

/**
 * Get report permissions based on user role
 */
export function getReportPermissions(role: UserRole): ReportPermissions {
  const basePermissions: ReportPermissions = {
    canViewInventoryReports: false,
    canViewFinancialReports: false,
    canViewOperationalReports: false,
    canExportReports: false,
    canScheduleReports: false
  }

  switch (role) {
    case 'admin':
      return {
        ...basePermissions,
        canViewInventoryReports: true,
        canViewFinancialReports: true,
        canViewOperationalReports: true,
        canExportReports: true,
        canScheduleReports: true
      }

    case 'manager':
      return {
        ...basePermissions,
        canViewInventoryReports: true,
        canViewFinancialReports: true,
        canViewOperationalReports: true,
        canExportReports: true,
        canScheduleReports: false
      }

    case 'staff':
      return {
        ...basePermissions,
        canViewInventoryReports: true,
        canViewFinancialReports: false,
        canViewOperationalReports: false,
        canExportReports: false,
        canScheduleReports: false
      }

    case 'cashier':
      return {
        ...basePermissions,
        canViewInventoryReports: false,
        canViewFinancialReports: false,
        canViewOperationalReports: false,
        canExportReports: false,
        canScheduleReports: false
      }

    default:
      return basePermissions
  }
}

/**
 * Check if user has permission for a specific inventory action
 */
export function hasInventoryPermission(
  role: UserRole, 
  action: keyof InventoryPermissions
): boolean {
  const permissions = getInventoryPermissions(role)
  return permissions[action]
}

/**
 * Check if user has permission for a specific transfer action
 */
export function hasTransferPermission(
  role: UserRole, 
  action: keyof TransferPermissions
): boolean {
  const permissions = getTransferPermissions(role)
  return permissions[action]
}

/**
 * Check if user has permission for a specific report action
 */
export function hasReportPermission(
  role: UserRole, 
  action: keyof ReportPermissions
): boolean {
  const permissions = getReportPermissions(role)
  return permissions[action]
}

/**
 * Get all permissions for a user role
 */
export function getAllPermissions(role: UserRole) {
  return {
    inventory: getInventoryPermissions(role),
    transfers: getTransferPermissions(role),
    reports: getReportPermissions(role)
  }
}

/**
 * Validate if user can perform an action and throw error if not
 */
export function validatePermission(
  role: UserRole,
  permissionType: 'inventory' | 'transfer' | 'report',
  action: string,
  actionDescription?: string
): void {
  let hasPermission = false

  switch (permissionType) {
    case 'inventory':
      hasPermission = hasInventoryPermission(role, action as keyof InventoryPermissions)
      break
    case 'transfer':
      hasPermission = hasTransferPermission(role, action as keyof TransferPermissions)
      break
    case 'report':
      hasPermission = hasReportPermission(role, action as keyof ReportPermissions)
      break
  }

  if (!hasPermission) {
    throw new Error(
      `Access denied: ${role} role does not have permission to ${actionDescription || action}`
    )
  }
}
