'use client'

import { useState, useEffect } from 'react'
import { inventoryService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface Transfer {
  id: string
  status: string
  requested_at: string
  notes?: string
  total_items: number
  from_warehouse: {
    id: string
    name: string
    code: string
  }
  to_warehouse: {
    id: string
    name: string
    code: string
  }
  requester: {
    id: string
    full_name: string
  }
  transfer_items: Array<{
    id: string
    requested_quantity: number
    approved_quantity?: number
    unit: string
    notes?: string
    ingredient: {
      id: string
      name: string
      code: string
    }
  }>
}

interface TransferApprovalModalProps {
  isOpen: boolean
  onClose: () => void
  transfer: Transfer | null
  onTransferUpdated: () => void
}

export default function TransferApprovalModal({ 
  isOpen, 
  onClose, 
  transfer, 
  onTransferUpdated 
}: TransferApprovalModalProps) {
  const { user } = useAuth()
  const [approvedItems, setApprovedItems] = useState<Array<{
    id: string
    approved_quantity: number
  }>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [stockInfo, setStockInfo] = useState<Record<string, number>>({})

  useEffect(() => {
    if (isOpen && transfer) {
      // Initialize approved quantities with requested quantities
      const initialApprovals = transfer.transfer_items.map(item => ({
        id: item.id,
        approved_quantity: item.requested_quantity
      }))
      setApprovedItems(initialApprovals)
      setError('')
      loadStockInfo()
    }
  }, [isOpen, transfer])

  const loadStockInfo = async () => {
    if (!transfer) return

    try {
      const stockData: Record<string, number> = {}
      
      // Get current stock for each ingredient in the source warehouse
      for (const item of transfer.transfer_items) {
        const inventory = await inventoryService.getWarehouseInventory(transfer.from_warehouse.id)
        const inventoryItem = inventory.find(inv => inv.ingredient.id === item.ingredient.id)
        stockData[item.ingredient.id] = inventoryItem?.current_stock || 0
      }
      
      setStockInfo(stockData)
    } catch (err) {
      console.error('Failed to load stock info:', err)
    }
  }

  const handleApprovedQuantityChange = (itemId: string, quantity: number) => {
    setApprovedItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { ...item, approved_quantity: Math.max(0, quantity) }
          : item
      )
    )
  }

  const handleApprove = async () => {
    if (!transfer || !user?.id) {
      setError('Missing required information')
      return
    }

    // Validate approved quantities
    for (const approvedItem of approvedItems) {
      const transferItem = transfer.transfer_items.find(item => item.id === approvedItem.id)
      if (!transferItem) continue

      const availableStock = stockInfo[transferItem.ingredient.id] || 0
      if (approvedItem.approved_quantity > availableStock) {
        setError(`Insufficient stock for ${transferItem.ingredient.name}. Available: ${availableStock}, Approved: ${approvedItem.approved_quantity}`)
        return
      }

      if (approvedItem.approved_quantity <= 0) {
        setError(`Approved quantity must be greater than 0 for ${transferItem.ingredient.name}`)
        return
      }
    }

    setIsLoading(true)
    setError('')

    try {
      await inventoryService.approveTransfer(transfer.id, user.id, approvedItems)
      onTransferUpdated()
      handleClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to approve transfer')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setApprovedItems([])
    setError('')
    setStockInfo({})
    onClose()
  }

  if (!isOpen || !transfer) return null

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTotalApprovedItems = () => {
    return approvedItems.filter(item => item.approved_quantity > 0).length
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Approve Transfer Request</h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Transfer Summary */}
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900">Transfer Details</h4>
                <p className="text-sm text-gray-600">ID: #{transfer.id.slice(-8)}</p>
                <p className="text-sm text-gray-600">Requested: {formatDate(transfer.requested_at)}</p>
                <p className="text-sm text-gray-600">By: {transfer.requester.full_name}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Route</h4>
                <p className="text-sm text-gray-600">
                  From: {transfer.from_warehouse.name} ({transfer.from_warehouse.code})
                </p>
                <p className="text-sm text-gray-600">
                  To: {transfer.to_warehouse.name} ({transfer.to_warehouse.code})
                </p>
              </div>
            </div>
            {transfer.notes && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-900">Notes</h4>
                <p className="text-sm text-gray-600">{transfer.notes}</p>
              </div>
            )}
          </div>

          {error && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Items to Approve */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Items to Approve</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ingredient
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requested
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Available Stock
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approve Quantity
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transfer.transfer_items.map((item) => {
                    const approvedItem = approvedItems.find(a => a.id === item.id)
                    const availableStock = stockInfo[item.ingredient.id] || 0
                    const approvedQuantity = approvedItem?.approved_quantity || 0
                    const isOverStock = approvedQuantity > availableStock

                    return (
                      <tr key={item.id} className={isOverStock ? 'bg-red-50' : ''}>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.ingredient.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              Code: {item.ingredient.code}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.requested_quantity.toLocaleString()} {item.unit}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`text-sm ${isOverStock ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                            {availableStock.toLocaleString()} {item.unit}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            step="0.001"
                            min="0"
                            max={availableStock}
                            value={approvedQuantity}
                            onChange={(e) => handleApprovedQuantityChange(item.id, parseFloat(e.target.value) || 0)}
                            className={`w-24 border rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                              isOverStock ? 'border-red-300 bg-red-50' : 'border-gray-300'
                            }`}
                          />
                          <span className="ml-1 text-sm text-gray-500">{item.unit}</span>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-500">
                          {item.notes || '-'}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Approval Summary */}
          <div className="mb-6 p-4 bg-blue-50 rounded-md">
            <h4 className="font-medium text-blue-900">Approval Summary</h4>
            <p className="text-sm text-blue-800">
              Approving {getTotalApprovedItems()} of {transfer.total_items} requested items
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              onClick={handleApprove}
              disabled={isLoading || getTotalApprovedItems() === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Approving...' : 'Approve Transfer'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
