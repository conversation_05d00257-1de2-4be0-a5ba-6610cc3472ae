import { supabase } from './supabase'

export type AuditAction = 
  | 'inventory_create'
  | 'inventory_update'
  | 'inventory_delete'
  | 'stock_add'
  | 'stock_reduce'
  | 'stock_adjust'
  | 'stock_waste'
  | 'transfer_create'
  | 'transfer_approve'
  | 'transfer_complete'
  | 'transfer_cancel'
  | 'bulk_update'
  | 'warehouse_create'
  | 'warehouse_update'
  | 'warehouse_delete'
  | 'ingredient_create'
  | 'ingredient_update'
  | 'ingredient_delete'
  | 'user_login'
  | 'user_logout'
  | 'permission_change'
  | 'system_backup'
  | 'data_export'

export interface AuditLogEntry {
  id?: string
  action: AuditAction
  entity_type: string
  entity_id: string
  user_id: string
  user_role: string
  details: Record<string, any>
  ip_address?: string
  user_agent?: string
  timestamp: string
  branch_id?: string
  warehouse_id?: string
}

export interface AuditLogFilter {
  action?: AuditAction
  entity_type?: string
  entity_id?: string
  user_id?: string
  start_date?: string
  end_date?: string
  branch_id?: string
  warehouse_id?: string
  limit?: number
}

/**
 * Create an audit log entry
 */
export async function createAuditLog(entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {
  try {
    const auditEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date().toISOString()
    }

    const { error } = await supabase
      .from('audit_logs')
      .insert(auditEntry)

    if (error) {
      console.error('Failed to create audit log:', error)
      // Don't throw error to avoid breaking the main operation
    }
  } catch (error) {
    console.error('Audit logging error:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Get audit logs with filtering
 */
export async function getAuditLogs(filter: AuditLogFilter = {}): Promise<AuditLogEntry[]> {
  try {
    let query = supabase
      .from('audit_logs')
      .select(`
        *,
        user:profiles!user_id(full_name, email),
        branch:branches(name),
        warehouse:warehouses(name)
      `)

    // Apply filters
    if (filter.action) {
      query = query.eq('action', filter.action)
    }

    if (filter.entity_type) {
      query = query.eq('entity_type', filter.entity_type)
    }

    if (filter.entity_id) {
      query = query.eq('entity_id', filter.entity_id)
    }

    if (filter.user_id) {
      query = query.eq('user_id', filter.user_id)
    }

    if (filter.branch_id) {
      query = query.eq('branch_id', filter.branch_id)
    }

    if (filter.warehouse_id) {
      query = query.eq('warehouse_id', filter.warehouse_id)
    }

    if (filter.start_date) {
      query = query.gte('timestamp', filter.start_date)
    }

    if (filter.end_date) {
      query = query.lte('timestamp', filter.end_date)
    }

    // Order by timestamp descending and apply limit
    query = query.order('timestamp', { ascending: false })

    if (filter.limit) {
      query = query.limit(filter.limit)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch audit logs: ${error.message}`)
    }

    return data || []
  } catch (error) {
    console.error('Error fetching audit logs:', error)
    throw error
  }
}

/**
 * Log inventory stock change
 */
export async function logStockChange(
  action: 'stock_add' | 'stock_reduce' | 'stock_adjust' | 'stock_waste',
  inventoryId: string,
  userId: string,
  userRole: string,
  details: {
    ingredient_name: string
    warehouse_name: string
    previous_stock: number
    new_stock: number
    quantity_changed: number
    unit: string
    notes?: string
  },
  warehouseId?: string,
  branchId?: string
): Promise<void> {
  await createAuditLog({
    action,
    entity_type: 'inventory',
    entity_id: inventoryId,
    user_id: userId,
    user_role: userRole,
    details,
    warehouse_id: warehouseId,
    branch_id: branchId
  })
}

/**
 * Log transfer operation
 */
export async function logTransferOperation(
  action: 'transfer_create' | 'transfer_approve' | 'transfer_complete' | 'transfer_cancel',
  transferId: string,
  userId: string,
  userRole: string,
  details: {
    from_warehouse: string
    to_warehouse: string
    items_count: number
    total_quantity?: number
    reason?: string
  },
  warehouseId?: string,
  branchId?: string
): Promise<void> {
  await createAuditLog({
    action,
    entity_type: 'transfer',
    entity_id: transferId,
    user_id: userId,
    user_role: userRole,
    details,
    warehouse_id: warehouseId,
    branch_id: branchId
  })
}

/**
 * Log bulk operation
 */
export async function logBulkOperation(
  userId: string,
  userRole: string,
  details: {
    operation_type: string
    items_affected: number
    success_count: number
    failure_count: number
    warehouse_name?: string
  },
  warehouseId?: string,
  branchId?: string
): Promise<void> {
  await createAuditLog({
    action: 'bulk_update',
    entity_type: 'inventory',
    entity_id: 'bulk_operation',
    user_id: userId,
    user_role: userRole,
    details,
    warehouse_id: warehouseId,
    branch_id: branchId
  })
}

/**
 * Log user authentication
 */
export async function logUserAuth(
  action: 'user_login' | 'user_logout',
  userId: string,
  userRole: string,
  details: {
    email: string
    login_method?: string
    session_duration?: number
  },
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await createAuditLog({
    action,
    entity_type: 'user',
    entity_id: userId,
    user_id: userId,
    user_role: userRole,
    details,
    ip_address: ipAddress,
    user_agent: userAgent
  })
}

/**
 * Log data export
 */
export async function logDataExport(
  userId: string,
  userRole: string,
  details: {
    export_type: string
    data_range: string
    file_format: string
    record_count: number
    filters_applied?: Record<string, any>
  },
  branchId?: string
): Promise<void> {
  await createAuditLog({
    action: 'data_export',
    entity_type: 'system',
    entity_id: 'data_export',
    user_id: userId,
    user_role: userRole,
    details,
    branch_id: branchId
  })
}

/**
 * Get audit summary for a specific time period
 */
export async function getAuditSummary(
  startDate: string,
  endDate: string,
  branchId?: string
): Promise<{
  totalActions: number
  actionBreakdown: Record<AuditAction, number>
  userActivity: Record<string, number>
  topActions: Array<{ action: AuditAction; count: number }>
}> {
  try {
    const filter: AuditLogFilter = {
      start_date: startDate,
      end_date: endDate,
      branch_id: branchId
    }

    const logs = await getAuditLogs(filter)

    const actionBreakdown: Record<string, number> = {}
    const userActivity: Record<string, number> = {}

    logs.forEach(log => {
      // Count actions
      actionBreakdown[log.action] = (actionBreakdown[log.action] || 0) + 1
      
      // Count user activity
      userActivity[log.user_id] = (userActivity[log.user_id] || 0) + 1
    })

    // Get top actions
    const topActions = Object.entries(actionBreakdown)
      .map(([action, count]) => ({ action: action as AuditAction, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return {
      totalActions: logs.length,
      actionBreakdown: actionBreakdown as Record<AuditAction, number>,
      userActivity,
      topActions
    }
  } catch (error) {
    console.error('Error generating audit summary:', error)
    throw error
  }
}

/**
 * Clean up old audit logs (for maintenance)
 */
export async function cleanupOldAuditLogs(daysToKeep: number = 365): Promise<number> {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    const { data, error } = await supabase
      .from('audit_logs')
      .delete()
      .lt('timestamp', cutoffDate.toISOString())
      .select('id')

    if (error) {
      throw new Error(`Failed to cleanup audit logs: ${error.message}`)
    }

    return data?.length || 0
  } catch (error) {
    console.error('Error cleaning up audit logs:', error)
    throw error
  }
}
