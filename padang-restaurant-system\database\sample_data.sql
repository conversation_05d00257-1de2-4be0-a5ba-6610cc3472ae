-- Sample data for Padang Restaurant Management System
-- This file contains realistic sample data for testing and development

-- Insert ingredient categories
INSERT INTO ingredient_categories (id, name, description) VALUES
    (uuid_generate_v4(), 'Spices & Seasonings', 'Traditional Padang spices and seasonings'),
    (uuid_generate_v4(), 'Meat & Poultry', 'Fresh meat and poultry products'),
    (uuid_generate_v4(), 'Vegetables', 'Fresh vegetables and herbs'),
    (uuid_generate_v4(), 'Dairy & Eggs', 'Dairy products and eggs'),
    (uuid_generate_v4(), 'Grains & Starches', 'Rice, flour, and other starches'),
    (uuid_generate_v4(), 'Oils & Fats', 'Cooking oils and fats'),
    (uuid_generate_v4(), 'Beverages', 'Drinks and beverage ingredients');

-- Insert sample branches
INSERT INTO branches (id, name, code, address, phone, email, operating_hours) VALUES
    (uuid_generate_v4(), 'Padang Central', 'PC001', 'Jl. Sudirman No. 123, Jakarta Pusat', '+62-21-1234567', '<EMAIL>', '{"open": "06:00", "close": "22:00"}'),
    (uuid_generate_v4(), 'Padang Kelapa Gading', 'PKG001', 'Mall Kelapa Gading Lt. 3, Jakarta Utara', '+62-21-2345678', '<EMAIL>', '{"open": "10:00", "close": "22:00"}'),
    (uuid_generate_v4(), 'Padang Pondok Indah', 'PPI001', 'Pondok Indah Mall Lt. 2, Jakarta Selatan', '+62-21-3456789', '<EMAIL>', '{"open": "10:00", "close": "22:00"}');

-- Insert warehouses
INSERT INTO warehouses (id, name, code, address, type, branch_id) VALUES
    (uuid_generate_v4(), 'Central Warehouse', 'CW001', 'Jl. Industri No. 45, Jakarta Timur', 'central', NULL),
    (uuid_generate_v4(), 'Kelapa Gading Storage', 'KGS001', 'Mall Kelapa Gading Basement, Jakarta Utara', 'branch', (SELECT id FROM branches WHERE code = 'PKG001')),
    (uuid_generate_v4(), 'Pondok Indah Storage', 'PIS001', 'Pondok Indah Mall Basement, Jakarta Selatan', 'branch', (SELECT id FROM branches WHERE code = 'PPI001'));

-- Insert kitchens
INSERT INTO kitchens (id, name, code, type, branch_id, warehouse_id) VALUES
    (uuid_generate_v4(), 'Central Kitchen', 'CK001', 'central', NULL, (SELECT id FROM warehouses WHERE code = 'CW001')),
    (uuid_generate_v4(), 'Kelapa Gading Kitchen', 'KGK001', 'branch', (SELECT id FROM branches WHERE code = 'PKG001'), (SELECT id FROM warehouses WHERE code = 'KGS001')),
    (uuid_generate_v4(), 'Pondok Indah Kitchen', 'PIK001', 'branch', (SELECT id FROM branches WHERE code = 'PPI001'), (SELECT id FROM warehouses WHERE code = 'PIS001'));

-- Insert sample ingredients
INSERT INTO ingredients (id, name, code, category_id, unit, cost_per_unit, shelf_life_days, storage_requirements) VALUES
    (uuid_generate_v4(), 'Beef Chuck', 'BEEF001', (SELECT id FROM ingredient_categories WHERE name = 'Meat & Poultry'), 'kg', 120000, 3, 'Refrigerated 0-4°C'),
    (uuid_generate_v4(), 'Chicken Whole', 'CHKN001', (SELECT id FROM ingredient_categories WHERE name = 'Meat & Poultry'), 'kg', 35000, 2, 'Refrigerated 0-4°C'),
    (uuid_generate_v4(), 'Coconut Milk', 'COCM001', (SELECT id FROM ingredient_categories WHERE name = 'Dairy & Eggs'), 'liter', 25000, 7, 'Refrigerated after opening'),
    (uuid_generate_v4(), 'Red Chili', 'CHIL001', (SELECT id FROM ingredient_categories WHERE name = 'Spices & Seasonings'), 'kg', 45000, 5, 'Cool dry place'),
    (uuid_generate_v4(), 'Galangal', 'GALA001', (SELECT id FROM ingredient_categories WHERE name = 'Spices & Seasonings'), 'kg', 30000, 14, 'Cool dry place'),
    (uuid_generate_v4(), 'Lemongrass', 'LEMG001', (SELECT id FROM ingredient_categories WHERE name = 'Spices & Seasonings'), 'kg', 20000, 7, 'Refrigerated'),
    (uuid_generate_v4(), 'Palm Oil', 'PALM001', (SELECT id FROM ingredient_categories WHERE name = 'Oils & Fats'), 'liter', 15000, 365, 'Room temperature'),
    (uuid_generate_v4(), 'Jasmine Rice', 'RICE001', (SELECT id FROM ingredient_categories WHERE name = 'Grains & Starches'), 'kg', 12000, 365, 'Dry storage'),
    (uuid_generate_v4(), 'Shallots', 'SHAL001', (SELECT id FROM ingredient_categories WHERE name = 'Vegetables'), 'kg', 35000, 30, 'Cool dry place'),
    (uuid_generate_v4(), 'Garlic', 'GARL001', (SELECT id FROM ingredient_categories WHERE name = 'Vegetables'), 'kg', 40000, 30, 'Cool dry place');

-- Insert sample recipes (traditional Padang dishes)
INSERT INTO recipes (id, name, code, description, category, serving_size, prep_time_minutes, cook_time_minutes, difficulty_level, instructions) VALUES
    (uuid_generate_v4(), 'Rendang Daging', 'RND001', 'Traditional slow-cooked beef in coconut milk and spices', 'main_dish', 8, 30, 180, 4, 'Slow cook beef with coconut milk, galangal, lemongrass, and spices until tender and dry'),
    (uuid_generate_v4(), 'Ayam Pop', 'AYM001', 'Padang-style fried chicken with special seasoning', 'main_dish', 4, 20, 45, 3, 'Marinate chicken, steam, then deep fry until golden'),
    (uuid_generate_v4(), 'Gulai Kambing', 'GUL001', 'Spicy goat curry with coconut milk', 'main_dish', 6, 25, 120, 4, 'Cook goat meat with curry spices and coconut milk'),
    (uuid_generate_v4(), 'Nasi Putih', 'NSI001', 'Steamed white rice', 'staple', 1, 5, 25, 1, 'Steam jasmine rice until fluffy'),
    (uuid_generate_v4(), 'Sambal Lado', 'SBL001', 'Spicy red chili sauce', 'condiment', 10, 15, 10, 2, 'Blend red chilies with shallots and garlic, then sauté');

-- Insert recipe ingredients
INSERT INTO recipe_ingredients (recipe_id, ingredient_id, quantity, unit) VALUES
    -- Rendang Daging ingredients
    ((SELECT id FROM recipes WHERE code = 'RND001'), (SELECT id FROM ingredients WHERE code = 'BEEF001'), 1.0, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'RND001'), (SELECT id FROM ingredients WHERE code = 'COCM001'), 0.5, 'liter'),
    ((SELECT id FROM recipes WHERE code = 'RND001'), (SELECT id FROM ingredients WHERE code = 'GALA001'), 0.05, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'RND001'), (SELECT id FROM ingredients WHERE code = 'LEMG001'), 0.03, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'RND001'), (SELECT id FROM ingredients WHERE code = 'CHIL001'), 0.1, 'kg'),
    
    -- Ayam Pop ingredients
    ((SELECT id FROM recipes WHERE code = 'AYM001'), (SELECT id FROM ingredients WHERE code = 'CHKN001'), 1.0, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'AYM001'), (SELECT id FROM ingredients WHERE code = 'PALM001'), 0.2, 'liter'),
    ((SELECT id FROM recipes WHERE code = 'AYM001'), (SELECT id FROM ingredients WHERE code = 'GARL001'), 0.02, 'kg'),
    
    -- Nasi Putih ingredients
    ((SELECT id FROM recipes WHERE code = 'NSI001'), (SELECT id FROM ingredients WHERE code = 'RICE001'), 0.1, 'kg'),
    
    -- Sambal Lado ingredients
    ((SELECT id FROM recipes WHERE code = 'SBL001'), (SELECT id FROM ingredients WHERE code = 'CHIL001'), 0.2, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'SBL001'), (SELECT id FROM ingredients WHERE code = 'SHAL001'), 0.05, 'kg'),
    ((SELECT id FROM recipes WHERE code = 'SBL001'), (SELECT id FROM ingredients WHERE code = 'GARL001'), 0.02, 'kg');

-- Insert menu items
INSERT INTO menu_items (id, recipe_id, name, description, category, base_price, is_signature_dish, spice_level) VALUES
    (uuid_generate_v4(), (SELECT id FROM recipes WHERE code = 'RND001'), 'Rendang Daging', 'Our signature slow-cooked beef rendang', 'main_dish', 45000, true, 3),
    (uuid_generate_v4(), (SELECT id FROM recipes WHERE code = 'AYM001'), 'Ayam Pop', 'Crispy Padang-style fried chicken', 'main_dish', 25000, false, 2),
    (uuid_generate_v4(), (SELECT id FROM recipes WHERE code = 'GUL001'), 'Gulai Kambing', 'Spicy goat curry with rich coconut milk', 'main_dish', 55000, true, 4),
    (uuid_generate_v4(), (SELECT id FROM recipes WHERE code = 'NSI001'), 'Nasi Putih', 'Steamed jasmine rice', 'staple', 5000, false, 0),
    (uuid_generate_v4(), (SELECT id FROM recipes WHERE code = 'SBL001'), 'Sambal Lado', 'Traditional spicy chili sauce', 'condiment', 3000, false, 5);

-- Insert branch menu pricing (same prices for all branches initially)
INSERT INTO branch_menu_pricing (branch_id, menu_item_id, price, daily_quota)
SELECT 
    b.id as branch_id,
    m.id as menu_item_id,
    m.base_price as price,
    CASE 
        WHEN m.name = 'Rendang Daging' THEN 50
        WHEN m.name = 'Ayam Pop' THEN 100
        WHEN m.name = 'Gulai Kambing' THEN 30
        WHEN m.name = 'Nasi Putih' THEN 500
        WHEN m.name = 'Sambal Lado' THEN 200
    END as daily_quota
FROM branches b
CROSS JOIN menu_items m;

-- Insert sample inventory for central warehouse
INSERT INTO inventory (warehouse_id, ingredient_id, current_stock, minimum_stock, maximum_stock, reorder_point)
SELECT 
    (SELECT id FROM warehouses WHERE code = 'CW001') as warehouse_id,
    i.id as ingredient_id,
    CASE 
        WHEN i.code = 'BEEF001' THEN 50.0
        WHEN i.code = 'CHKN001' THEN 100.0
        WHEN i.code = 'COCM001' THEN 200.0
        WHEN i.code = 'RICE001' THEN 500.0
        ELSE 25.0
    END as current_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 10.0
        WHEN i.code = 'CHKN001' THEN 20.0
        WHEN i.code = 'COCM001' THEN 50.0
        WHEN i.code = 'RICE001' THEN 100.0
        ELSE 5.0
    END as minimum_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 100.0
        WHEN i.code = 'CHKN001' THEN 200.0
        WHEN i.code = 'COCM001' THEN 500.0
        WHEN i.code = 'RICE001' THEN 1000.0
        ELSE 50.0
    END as maximum_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 15.0
        WHEN i.code = 'CHKN001' THEN 30.0
        WHEN i.code = 'COCM001' THEN 75.0
        WHEN i.code = 'RICE001' THEN 150.0
        ELSE 10.0
    END as reorder_point
FROM ingredients i;

-- Insert sample inventory for branch warehouses (smaller quantities)
INSERT INTO inventory (warehouse_id, ingredient_id, current_stock, minimum_stock, maximum_stock, reorder_point)
SELECT 
    w.id as warehouse_id,
    i.id as ingredient_id,
    CASE 
        WHEN i.code = 'BEEF001' THEN 15.0
        WHEN i.code = 'CHKN001' THEN 30.0
        WHEN i.code = 'COCM001' THEN 50.0
        WHEN i.code = 'RICE001' THEN 100.0
        ELSE 10.0
    END as current_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 5.0
        WHEN i.code = 'CHKN001' THEN 10.0
        WHEN i.code = 'COCM001' THEN 15.0
        WHEN i.code = 'RICE001' THEN 25.0
        ELSE 3.0
    END as minimum_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 30.0
        WHEN i.code = 'CHKN001' THEN 60.0
        WHEN i.code = 'COCM001' THEN 100.0
        WHEN i.code = 'RICE001' THEN 200.0
        ELSE 20.0
    END as maximum_stock,
    CASE 
        WHEN i.code = 'BEEF001' THEN 8.0
        WHEN i.code = 'CHKN001' THEN 15.0
        WHEN i.code = 'COCM001' THEN 25.0
        WHEN i.code = 'RICE001' THEN 40.0
        ELSE 5.0
    END as reorder_point
FROM warehouses w
CROSS JOIN ingredients i
WHERE w.type = 'branch';

-- Insert sample production batches
INSERT INTO production_batches (kitchen_id, recipe_id, batch_number, planned_quantity, actual_quantity, status, quality_score, planned_start_time, actual_start_time, actual_end_time)
VALUES
    ((SELECT id FROM kitchens WHERE code = 'CK001'), (SELECT id FROM recipes WHERE code = 'RND001'), 'RND-20241203-001', 20, 18, 'completed', 9, NOW() - INTERVAL '4 hours', NOW() - INTERVAL '4 hours', NOW() - INTERVAL '1 hour'),
    ((SELECT id FROM kitchens WHERE code = 'CK001'), (SELECT id FROM recipes WHERE code = 'AYM001'), 'AYM-20241203-001', 50, 48, 'completed', 8, NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '30 minutes'),
    ((SELECT id FROM kitchens WHERE code = 'KGK001'), (SELECT id FROM recipes WHERE code = 'NSI001'), 'NSI-20241203-001', 100, 100, 'completed', 10, NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour', NOW() - INTERVAL '30 minutes');

-- Note: Additional sample data for sales transactions, stock movements, etc. can be added as needed
-- This provides a good foundation for testing the system
