# Database Setup for Padang Restaurant Management System

This directory contains the database schema and setup scripts for the Padang Restaurant Management System.

## Files Overview

- `schema.sql` - Complete database schema with all tables, indexes, triggers, and RLS policies
- `sample_data.sql` - Sample data for testing and development
- `setup.js` - Automated setup script (Node.js)
- `README.md` - This documentation file

## Database Schema Overview

The database is designed to support a multi-location Padang restaurant chain with the following key entities:

### Core Tables

1. **profiles** - User profiles extending Supabase auth
2. **branches** - Restaurant branch locations
3. **warehouses** - Storage facilities (central and branch-specific)
4. **kitchens** - Cooking facilities (central and branch-specific)

### Inventory Management

5. **ingredient_categories** - Categories for organizing ingredients
6. **ingredients** - Raw materials and ingredients
7. **inventory** - Stock levels per warehouse
8. **stock_movements** - All inventory transactions
9. **warehouse_transfers** - Inter-warehouse transfers

### Production Management

10. **recipes** - Cooking recipes for Padang dishes
11. **recipe_ingredients** - Ingredients required for each recipe
12. **production_batches** - Production tracking
13. **batch_ingredients_used** - Actual ingredients used in production

### Sales Management

14. **menu_items** - Items available for sale
15. **branch_menu_pricing** - Branch-specific pricing
16. **sales_transactions** - Customer orders
17. **sales_transaction_items** - Individual items in orders
18. **daily_sales_summaries** - Daily sales aggregation

## Setup Instructions

### Method 1: Automated Setup (Recommended)

1. **Install dependencies:**
   ```bash
   npm install @supabase/supabase-js dotenv
   ```

2. **Add service role key to environment:**
   Add the following to your `.env.local` file:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```
   
   You can find this key in your Supabase project dashboard under Settings > API.

3. **Run the setup script:**
   ```bash
   node database/setup.js
   ```

### Method 2: Manual Setup

1. **Go to your Supabase project dashboard**
2. **Navigate to SQL Editor**
3. **Execute schema.sql:**
   - Copy the entire contents of `schema.sql`
   - Paste into the SQL editor
   - Click "Run"
4. **Execute sample_data.sql:**
   - Copy the entire contents of `sample_data.sql`
   - Paste into the SQL editor
   - Click "Run"

### Method 3: Using PostgreSQL Client

If you have a PostgreSQL client installed:

```bash
# Replace with your actual connection string
psql "postgresql://postgres:[password]@[host]:[port]/postgres" -f schema.sql
psql "postgresql://postgres:[password]@[host]:[port]/postgres" -f sample_data.sql
```

## Sample Data Included

The sample data includes:

- **3 restaurant branches** (Central, Kelapa Gading, Pondok Indah)
- **3 warehouses** (1 central, 2 branch-specific)
- **3 kitchens** (1 central, 2 branch-specific)
- **10 common ingredients** (beef, chicken, spices, etc.)
- **5 traditional recipes** (Rendang, Ayam Pop, Gulai, etc.)
- **5 menu items** with branch-specific pricing
- **Sample inventory** for all warehouses
- **Sample production batches**

## Security Features

The database includes comprehensive security features:

- **Row Level Security (RLS)** enabled on all sensitive tables
- **Role-based access control** (admin, manager, staff, cashier)
- **Branch-based data isolation** for multi-tenant operations
- **Automatic user profile creation** on registration
- **Audit trails** through stock movements and transaction logs

## Key Features

### Multi-Location Support
- Central and branch-specific warehouses
- Central and branch-specific kitchens
- Branch-specific menu pricing
- Inter-branch transfers

### Inventory Management
- Real-time stock tracking
- Minimum stock alerts
- Batch tracking with expiry dates
- Comprehensive movement history

### Production Management
- Recipe standardization
- Batch production tracking
- Quality control scoring
- Ingredient consumption tracking

### Sales Management
- Multi-payment method support
- Daily sales summaries
- Transaction-level tracking
- Branch performance metrics

## Maintenance

### Regular Tasks

1. **Clean old stock movements** (keep last 12 months)
2. **Archive completed production batches** (older than 3 months)
3. **Update ingredient costs** regularly
4. **Review and update minimum stock levels**

### Monitoring

Monitor these key metrics:
- Low stock alerts
- Failed transfers
- Production efficiency
- Sales performance by branch

## Troubleshooting

### Common Issues

1. **Permission denied errors:**
   - Ensure you're using the service role key for setup
   - Check RLS policies if users can't access data

2. **Foreign key constraint errors:**
   - Ensure parent records exist before inserting child records
   - Check the order of data insertion

3. **Duplicate key errors:**
   - Check for existing data before running sample_data.sql
   - Use UPSERT operations for updates

### Getting Help

If you encounter issues:
1. Check the Supabase logs in your dashboard
2. Verify your environment variables
3. Ensure your Supabase project has the required permissions
4. Check the PostgreSQL error messages for specific issues

## Next Steps

After setting up the database:

1. **Create your first admin user** through the application
2. **Configure your actual branches** and locations
3. **Set up your real inventory** and suppliers
4. **Create your restaurant's recipes** and menu items
5. **Train your staff** on the system

The sample data provides a good starting point, but you'll want to customize it for your specific restaurant operations.
