'use client'

import { useState, useEffect } from 'react'
import { inventoryService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface InventoryItem {
  id: string
  current_stock: number
  minimum_stock: number
  maximum_stock: number
  reorder_point: number
  ingredient: {
    id: string
    name: string
    code: string
    unit: string
    cost_per_unit: number
  }
  warehouse: {
    id: string
    name: string
    code: string
  }
}

interface BulkUpdateModalProps {
  isOpen: boolean
  onClose: () => void
  items: InventoryItem[]
  onBulkUpdated: () => void
}

interface BulkUpdateItem {
  inventoryId: string
  newStock: number
  movementType: string
  notes?: string
  selected: boolean
}

export default function BulkUpdateModal({ 
  isOpen, 
  onClose, 
  items, 
  onBulkUpdated 
}: BulkUpdateModalProps) {
  const { user } = useAuth()
  const [bulkItems, setBulkItems] = useState<BulkUpdateItem[]>([])
  const [globalMovementType, setGlobalMovementType] = useState('adjustment')
  const [globalNotes, setGlobalNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [selectAll, setSelectAll] = useState(false)

  useEffect(() => {
    if (isOpen && items.length > 0) {
      const initialBulkItems = items.map(item => ({
        inventoryId: item.id,
        newStock: item.current_stock,
        movementType: 'adjustment',
        notes: '',
        selected: false
      }))
      setBulkItems(initialBulkItems)
      setError('')
      setSelectAll(false)
      setGlobalMovementType('adjustment')
      setGlobalNotes('')
    }
  }, [isOpen, items])

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked)
    setBulkItems(prev => prev.map(item => ({ ...item, selected: checked })))
  }

  const handleItemSelect = (inventoryId: string, selected: boolean) => {
    setBulkItems(prev => 
      prev.map(item => 
        item.inventoryId === inventoryId 
          ? { ...item, selected }
          : item
      )
    )
    
    // Update select all state
    const updatedItems = bulkItems.map(item => 
      item.inventoryId === inventoryId ? { ...item, selected } : item
    )
    setSelectAll(updatedItems.every(item => item.selected))
  }

  const handleStockChange = (inventoryId: string, newStock: number) => {
    setBulkItems(prev => 
      prev.map(item => 
        item.inventoryId === inventoryId 
          ? { ...item, newStock: Math.max(0, newStock) }
          : item
      )
    )
  }

  const handleMovementTypeChange = (inventoryId: string, movementType: string) => {
    setBulkItems(prev => 
      prev.map(item => 
        item.inventoryId === inventoryId 
          ? { ...item, movementType }
          : item
      )
    )
  }

  const handleNotesChange = (inventoryId: string, notes: string) => {
    setBulkItems(prev => 
      prev.map(item => 
        item.inventoryId === inventoryId 
          ? { ...item, notes }
          : item
      )
    )
  }

  const applyGlobalSettings = () => {
    setBulkItems(prev => 
      prev.map(item => 
        item.selected 
          ? { 
              ...item, 
              movementType: globalMovementType,
              notes: globalNotes 
            }
          : item
      )
    )
  }

  const handleBulkUpdate = async () => {
    const selectedItems = bulkItems.filter(item => item.selected)
    
    if (selectedItems.length === 0) {
      setError('Please select at least one item to update')
      return
    }

    // Validate all selected items
    for (const item of selectedItems) {
      const inventoryItem = items.find(inv => inv.id === item.inventoryId)
      if (!inventoryItem) continue

      if (item.newStock < 0) {
        setError(`Stock cannot be negative for ${inventoryItem.ingredient.name}`)
        return
      }

      if (item.newStock > inventoryItem.maximum_stock && inventoryItem.maximum_stock > 0) {
        setError(`Stock cannot exceed maximum limit for ${inventoryItem.ingredient.name}`)
        return
      }
    }

    setIsLoading(true)
    setError('')

    try {
      const updates = selectedItems.map(item => ({
        inventoryId: item.inventoryId,
        newStock: item.newStock,
        movementType: item.movementType,
        notes: item.notes || globalNotes
      }))

      const result = await inventoryService.bulkUpdateStock(updates, user?.id)
      
      if (result.success) {
        onBulkUpdated()
        handleClose()
      } else {
        setError(`Bulk update completed with ${result.failureCount} failures. Check individual items for details.`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to perform bulk update')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setBulkItems([])
    setError('')
    setSelectAll(false)
    setGlobalMovementType('adjustment')
    setGlobalNotes('')
    onClose()
  }

  if (!isOpen || items.length === 0) return null

  const selectedCount = bulkItems.filter(item => item.selected).length

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Bulk Update Inventory</h3>
              <p className="text-sm text-gray-600">Update multiple inventory items at once</p>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Global Settings */}
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <h4 className="font-medium text-gray-900 mb-3">Global Settings</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Movement Type
                </label>
                <select
                  value={globalMovementType}
                  onChange={(e) => setGlobalMovementType(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="adjustment">Adjustment</option>
                  <option value="in">Stock In</option>
                  <option value="out">Stock Out</option>
                  <option value="waste">Waste</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Global Notes
                </label>
                <input
                  type="text"
                  value={globalNotes}
                  onChange={(e) => setGlobalNotes(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Notes for all selected items"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={applyGlobalSettings}
                  disabled={selectedCount === 0}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Apply to Selected ({selectedCount})
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Items Table */}
          <div className="mb-6">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ingredient
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Stock
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      New Stock
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Movement Type
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {items.map((item, index) => {
                    const bulkItem = bulkItems[index]
                    if (!bulkItem) return null

                    const stockChange = bulkItem.newStock - item.current_stock
                    const isOverMax = bulkItem.newStock > item.maximum_stock && item.maximum_stock > 0

                    return (
                      <tr key={item.id} className={bulkItem.selected ? 'bg-blue-50' : ''}>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={bulkItem.selected}
                            onChange={(e) => handleItemSelect(item.id, e.target.checked)}
                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          />
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.ingredient.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.ingredient.code} | {item.warehouse.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.current_stock.toLocaleString()} {item.ingredient.unit}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            step="0.001"
                            min="0"
                            value={bulkItem.newStock}
                            onChange={(e) => handleStockChange(item.id, parseFloat(e.target.value) || 0)}
                            className={`w-24 border rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                              isOverMax ? 'border-red-300 bg-red-50' : 'border-gray-300'
                            }`}
                          />
                          <span className="ml-1 text-sm text-gray-500">{item.ingredient.unit}</span>
                          {stockChange !== 0 && (
                            <div className={`text-xs ${stockChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {stockChange > 0 ? '+' : ''}{stockChange.toLocaleString()}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <select
                            value={bulkItem.movementType}
                            onChange={(e) => handleMovementTypeChange(item.id, e.target.value)}
                            className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                          >
                            <option value="adjustment">Adjustment</option>
                            <option value="in">Stock In</option>
                            <option value="out">Stock Out</option>
                            <option value="waste">Waste</option>
                          </select>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            value={bulkItem.notes}
                            onChange={(e) => handleNotesChange(item.id, e.target.value)}
                            className="w-32 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Optional notes"
                          />
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Summary */}
          <div className="mb-6 p-4 bg-blue-50 rounded-md">
            <h4 className="font-medium text-blue-900">Update Summary</h4>
            <p className="text-sm text-blue-800">
              {selectedCount} of {items.length} items selected for bulk update
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              onClick={handleBulkUpdate}
              disabled={isLoading || selectedCount === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Updating...' : `Update ${selectedCount} Items`}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
