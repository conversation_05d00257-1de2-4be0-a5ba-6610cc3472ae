/**
 * Database Setup Script for Padang Restaurant Management System
 * This script sets up the database schema and populates it with sample data
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY // You'll need to add this to .env.local

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runSQLFile(filename) {
  console.log(`Running ${filename}...`)
  
  try {
    const sqlContent = fs.readFileSync(path.join(__dirname, filename), 'utf8')
    
    // Split SQL content by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        if (error) {
          console.error(`Error executing statement: ${statement.substring(0, 100)}...`)
          console.error(error)
        }
      }
    }
    
    console.log(`✅ ${filename} executed successfully`)
  } catch (error) {
    console.error(`❌ Error running ${filename}:`, error.message)
    throw error
  }
}

async function setupDatabase() {
  console.log('🚀 Setting up Padang Restaurant Management System Database...\n')
  
  try {
    // First, create the exec_sql function if it doesn't exist
    const { error: funcError } = await supabase.rpc('exec_sql', { 
      sql: `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;
      `
    })
    
    if (funcError) {
      console.log('Creating exec_sql function manually...')
      // If the function doesn't exist, we'll need to create it through the Supabase dashboard
      console.log('Please run the following SQL in your Supabase SQL editor:')
      console.log(`
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;
      `)
      console.log('\nThen run this script again.')
      return
    }
    
    // Run schema setup
    await runSQLFile('schema.sql')
    
    // Run sample data
    await runSQLFile('sample_data.sql')
    
    console.log('\n🎉 Database setup completed successfully!')
    console.log('\nYour Padang Restaurant Management System is ready to use.')
    console.log('\nNext steps:')
    console.log('1. Create your first admin user through the application')
    console.log('2. Set up your branches and assign managers')
    console.log('3. Configure your inventory and recipes')
    console.log('4. Start managing your restaurant operations!')
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message)
    console.error('\nPlease check your Supabase configuration and try again.')
    process.exit(1)
  }
}

// Alternative method using direct SQL execution
async function setupDatabaseDirect() {
  console.log('🚀 Setting up database using direct SQL execution...\n')
  
  try {
    // Read and execute schema
    const schemaSQL = fs.readFileSync(path.join(__dirname, 'schema.sql'), 'utf8')
    console.log('Executing schema...')
    
    // For direct execution, you would need to use a PostgreSQL client
    // This is a placeholder for the actual implementation
    console.log('Schema SQL ready for execution in Supabase SQL editor')
    
    // Read sample data
    const sampleDataSQL = fs.readFileSync(path.join(__dirname, 'sample_data.sql'), 'utf8')
    console.log('Sample data SQL ready for execution')
    
    console.log('\n📋 Manual Setup Instructions:')
    console.log('1. Go to your Supabase project dashboard')
    console.log('2. Navigate to SQL Editor')
    console.log('3. Copy and paste the contents of schema.sql')
    console.log('4. Execute the schema SQL')
    console.log('5. Copy and paste the contents of sample_data.sql')
    console.log('6. Execute the sample data SQL')
    console.log('\nAlternatively, you can use a PostgreSQL client with your database connection string.')
    
  } catch (error) {
    console.error('Error reading SQL files:', error.message)
  }
}

// Check if we can use the RPC method or need manual setup
async function main() {
  try {
    // Test if we can connect to Supabase
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    
    if (error && error.code === '42P01') {
      // Table doesn't exist, proceed with setup
      await setupDatabase()
    } else if (error) {
      console.log('Cannot use automated setup. Using manual setup instructions...')
      await setupDatabaseDirect()
    } else {
      console.log('Database appears to be already set up.')
      console.log('If you want to reset the database, please drop all tables first.')
    }
  } catch (error) {
    console.log('Using manual setup method...')
    await setupDatabaseDirect()
  }
}

if (require.main === module) {
  main()
}

module.exports = { setupDatabase, setupDatabaseDirect }
