'use client'

interface Transfer {
  id: string
  status: string
  requested_at: string
  approved_at?: string
  completed_at?: string
  notes?: string
  total_items: number
  from_warehouse: {
    id: string
    name: string
    code: string
  }
  to_warehouse: {
    id: string
    name: string
    code: string
  }
  requester: {
    id: string
    full_name: string
  }
  transfer_items: Array<{
    id: string
    requested_quantity: number
    approved_quantity?: number
    unit: string
    notes?: string
    ingredient: {
      id: string
      name: string
      code: string
    }
  }>
}

interface TransferDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  transfer: Transfer | null
}

export default function TransferDetailsModal({ 
  isOpen, 
  onClose, 
  transfer 
}: TransferDetailsModalProps) {
  if (!isOpen || !transfer) return null

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
      approved: { color: 'bg-blue-100 text-blue-800', text: 'Approved' },
      in_transit: { color: 'bg-purple-100 text-purple-800', text: 'In Transit' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Completed' },
      cancelled: { color: 'bg-red-100 text-red-800', text: 'Cancelled' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    )
  }

  const getStatusTimeline = () => {
    const timeline = [
      {
        status: 'requested',
        label: 'Requested',
        date: transfer.requested_at,
        completed: true
      },
      {
        status: 'approved',
        label: 'Approved',
        date: transfer.approved_at,
        completed: !!transfer.approved_at
      },
      {
        status: 'completed',
        label: 'Completed',
        date: transfer.completed_at,
        completed: !!transfer.completed_at
      }
    ]

    return timeline
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Transfer Details</h3>
              <p className="text-sm text-gray-600">Transfer #{transfer.id.slice(-8)}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Transfer Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-medium text-gray-900 mb-3">Transfer Information</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status:</span>
                  {getStatusBadge(transfer.status)}
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Requested by:</span>
                  <span className="text-sm text-gray-900">{transfer.requester.full_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total items:</span>
                  <span className="text-sm text-gray-900">{transfer.total_items}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Requested:</span>
                  <span className="text-sm text-gray-900">{formatDate(transfer.requested_at)}</span>
                </div>
                {transfer.approved_at && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Approved:</span>
                    <span className="text-sm text-gray-900">{formatDate(transfer.approved_at)}</span>
                  </div>
                )}
                {transfer.completed_at && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completed:</span>
                    <span className="text-sm text-gray-900">{formatDate(transfer.completed_at)}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-medium text-gray-900 mb-3">Warehouse Route</h4>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600">From:</span>
                  <div className="text-sm text-gray-900 font-medium">
                    {transfer.from_warehouse.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    Code: {transfer.from_warehouse.code}
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="text-gray-400">↓</div>
                </div>
                <div>
                  <span className="text-sm text-gray-600">To:</span>
                  <div className="text-sm text-gray-900 font-medium">
                    {transfer.to_warehouse.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    Code: {transfer.to_warehouse.code}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Status Timeline */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Status Timeline</h4>
            <div className="flex items-center space-x-4">
              {getStatusTimeline().map((step, index) => (
                <div key={step.status} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    step.completed ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                  }`}>
                    {step.completed ? '✓' : index + 1}
                  </div>
                  <div className="ml-2">
                    <div className={`text-sm font-medium ${
                      step.completed ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.label}
                    </div>
                    {step.date && (
                      <div className="text-xs text-gray-500">
                        {formatDate(step.date)}
                      </div>
                    )}
                  </div>
                  {index < getStatusTimeline().length - 1 && (
                    <div className={`w-8 h-0.5 mx-2 ${
                      step.completed ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Transfer Items */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Transfer Items</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ingredient
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requested
                    </th>
                    {(transfer.status === 'approved' || transfer.status === 'completed') && (
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Approved
                      </th>
                    )}
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transfer.transfer_items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.ingredient.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            Code: {item.ingredient.code}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.requested_quantity.toLocaleString()} {item.unit}
                      </td>
                      {(transfer.status === 'approved' || transfer.status === 'completed') && (
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.approved_quantity ? 
                            `${item.approved_quantity.toLocaleString()} ${item.unit}` : 
                            'Not set'
                          }
                        </td>
                      )}
                      <td className="px-4 py-4 text-sm text-gray-500">
                        {item.notes || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Transfer Notes */}
          {transfer.notes && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Transfer Notes</h4>
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-sm text-gray-700">{transfer.notes}</p>
              </div>
            </div>
          )}

          {/* Close Button */}
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
