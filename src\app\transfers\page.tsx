'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import TransferApprovalModal from '@/components/transfers/TransferApprovalModal'
import TransferDetailsModal from '@/components/transfers/TransferDetailsModal'
import { inventoryService, warehouseService } from '@/lib/database'

interface Transfer {
  id: string
  status: string
  requested_at: string
  approved_at?: string
  completed_at?: string
  notes?: string
  total_items: number
  from_warehouse: {
    id: string
    name: string
    code: string
  }
  to_warehouse: {
    id: string
    name: string
    code: string
  }
  requester: {
    id: string
    full_name: string
  }
  transfer_items: Array<{
    id: string
    requested_quantity: number
    approved_quantity?: number
    unit: string
    notes?: string
    ingredient: {
      id: string
      name: string
      code: string
    }
  }>
}

interface Warehouse {
  id: string
  name: string
  code: string
  type: string
}

export default function TransfersPage() {
  const { user, profile } = useAuth()
  const [transfers, setTransfers] = useState<Transfer[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null)
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)

  const loadWarehouses = useCallback(async () => {
    try {
      const data = await warehouseService.getAllWarehouses()
      setWarehouses(data)
    } catch (err) {
      console.error('Failed to load warehouses:', err)
      setError('Failed to load warehouses')
    }
  }, [])

  const loadTransfers = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      let data: Transfer[]
      
      if (statusFilter === 'pending') {
        data = await inventoryService.getPendingTransfers(selectedWarehouse || undefined)
      } else {
        // For now, we'll use pending transfers and filter client-side
        // In a real implementation, you'd want a more comprehensive getTransfers method
        data = await inventoryService.getPendingTransfers(selectedWarehouse || undefined)
      }
      
      // Apply status filter
      if (statusFilter !== 'all' && statusFilter !== 'pending') {
        data = data.filter(transfer => transfer.status === statusFilter)
      }
      
      setTransfers(data)
    } catch (err) {
      console.error('Failed to load transfers:', err)
      setError('Failed to load transfers')
    } finally {
      setLoading(false)
    }
  }, [selectedWarehouse, statusFilter])

  useEffect(() => {
    loadWarehouses()
  }, [loadWarehouses])

  useEffect(() => {
    loadTransfers()
  }, [loadTransfers])

  const handleWarehouseChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedWarehouse(event.target.value)
  }

  const handleStatusFilterChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(event.target.value)
  }

  const handleApproveTransfer = (transfer: Transfer) => {
    setSelectedTransfer(transfer)
    setIsApprovalModalOpen(true)
  }

  const handleViewDetails = (transfer: Transfer) => {
    setSelectedTransfer(transfer)
    setIsDetailsModalOpen(true)
  }

  const handleCompleteTransfer = async (transferId: string) => {
    if (!user?.id) return
    
    try {
      await inventoryService.completeTransfer(transferId, user.id)
      await loadTransfers()
    } catch (err) {
      console.error('Failed to complete transfer:', err)
      setError('Failed to complete transfer')
    }
  }

  const handleCancelTransfer = async (transferId: string, reason?: string) => {
    if (!user?.id) return
    
    try {
      await inventoryService.cancelTransfer(transferId, user.id, reason)
      await loadTransfers()
    } catch (err) {
      console.error('Failed to cancel transfer:', err)
      setError('Failed to cancel transfer')
    }
  }

  const handleTransferUpdated = () => {
    loadTransfers()
    setIsApprovalModalOpen(false)
    setIsDetailsModalOpen(false)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
      approved: { color: 'bg-blue-100 text-blue-800', text: 'Approved' },
      in_transit: { color: 'bg-purple-100 text-purple-800', text: 'In Transit' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Completed' },
      cancelled: { color: 'bg-red-100 text-red-800', text: 'Cancelled' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const canApprove = (transfer: Transfer) => {
    return transfer.status === 'pending' && profile?.role && ['admin', 'manager'].includes(profile.role)
  }

  const canComplete = (transfer: Transfer) => {
    return transfer.status === 'approved' && profile?.role && ['admin', 'manager', 'staff'].includes(profile.role)
  }

  const canCancel = (transfer: Transfer) => {
    return ['pending', 'approved'].includes(transfer.status) && profile?.role && ['admin', 'manager'].includes(profile.role)
  }

  if (loading && transfers.length === 0) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
        <DashboardLayout>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transfer Management</h1>
              <p className="text-gray-600">Manage warehouse transfers and approvals</p>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Warehouse
                </label>
                <select
                  value={selectedWarehouse}
                  onChange={handleWarehouseChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Warehouses</option>
                  {warehouses.map((warehouse) => (
                    <option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name} ({warehouse.code}) - {warehouse.type}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Status
                </label>
                <select
                  value={statusFilter}
                  onChange={handleStatusFilterChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="in_transit">In Transit</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Transfers Table */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Transfers ({transfers.length})
              </h3>
            </div>

            {transfers.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No transfers found</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transfer Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        From → To
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Items
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Requested
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transfers.map((transfer) => (
                      <tr key={transfer.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              Transfer #{transfer.id.slice(-8)}
                            </div>
                            <div className="text-sm text-gray-500">
                              By: {transfer.requester.full_name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <div>{transfer.from_warehouse.name} ({transfer.from_warehouse.code})</div>
                            <div className="text-gray-500">↓</div>
                            <div>{transfer.to_warehouse.name} ({transfer.to_warehouse.code})</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transfer.total_items} item{transfer.total_items !== 1 ? 's' : ''}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(transfer.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(transfer.requested_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                          <button
                            onClick={() => handleViewDetails(transfer)}
                            className="text-indigo-600 hover:text-indigo-900 transition-colors"
                          >
                            View
                          </button>
                          {canApprove(transfer) && (
                            <button
                              onClick={() => handleApproveTransfer(transfer)}
                              className="text-green-600 hover:text-green-900 transition-colors"
                            >
                              Approve
                            </button>
                          )}
                          {canComplete(transfer) && (
                            <button
                              onClick={() => handleCompleteTransfer(transfer.id)}
                              className="text-blue-600 hover:text-blue-900 transition-colors"
                            >
                              Complete
                            </button>
                          )}
                          {canCancel(transfer) && (
                            <button
                              onClick={() => handleCancelTransfer(transfer.id, 'Cancelled by user')}
                              className="text-red-600 hover:text-red-900 transition-colors"
                            >
                              Cancel
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Modals */}
          <TransferApprovalModal
            isOpen={isApprovalModalOpen}
            onClose={() => setIsApprovalModalOpen(false)}
            transfer={selectedTransfer}
            onTransferUpdated={handleTransferUpdated}
          />

          <TransferDetailsModal
            isOpen={isDetailsModalOpen}
            onClose={() => setIsDetailsModalOpen(false)}
            transfer={selectedTransfer}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
