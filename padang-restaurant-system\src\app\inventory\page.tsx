'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import AddStockModal from '@/components/inventory/AddStockModal'
import UpdateStockModal from '@/components/inventory/UpdateStockModal'
import TransferStockModal from '@/components/inventory/TransferStockModal'
import { inventoryService, warehouseService } from '@/lib/database'

interface InventoryItem {
  id: string
  current_stock: number
  minimum_stock: number
  maximum_stock: number
  reorder_point: number
  ingredient: {
    id: string
    name: string
    code: string
    unit: string
    cost_per_unit: number
  }
  warehouse: {
    id: string
    name: string
    code: string
  }
}

interface Warehouse {
  id: string
  name: string
  code: string
  type: string
}

type StockStatus = 'critical' | 'low' | 'good'

interface StockStatusInfo {
  status: StockStatus
  color: string
}

export default function InventoryPage() {
  const { profile } = useAuth()
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('')
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isInventoryLoading, setIsInventoryLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Modal states
  const [isAddStockModalOpen, setIsAddStockModalOpen] = useState(false)
  const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = useState(false)
  const [isTransferStockModalOpen, setIsTransferStockModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)

  const loadWarehouses = useCallback(async () => {
    try {
      const data = await warehouseService.getAllWarehouses()
      setWarehouses(data)
      // Auto-select first warehouse if available and no warehouse is selected
      if (data.length > 0 && !selectedWarehouse) {
        setSelectedWarehouse(data[0].id)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses'
      setError(errorMessage)
      console.error('Error loading warehouses:', err)
    }
  }, [selectedWarehouse])

  const loadInventory = useCallback(async (warehouseId: string) => {
    if (!warehouseId) return
    
    try {
      setIsInventoryLoading(true)
      setError(null) // Clear previous errors
      const data = await inventoryService.getWarehouseInventory(warehouseId)
      setInventory(data || []) // Ensure we always have an array
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory'
      setError(errorMessage)
      console.error('Error loading inventory:', err)
      setInventory([]) // Reset inventory on error
    } finally {
      setIsInventoryLoading(false)
    }
  }, [])

  const loadLowStockItems = useCallback(async () => {
    try {
      const data = await inventoryService.getLowStockItems()
      setLowStockItems(data || [])
    } catch (err) {
      console.error('Failed to load low stock items:', err)
      // Don't set error state for this as it's not critical
    }
  }, [])

  // Initial load
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true)
      await Promise.all([
        loadWarehouses(),
        loadLowStockItems()
      ])
      setIsLoading(false)
    }
    
    initializeData()
  }, [loadWarehouses, loadLowStockItems])

  // Load inventory when warehouse changes
  useEffect(() => {
    if (selectedWarehouse) {
      loadInventory(selectedWarehouse)
    } else {
      setInventory([])
    }
  }, [selectedWarehouse, loadInventory])

  const getStockStatus = (item: InventoryItem): StockStatusInfo => {
    if (item.current_stock <= item.minimum_stock) {
      return { status: 'critical', color: 'text-red-600 bg-red-100' }
    } else if (item.current_stock <= item.reorder_point) {
      return { status: 'low', color: 'text-yellow-600 bg-yellow-100' }
    } else {
      return { status: 'good', color: 'text-green-600 bg-green-100' }
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const calculateInventoryStats = () => {
    const totalItems = inventory.length
    const lowStockCount = inventory.filter(item => item.current_stock <= item.minimum_stock).length
    const reorderNeededCount = inventory.filter(item => item.current_stock <= item.reorder_point).length
    const totalValue = inventory.reduce((total, item) => {
      const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0)
      return total + itemValue
    }, 0)

    return {
      totalItems,
      lowStockCount,
      reorderNeededCount,
      totalValue
    }
  }

  const handleWarehouseChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedWarehouse(event.target.value)
  }

  const handleAddStock = () => {
    setIsAddStockModalOpen(true)
  }

  const handleUpdateStock = (itemId: string) => {
    const item = inventory.find(i => i.id === itemId)
    if (item) {
      setSelectedItem(item)
      setIsUpdateStockModalOpen(true)
    }
  }

  const handleTransferStock = (itemId: string) => {
    const item = inventory.find(i => i.id === itemId)
    if (item) {
      setSelectedItem(item)
      setIsTransferStockModalOpen(true)
    }
  }

  const handleStockUpdated = () => {
    if (selectedWarehouse) {
      loadInventory(selectedWarehouse)
    }
    loadLowStockItems()
  }

  const stats = calculateInventoryStats()

  // Show loading spinner during initial load
  if (isLoading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                value={selectedWarehouse}
                onChange={handleWarehouseChange}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                disabled={warehouses.length === 0}
              >
                <option value="">Select Warehouse</option>
                {warehouses.map((warehouse) => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name} ({warehouse.code})
                  </option>
                ))}
              </select>
              <button 
                onClick={handleAddStock}
                disabled={!selectedWarehouse}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                Add Stock
              </button>
            </div>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{error}</span>
              <button
                onClick={() => setError(null)}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
                aria-label="Close error message"
              >
                <span className="text-red-500">&times;</span>
              </button>
            </div>
          )}

          {/* Low Stock Alert */}
          {lowStockItems.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="text-red-400 text-xl mr-3" aria-hidden="true">⚠️</div>
                <div>
                  <h3 className="text-lg font-medium text-red-800">Low Stock Alert</h3>
                  <p className="text-red-700">
                    {lowStockItems.length} item{lowStockItems.length !== 1 ? 's' : ''} 
                    {lowStockItems.length === 1 ? ' is' : ' are'} running low across all warehouses
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Inventory Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl" aria-hidden="true">📦</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Items
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.totalItems}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl" aria-hidden="true">⚠️</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Low Stock Items
                      </dt>
                      <dd className="text-lg font-medium text-red-600">
                        {stats.lowStockCount}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl" aria-hidden="true">💰</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Value
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatCurrency(stats.totalValue)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl" aria-hidden="true">🔄</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Reorder Needed
                      </dt>
                      <dd className="text-lg font-medium text-yellow-600">
                        {stats.reorderNeededCount}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Inventory Table */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Current Inventory
                {selectedWarehouse && (
                  <span className="text-sm text-gray-500 ml-2">
                    - {warehouses.find(w => w.id === selectedWarehouse)?.name || 'Unknown Warehouse'}
                  </span>
                )}
              </h3>
            </div>
            
            {isInventoryLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              </div>
            ) : !selectedWarehouse ? (
              <div className="px-4 py-8 text-center text-gray-500">
                Please select a warehouse to view inventory
              </div>
            ) : inventory.length === 0 ? (
              <div className="px-4 py-8 text-center text-gray-500">
                No inventory items found for this warehouse
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ingredient
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Current Stock
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Min/Max
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {inventory.map((item) => {
                      const stockStatus = getStockStatus(item)
                      const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0)
                      
                      return (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {item.ingredient.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {item.ingredient.code}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {item.current_stock.toLocaleString()} {item.ingredient.unit}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>
                              {stockStatus.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.minimum_stock.toLocaleString()} / {item.maximum_stock.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(itemValue)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button 
                              onClick={() => handleUpdateStock(item.id)}
                              className="text-indigo-600 hover:text-indigo-900 mr-3 transition-colors"
                            >
                              Update
                            </button>
                            <button 
                              onClick={() => handleTransferStock(item.id)}
                              className="text-green-600 hover:text-green-900 transition-colors"
                            >
                              Transfer
                            </button>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Modals */}
          <AddStockModal
            isOpen={isAddStockModalOpen}
            onClose={() => setIsAddStockModalOpen(false)}
            warehouseId={selectedWarehouse}
            onStockAdded={handleStockUpdated}
          />

          <UpdateStockModal
            isOpen={isUpdateStockModalOpen}
            onClose={() => setIsUpdateStockModalOpen(false)}
            item={selectedItem}
            onStockUpdated={handleStockUpdated}
          />

          <TransferStockModal
            isOpen={isTransferStockModalOpen}
            onClose={() => setIsTransferStockModalOpen(false)}
            item={selectedItem}
            onTransferCreated={handleStockUpdated}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}