-- Padang Restaurant Management System Database Schema
-- This schema supports multi-location restaurant chain with centralized and distributed operations

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- User profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'staff', 'cashier')),
    branch_id UUID REFERENCES branches(id),
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Branches table
CREATE TABLE branches (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL, -- Branch code for easy identification
    address TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    manager_id UUID REFERENCES profiles(id),
    is_active BOOLEAN DEFAULT true,
    operating_hours JSONB, -- Store opening/closing times
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Warehouses table
CREATE TABLE warehouses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    address TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('central', 'branch')),
    branch_id UUID REFERENCES branches(id), -- NULL for central warehouses
    manager_id UUID REFERENCES profiles(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Kitchens table
CREATE TABLE kitchens (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('central', 'branch')),
    branch_id UUID REFERENCES branches(id), -- NULL for central kitchens
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    head_chef_id UUID REFERENCES profiles(id),
    capacity INTEGER DEFAULT 100, -- Production capacity percentage
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ingredient categories
CREATE TABLE ingredient_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ingredients/Raw materials table
CREATE TABLE ingredients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    category_id UUID REFERENCES ingredient_categories(id),
    unit TEXT NOT NULL, -- kg, liter, pieces, etc.
    cost_per_unit DECIMAL(10,2),
    supplier_info JSONB, -- Store supplier details
    shelf_life_days INTEGER,
    storage_requirements TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipes table (for Padang dishes)
CREATE TABLE recipes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL, -- rendang, gulai, ayam pop, etc.
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    category TEXT NOT NULL, -- main_dish, side_dish, beverage, etc.
    serving_size INTEGER DEFAULT 1,
    prep_time_minutes INTEGER,
    cook_time_minutes INTEGER,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    instructions TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipe ingredients (ingredients needed for each recipe)
CREATE TABLE recipe_ingredients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    ingredient_id UUID REFERENCES ingredients(id),
    quantity DECIMAL(10,3) NOT NULL,
    unit TEXT NOT NULL,
    is_optional BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inventory table (stock levels per warehouse)
CREATE TABLE inventory (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    ingredient_id UUID REFERENCES ingredients(id) NOT NULL,
    current_stock DECIMAL(10,3) DEFAULT 0,
    minimum_stock DECIMAL(10,3) DEFAULT 0,
    maximum_stock DECIMAL(10,3),
    reorder_point DECIMAL(10,3),
    last_restocked_at TIMESTAMP WITH TIME ZONE,
    expiry_date DATE,
    batch_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(warehouse_id, ingredient_id, batch_number)
);

-- Stock movements (track all inventory changes)
CREATE TABLE stock_movements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    ingredient_id UUID REFERENCES ingredients(id) NOT NULL,
    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'transfer', 'adjustment', 'waste')),
    quantity DECIMAL(10,3) NOT NULL,
    unit TEXT NOT NULL,
    reference_type TEXT, -- 'purchase', 'production', 'transfer', 'sale', 'waste'
    reference_id UUID, -- ID of related record (purchase_order, production_batch, etc.)
    notes TEXT,
    performed_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inter-warehouse transfers
CREATE TABLE warehouse_transfers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    from_warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    to_warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'in_transit', 'completed', 'cancelled')),
    requested_by UUID REFERENCES profiles(id) NOT NULL,
    approved_by UUID REFERENCES profiles(id),
    total_items INTEGER DEFAULT 0,
    notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    approved_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Transfer items
CREATE TABLE transfer_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    transfer_id UUID REFERENCES warehouse_transfers(id) ON DELETE CASCADE,
    ingredient_id UUID REFERENCES ingredients(id) NOT NULL,
    requested_quantity DECIMAL(10,3) NOT NULL,
    approved_quantity DECIMAL(10,3),
    unit TEXT NOT NULL,
    notes TEXT
);

-- Production batches (track cooking/preparation)
CREATE TABLE production_batches (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    kitchen_id UUID REFERENCES kitchens(id) NOT NULL,
    recipe_id UUID REFERENCES recipes(id) NOT NULL,
    batch_number TEXT UNIQUE NOT NULL,
    planned_quantity INTEGER NOT NULL,
    actual_quantity INTEGER,
    status TEXT NOT NULL DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled')),
    quality_score INTEGER CHECK (quality_score BETWEEN 1 AND 10),
    quality_notes TEXT,
    started_by UUID REFERENCES profiles(id),
    completed_by UUID REFERENCES profiles(id),
    planned_start_time TIMESTAMP WITH TIME ZONE,
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Production batch ingredients used
CREATE TABLE batch_ingredients_used (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    batch_id UUID REFERENCES production_batches(id) ON DELETE CASCADE,
    ingredient_id UUID REFERENCES ingredients(id) NOT NULL,
    planned_quantity DECIMAL(10,3) NOT NULL,
    actual_quantity DECIMAL(10,3),
    unit TEXT NOT NULL,
    cost_per_unit DECIMAL(10,2),
    total_cost DECIMAL(10,2)
);

-- Menu items (dishes available for sale)
CREATE TABLE menu_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    recipe_id UUID REFERENCES recipes(id),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    base_price DECIMAL(10,2) NOT NULL,
    is_available BOOLEAN DEFAULT true,
    is_signature_dish BOOLEAN DEFAULT false,
    image_url TEXT,
    nutritional_info JSONB,
    allergen_info TEXT[],
    spice_level INTEGER CHECK (spice_level BETWEEN 1 AND 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Branch-specific menu pricing
CREATE TABLE branch_menu_pricing (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    menu_item_id UUID REFERENCES menu_items(id) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    is_available BOOLEAN DEFAULT true,
    daily_quota INTEGER, -- Maximum servings per day
    current_sold INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, menu_item_id)
);

-- Sales transactions
CREATE TABLE sales_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    transaction_number TEXT UNIQUE NOT NULL,
    customer_name TEXT,
    customer_phone TEXT,
    total_amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'e_wallet', 'qris', 'bank_transfer')),
    payment_status TEXT NOT NULL DEFAULT 'completed' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    served_by UUID REFERENCES profiles(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sales transaction items
CREATE TABLE sales_transaction_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    transaction_id UUID REFERENCES sales_transactions(id) ON DELETE CASCADE,
    menu_item_id UUID REFERENCES menu_items(id) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    special_instructions TEXT
);

-- Daily sales summaries
CREATE TABLE daily_sales_summaries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    date DATE NOT NULL,
    total_transactions INTEGER DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    total_tax DECIMAL(10,2) DEFAULT 0,
    total_discount DECIMAL(10,2) DEFAULT 0,
    cash_sales DECIMAL(10,2) DEFAULT 0,
    card_sales DECIMAL(10,2) DEFAULT 0,
    digital_sales DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, date)
);

-- Audit logs for tracking all system activities
CREATE TABLE audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    user_role TEXT NOT NULL,
    details JSONB NOT NULL DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    branch_id UUID REFERENCES branches(id),
    warehouse_id UUID REFERENCES warehouses(id)
);

-- Add foreign key constraint for profiles.branch_id (had to add after branches table creation)
ALTER TABLE profiles ADD CONSTRAINT fk_profiles_branch FOREIGN KEY (branch_id) REFERENCES branches(id);

-- Create indexes for better performance
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_branch ON profiles(branch_id);
CREATE INDEX idx_inventory_warehouse ON inventory(warehouse_id);
CREATE INDEX idx_inventory_ingredient ON inventory(ingredient_id);
CREATE INDEX idx_stock_movements_warehouse ON stock_movements(warehouse_id);
CREATE INDEX idx_stock_movements_date ON stock_movements(created_at);
CREATE INDEX idx_production_batches_kitchen ON production_batches(kitchen_id);
CREATE INDEX idx_production_batches_status ON production_batches(status);
CREATE INDEX idx_sales_transactions_branch ON sales_transactions(branch_id);
CREATE INDEX idx_sales_transactions_date ON sales_transactions(created_at);
CREATE INDEX idx_branch_menu_pricing_branch ON branch_menu_pricing(branch_id);

-- Index for audit logs performance
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_branch ON audit_logs(branch_id);
CREATE INDEX idx_audit_logs_warehouse ON audit_logs(warehouse_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Optimized function to get low stock items
CREATE OR REPLACE FUNCTION get_low_stock_items_optimized(branch_id_param UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    warehouse_id UUID,
    ingredient_id UUID,
    current_stock DECIMAL(10,3),
    minimum_stock DECIMAL(10,3),
    maximum_stock DECIMAL(10,3),
    reorder_point DECIMAL(10,3),
    last_restocked_at TIMESTAMP WITH TIME ZONE,
    expiry_date DATE,
    batch_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    ingredient JSONB,
    warehouse JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        i.id,
        i.warehouse_id,
        i.ingredient_id,
        i.current_stock,
        i.minimum_stock,
        i.maximum_stock,
        i.reorder_point,
        i.last_restocked_at,
        i.expiry_date,
        i.batch_number,
        i.created_at,
        i.updated_at,
        to_jsonb(ing.*) as ingredient,
        to_jsonb(w.*) as warehouse
    FROM inventory i
    JOIN ingredients ing ON i.ingredient_id = ing.id
    JOIN warehouses w ON i.warehouse_id = w.id
    WHERE i.current_stock <= i.minimum_stock
    AND (branch_id_param IS NULL OR w.branch_id = branch_id_param)
    ORDER BY i.current_stock ASC
    LIMIT 100;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers to relevant tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_warehouses_updated_at BEFORE UPDATE ON warehouses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kitchens_updated_at BEFORE UPDATE ON kitchens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ingredients_updated_at BEFORE UPDATE ON ingredients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recipes_updated_at BEFORE UPDATE ON recipes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branch_menu_pricing_updated_at BEFORE UPDATE ON branch_menu_pricing FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_sales_summaries_updated_at BEFORE UPDATE ON daily_sales_summaries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE warehouses ENABLE ROW LEVEL SECURITY;
ALTER TABLE kitchens ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_transactions ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON profiles FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Admins can manage all profiles" ON profiles FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Branch-based access policies
CREATE POLICY "Branch access for inventory" ON inventory FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles p
        JOIN warehouses w ON w.branch_id = p.branch_id
        WHERE p.id = auth.uid() AND w.id = inventory.warehouse_id
    ) OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Branch access for sales" ON sales_transactions FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND (branch_id = sales_transactions.branch_id OR role IN ('admin', 'manager'))
    )
);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, role)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'), 'staff');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
