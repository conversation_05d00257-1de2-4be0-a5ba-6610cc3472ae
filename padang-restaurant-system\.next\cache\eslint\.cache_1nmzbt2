[{"D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\branches\\page.tsx": "1", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\dashboard\\page.tsx": "2", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\inventory\\page.tsx": "3", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\layout.tsx": "4", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\page.tsx": "5", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\pos\\page.tsx": "6", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\production\\page.tsx": "7", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\reports\\page.tsx": "8", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\auth\\LoginForm.tsx": "9", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\auth\\ProtectedRoute.tsx": "10", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\layout\\DashboardLayout.tsx": "11", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx": "12", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\lib\\database.ts": "13", "D:\\padanghub_supabase\\padang-restaurant-system\\src\\lib\\supabase.ts": "14"}, {"size": 18739, "mtime": 1751546429028, "results": "15", "hashOfConfig": "16"}, {"size": 6094, "mtime": 1751539503486, "results": "17", "hashOfConfig": "16"}, {"size": 17166, "mtime": 1751544538402, "results": "18", "hashOfConfig": "16"}, {"size": 839, "mtime": 1751538828736, "results": "19", "hashOfConfig": "16"}, {"size": 5051, "mtime": 1751539066789, "results": "20", "hashOfConfig": "16"}, {"size": 16356, "mtime": 1751546497806, "results": "21", "hashOfConfig": "16"}, {"size": 16863, "mtime": 1751546312021, "results": "22", "hashOfConfig": "16"}, {"size": 19809, "mtime": 1751546571621, "results": "23", "hashOfConfig": "16"}, {"size": 3334, "mtime": 1751538744763, "results": "24", "hashOfConfig": "16"}, {"size": 1974, "mtime": 1751538754652, "results": "25", "hashOfConfig": "16"}, {"size": 5284, "mtime": 1751538778989, "results": "26", "hashOfConfig": "16"}, {"size": 3396, "mtime": 1751538732043, "results": "27", "hashOfConfig": "16"}, {"size": 19989, "mtime": 1751546363686, "results": "28", "hashOfConfig": "16"}, {"size": 1059, "mtime": 1751538717628, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "a9ap5j", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\branches\\page.tsx", ["72", "73", "74"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\dashboard\\page.tsx", ["75", "76"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\inventory\\page.tsx", ["77"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\layout.tsx", [], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\page.tsx", ["78"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\pos\\page.tsx", ["79"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\production\\page.tsx", [], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\reports\\page.tsx", ["80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\auth\\ProtectedRoute.tsx", ["92"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\components\\layout\\DashboardLayout.tsx", ["93"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx", ["94", "95", "96"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\lib\\database.ts", ["97", "98", "99", "100", "101", "102", "103", "104", "105", "106"], [], "D:\\padanghub_supabase\\padang-restaurant-system\\src\\lib\\supabase.ts", [], [], {"ruleId": "107", "severity": 2, "message": "108", "line": 37, "column": 11, "nodeType": null, "messageId": "109", "endLine": 37, "endColumn": 18}, {"ruleId": "110", "severity": 2, "message": "111", "line": 41, "column": 58, "nodeType": "112", "messageId": "113", "endLine": 41, "endColumn": 61, "suggestions": "114"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 54, "column": 6, "nodeType": "117", "endLine": 54, "endColumn": 33, "suggestions": "118"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 21, "column": 76, "nodeType": "121", "messageId": "122", "suggestions": "123"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 98, "column": 30, "nodeType": "121", "messageId": "122", "suggestions": "124"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 44, "column": 11, "nodeType": null, "messageId": "109", "endLine": 44, "endColumn": 18}, {"ruleId": "119", "severity": 2, "message": "120", "line": 98, "column": 30, "nodeType": "121", "messageId": "122", "suggestions": "125"}, {"ruleId": "115", "severity": 1, "message": "126", "line": 58, "column": 6, "nodeType": "117", "endLine": 58, "endColumn": 8, "suggestions": "127"}, {"ruleId": "107", "severity": 2, "message": "128", "line": 7, "column": 43, "nodeType": null, "messageId": "109", "endLine": 7, "endColumn": 55}, {"ruleId": "107", "severity": 2, "message": "129", "line": 7, "column": 57, "nodeType": null, "messageId": "109", "endLine": 7, "endColumn": 73}, {"ruleId": "110", "severity": 2, "message": "111", "line": 22, "column": 21, "nodeType": "112", "messageId": "113", "endLine": 22, "endColumn": 24, "suggestions": "130"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 23, "column": 20, "nodeType": "112", "messageId": "113", "endLine": 23, "endColumn": 23, "suggestions": "131"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 24, "column": 15, "nodeType": "112", "messageId": "113", "endLine": 24, "endColumn": 18, "suggestions": "132"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 28, "column": 11, "nodeType": null, "messageId": "109", "endLine": 28, "endColumn": 18}, {"ruleId": "110", "severity": 2, "message": "111", "line": 30, "column": 44, "nodeType": "112", "messageId": "113", "endLine": 30, "endColumn": 47, "suggestions": "133"}, {"ruleId": "115", "severity": 1, "message": "134", "line": 45, "column": 6, "nodeType": "117", "endLine": 45, "endColumn": 55, "suggestions": "135"}, {"ruleId": "136", "severity": 2, "message": "137", "line": 80, "column": 11, "nodeType": "138", "messageId": "139", "endLine": 80, "endColumn": 34, "fix": "140"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 80, "column": 29, "nodeType": "112", "messageId": "113", "endLine": 80, "endColumn": 32, "suggestions": "141"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 81, "column": 28, "nodeType": "112", "messageId": "113", "endLine": 81, "endColumn": 31, "suggestions": "142"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 106, "column": 35, "nodeType": "112", "messageId": "113", "endLine": 106, "endColumn": 38, "suggestions": "143"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 57, "column": 20, "nodeType": "121", "messageId": "122", "suggestions": "144"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 116, "column": 65, "nodeType": "112", "messageId": "113", "endLine": 116, "endColumn": 68, "suggestions": "145"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 12, "column": 65, "nodeType": "112", "messageId": "113", "endLine": 12, "endColumn": 68, "suggestions": "146"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 13, "column": 83, "nodeType": "112", "messageId": "113", "endLine": 13, "endColumn": 86, "suggestions": "147"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 15, "column": 66, "nodeType": "112", "messageId": "113", "endLine": 15, "endColumn": 69, "suggestions": "148"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 135, "column": 79, "nodeType": "112", "messageId": "113", "endLine": 135, "endColumn": 82, "suggestions": "149"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 194, "column": 80, "nodeType": "112", "messageId": "113", "endLine": 194, "endColumn": 83, "suggestions": "150"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 415, "column": 20, "nodeType": "112", "messageId": "113", "endLine": 415, "endColumn": 23, "suggestions": "151"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 440, "column": 65, "nodeType": "112", "messageId": "113", "endLine": 440, "endColumn": 68, "suggestions": "152"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 571, "column": 57, "nodeType": "112", "messageId": "113", "endLine": 571, "endColumn": 60, "suggestions": "153"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 571, "column": 78, "nodeType": "112", "messageId": "113", "endLine": 571, "endColumn": 81, "suggestions": "154"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 571, "column": 96, "nodeType": "112", "messageId": "113", "endLine": 571, "endColumn": 99, "suggestions": "155"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 741, "column": 41, "nodeType": "112", "messageId": "113", "endLine": 741, "endColumn": 44, "suggestions": "156"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 757, "column": 17, "nodeType": "112", "messageId": "113", "endLine": 757, "endColumn": 20, "suggestions": "157"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 757, "column": 25, "nodeType": "112", "messageId": "113", "endLine": 757, "endColumn": 28, "suggestions": "158"}, "@typescript-eslint/no-unused-vars", "'profile' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["159", "160"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadBranchAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["161"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["162", "163", "164", "165"], ["166", "167", "168", "169"], ["170", "171", "172", "173"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["174"], "'salesService' is defined but never used.", "'inventoryService' is defined but never used.", ["175", "176"], ["177", "178"], ["179", "180"], ["181", "182"], "React Hook useEffect has a missing dependency: 'generateReport'. Either include it or remove the dependency array.", ["183"], "prefer-const", "'branchComparison' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "184", "text": "185"}, ["186", "187"], ["188", "189"], ["190", "191"], ["192", "193", "194", "195"], ["196", "197"], ["198", "199"], ["200", "201"], ["202", "203"], ["204", "205"], ["206", "207"], ["208", "209"], ["210", "211"], ["212", "213"], ["214", "215"], ["216", "217"], ["218", "219"], ["220", "221"], ["222", "223"], {"messageId": "224", "fix": "225", "desc": "226"}, {"messageId": "227", "fix": "228", "desc": "229"}, {"desc": "230", "fix": "231"}, {"messageId": "232", "data": "233", "fix": "234", "desc": "235"}, {"messageId": "232", "data": "236", "fix": "237", "desc": "238"}, {"messageId": "232", "data": "239", "fix": "240", "desc": "241"}, {"messageId": "232", "data": "242", "fix": "243", "desc": "244"}, {"messageId": "232", "data": "245", "fix": "246", "desc": "235"}, {"messageId": "232", "data": "247", "fix": "248", "desc": "238"}, {"messageId": "232", "data": "249", "fix": "250", "desc": "241"}, {"messageId": "232", "data": "251", "fix": "252", "desc": "244"}, {"messageId": "232", "data": "253", "fix": "254", "desc": "235"}, {"messageId": "232", "data": "255", "fix": "256", "desc": "238"}, {"messageId": "232", "data": "257", "fix": "258", "desc": "241"}, {"messageId": "232", "data": "259", "fix": "260", "desc": "244"}, {"desc": "261", "fix": "262"}, {"messageId": "224", "fix": "263", "desc": "226"}, {"messageId": "227", "fix": "264", "desc": "229"}, {"messageId": "224", "fix": "265", "desc": "226"}, {"messageId": "227", "fix": "266", "desc": "229"}, {"messageId": "224", "fix": "267", "desc": "226"}, {"messageId": "227", "fix": "268", "desc": "229"}, {"messageId": "224", "fix": "269", "desc": "226"}, {"messageId": "227", "fix": "270", "desc": "229"}, {"desc": "271", "fix": "272"}, [2188, 2220], "const branchComparison: any[] = []", {"messageId": "224", "fix": "273", "desc": "226"}, {"messageId": "227", "fix": "274", "desc": "229"}, {"messageId": "224", "fix": "275", "desc": "226"}, {"messageId": "227", "fix": "276", "desc": "229"}, {"messageId": "224", "fix": "277", "desc": "226"}, {"messageId": "227", "fix": "278", "desc": "229"}, {"messageId": "232", "data": "279", "fix": "280", "desc": "235"}, {"messageId": "232", "data": "281", "fix": "282", "desc": "238"}, {"messageId": "232", "data": "283", "fix": "284", "desc": "241"}, {"messageId": "232", "data": "285", "fix": "286", "desc": "244"}, {"messageId": "224", "fix": "287", "desc": "226"}, {"messageId": "227", "fix": "288", "desc": "229"}, {"messageId": "224", "fix": "289", "desc": "226"}, {"messageId": "227", "fix": "290", "desc": "229"}, {"messageId": "224", "fix": "291", "desc": "226"}, {"messageId": "227", "fix": "292", "desc": "229"}, {"messageId": "224", "fix": "293", "desc": "226"}, {"messageId": "227", "fix": "294", "desc": "229"}, {"messageId": "224", "fix": "295", "desc": "226"}, {"messageId": "227", "fix": "296", "desc": "229"}, {"messageId": "224", "fix": "297", "desc": "226"}, {"messageId": "227", "fix": "298", "desc": "229"}, {"messageId": "224", "fix": "299", "desc": "226"}, {"messageId": "227", "fix": "300", "desc": "229"}, {"messageId": "224", "fix": "301", "desc": "226"}, {"messageId": "227", "fix": "302", "desc": "229"}, {"messageId": "224", "fix": "303", "desc": "226"}, {"messageId": "227", "fix": "304", "desc": "229"}, {"messageId": "224", "fix": "305", "desc": "226"}, {"messageId": "227", "fix": "306", "desc": "229"}, {"messageId": "224", "fix": "307", "desc": "226"}, {"messageId": "227", "fix": "308", "desc": "229"}, {"messageId": "224", "fix": "309", "desc": "226"}, {"messageId": "227", "fix": "310", "desc": "229"}, {"messageId": "224", "fix": "311", "desc": "226"}, {"messageId": "227", "fix": "312", "desc": "229"}, {"messageId": "224", "fix": "313", "desc": "226"}, {"messageId": "227", "fix": "314", "desc": "229"}, "suggestUnknown", {"range": "315", "text": "316"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "317", "text": "318"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [selectedBranch, dateRange, loadBranchAnalytics]", {"range": "319", "text": "320"}, "replaceWithAlt", {"alt": "321"}, {"range": "322", "text": "323"}, "Replace with `&apos;`.", {"alt": "324"}, {"range": "325", "text": "326"}, "Replace with `&lsquo;`.", {"alt": "327"}, {"range": "328", "text": "329"}, "Replace with `&#39;`.", {"alt": "330"}, {"range": "331", "text": "332"}, "Replace with `&rsquo;`.", {"alt": "321"}, {"range": "333", "text": "334"}, {"alt": "324"}, {"range": "335", "text": "336"}, {"alt": "327"}, {"range": "337", "text": "338"}, {"alt": "330"}, {"range": "339", "text": "340"}, {"alt": "321"}, {"range": "341", "text": "342"}, {"alt": "324"}, {"range": "343", "text": "344"}, {"alt": "327"}, {"range": "345", "text": "346"}, {"alt": "330"}, {"range": "347", "text": "348"}, "Update the dependencies array to be: [loadInitialData]", {"range": "349", "text": "350"}, {"range": "351", "text": "316"}, {"range": "352", "text": "318"}, {"range": "353", "text": "316"}, {"range": "354", "text": "318"}, {"range": "355", "text": "316"}, {"range": "356", "text": "318"}, {"range": "357", "text": "316"}, {"range": "358", "text": "318"}, "Update the dependencies array to be: [selectedBranch, dateRange, reportType, branches, generateReport]", {"range": "359", "text": "360"}, {"range": "361", "text": "316"}, {"range": "362", "text": "318"}, {"range": "363", "text": "316"}, {"range": "364", "text": "318"}, {"range": "365", "text": "316"}, {"range": "366", "text": "318"}, {"alt": "321"}, {"range": "367", "text": "368"}, {"alt": "324"}, {"range": "369", "text": "370"}, {"alt": "327"}, {"range": "371", "text": "372"}, {"alt": "330"}, {"range": "373", "text": "374"}, {"range": "375", "text": "316"}, {"range": "376", "text": "318"}, {"range": "377", "text": "316"}, {"range": "378", "text": "318"}, {"range": "379", "text": "316"}, {"range": "380", "text": "318"}, {"range": "381", "text": "316"}, {"range": "382", "text": "318"}, {"range": "383", "text": "316"}, {"range": "384", "text": "318"}, {"range": "385", "text": "316"}, {"range": "386", "text": "318"}, {"range": "387", "text": "316"}, {"range": "388", "text": "318"}, {"range": "389", "text": "316"}, {"range": "390", "text": "318"}, {"range": "391", "text": "316"}, {"range": "392", "text": "318"}, {"range": "393", "text": "316"}, {"range": "394", "text": "318"}, {"range": "395", "text": "316"}, {"range": "396", "text": "318"}, {"range": "397", "text": "316"}, {"range": "398", "text": "318"}, {"range": "399", "text": "316"}, {"range": "400", "text": "318"}, {"range": "401", "text": "316"}, {"range": "402", "text": "318"}, [1078, 1081], "unknown", [1078, 1081], "never", [1402, 1429], "[selectedBranch, dateRange, loadBranchAnalytics]", "&apos;", [764, 826], "! Here&apos;s your restaurant management overview.\n                ", "&lsquo;", [764, 826], "! Here&lsquo;s your restaurant management overview.\n                ", "&#39;", [764, 826], "! Here&#39;s your restaurant management overview.\n                ", "&rsquo;", [764, 826], "! Here&rsquo;s your restaurant management overview.\n                ", [3768, 3829], "\n                        Today&apos;s Sales\n                      ", [3768, 3829], "\n                        Today&lsquo;s Sales\n                      ", [3768, 3829], "\n                        Today&#39;s Sales\n                      ", [3768, 3829], "\n                        Today&rsquo;s Sales\n                      ", [3881, 3944], "\r\n                        Today&apos;s Sales\r\n                      ", [3881, 3944], "\r\n                        Today&lsquo;s Sales\r\n                      ", [3881, 3944], "\r\n                        Today&#39;s Sales\r\n                      ", [3881, 3944], "\r\n                        Today&rsquo;s Sales\r\n                      ", [1663, 1665], "[loadInitialData]", [637, 640], [637, 640], [662, 665], [662, 665], [682, 685], [682, 685], [878, 881], [878, 881], [1312, 1361], "[selectedBranch, dateRange, reportType, branches, generateReport]", [2210, 2213], [2210, 2213], [2248, 2251], [2248, 2251], [3334, 3337], [3334, 3337], [1731, 1801], "\n            You don&apos;t have permission to access this page.\n          ", [1731, 1801], "\n            You don&lsquo;t have permission to access this page.\n          ", [1731, 1801], "\n            You don&#39;t have permission to access this page.\n          ", [1731, 1801], "\n            You don&rsquo;t have permission to access this page.\n          ", [4274, 4277], [4274, 4277], [374, 377], [374, 377], [463, 466], [463, 466], [566, 569], [566, 569], [3678, 3681], [3678, 3681], [5346, 5349], [5346, 5349], [10368, 10371], [10368, 10371], [11150, 11153], [11150, 11153], [14311, 14314], [14311, 14314], [14332, 14335], [14332, 14335], [14350, 14353], [14350, 14353], [19468, 19471], [19468, 19471], [19909, 19912], [19909, 19912], [19917, 19920], [19917, 19920]]