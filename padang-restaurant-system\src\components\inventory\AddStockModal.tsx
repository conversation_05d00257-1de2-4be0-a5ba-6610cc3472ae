'use client'

import { useState, useEffect } from 'react'
import { ingredientService, inventoryService } from '@/lib/database'

interface Ingredient {
  id: string
  name: string
  code: string
  unit: string
  cost_per_unit: number
}

interface AddStockModalProps {
  isOpen: boolean
  onClose: () => void
  warehouseId: string
  onStockAdded: () => void
}

export default function AddStockModal({ isOpen, onClose, warehouseId, onStockAdded }: AddStockModalProps) {
  const [ingredients, setIngredients] = useState<Ingredient[]>([])
  const [selectedIngredient, setSelectedIngredient] = useState('')
  const [quantity, setQuantity] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen) {
      loadIngredients()
    }
  }, [isOpen])

  const loadIngredients = async () => {
    try {
      const data = await ingredientService.getAllIngredients()
      setIngredients(data)
    } catch (err) {
      setError('Failed to load ingredients')
      console.error(err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedIngredient || !quantity) {
      setError('Please fill in all required fields')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const ingredient = ingredients.find(i => i.id === selectedIngredient)
      if (!ingredient) {
        throw new Error('Selected ingredient not found')
      }

      // Check if inventory record exists for this ingredient in this warehouse
      const existingInventory = await inventoryService.getWarehouseInventory(warehouseId)
      const existingItem = existingInventory.find(item => item.ingredient.id === selectedIngredient)

      if (existingItem) {
        // Update existing inventory
        const newStock = existingItem.current_stock + parseFloat(quantity)
        await inventoryService.updateStock(
          existingItem.id,
          newStock,
          'in',
          notes || 'Stock added via inventory management',
          // We'll need to get the current user ID from auth context
          undefined
        )
      } else {
        // Create new inventory record
        await inventoryService.createInventoryRecord(
          warehouseId,
          selectedIngredient,
          parseFloat(quantity),
          notes || 'Initial stock entry'
        )
      }

      onStockAdded()
      handleClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add stock')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedIngredient('')
    setQuantity('')
    setNotes('')
    setError('')
    onClose()
  }

  if (!isOpen) return null

  const selectedIngredientData = ingredients.find(i => i.id === selectedIngredient)

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add Stock</h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {error && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ingredient *
              </label>
              <select
                value={selectedIngredient}
                onChange={(e) => setSelectedIngredient(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="">Select an ingredient</option>
                {ingredients.map((ingredient) => (
                  <option key={ingredient.id} value={ingredient.id}>
                    {ingredient.name} ({ingredient.code}) - {ingredient.unit}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity * {selectedIngredientData && `(${selectedIngredientData.unit})`}
              </label>
              <input
                type="number"
                step="0.001"
                min="0"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter quantity"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Optional notes about this stock addition"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Adding...' : 'Add Stock'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
