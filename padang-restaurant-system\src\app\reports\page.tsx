'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { analyticsService, branchService, salesService, inventoryService } from '@/lib/database'

interface ReportData {
  salesSummary: {
    totalRevenue: number
    totalTransactions: number
    averageTransaction: number
    topPaymentMethod: string
  }
  inventorySummary: {
    totalValue: number
    lowStockItems: number
    totalItems: number
    criticalItems: number
  }
  branchComparison: any[]
  topSellingItems: any[]
  salesTrend: any[]
}

export default function ReportsPage() {
  const { profile } = useAuth()
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [branches, setBranches] = useState<any[]>([])
  const [selectedBranch, setSelectedBranch] = useState<string>('all')
  const [dateRange, setDateRange] = useState('30')
  const [reportType, setReportType] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    if (branches.length > 0) {
      generateReport()
    }
  }, [selectedBranch, dateRange, reportType, branches])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      const branchesData = await branchService.getAllBranches()
      setBranches(branchesData)
    } catch (err) {
      setError('Failed to load data')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async () => {
    try {
      setLoading(true)
      const startDate = new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString()
      const endDate = new Date().toISOString()

      let salesSummary = {
        totalRevenue: 0,
        totalTransactions: 0,
        averageTransaction: 0,
        topPaymentMethod: 'cash'
      }

      let inventorySummary = {
        totalValue: 0,
        lowStockItems: 0,
        totalItems: 0,
        criticalItems: 0
      }

      let branchComparison: any[] = []
      let topSellingItems: any[] = []

      if (selectedBranch === 'all') {
        // Generate report for all branches
        for (const branch of branches) {
          const [performance, topItems] = await Promise.all([
            analyticsService.getBranchPerformance(branch.id, startDate, endDate),
            analyticsService.getTopSellingItems(branch.id, 10, startDate, endDate)
          ])

          salesSummary.totalRevenue += performance.totalRevenue
          salesSummary.totalTransactions += performance.totalTransactions
          inventorySummary.totalValue += performance.inventoryValue
          inventorySummary.lowStockItems += performance.lowStockItems

          branchComparison.push({
            branchName: branch.name,
            revenue: performance.totalRevenue,
            transactions: performance.totalTransactions,
            averageTransaction: performance.averageTransaction,
            inventoryValue: performance.inventoryValue,
            lowStockItems: performance.lowStockItems
          })

          // Merge top selling items
          topItems.forEach((item: any) => {
            const existingItem = topSellingItems.find(existing => existing.name === item.name)
            if (existingItem) {
              existingItem.totalQuantity += item.totalQuantity
              existingItem.totalRevenue += item.totalRevenue
            } else {
              topSellingItems.push({ ...item })
            }
          })
        }

        salesSummary.averageTransaction = salesSummary.totalTransactions > 0 
          ? salesSummary.totalRevenue / salesSummary.totalTransactions 
          : 0

        // Sort top selling items
        topSellingItems.sort((a, b) => b.totalQuantity - a.totalQuantity)
        topSellingItems = topSellingItems.slice(0, 10)

      } else {
        // Generate report for specific branch
        const [performance, topItems] = await Promise.all([
          analyticsService.getBranchPerformance(selectedBranch, startDate, endDate),
          analyticsService.getTopSellingItems(selectedBranch, 10, startDate, endDate)
        ])

        salesSummary = {
          totalRevenue: performance.totalRevenue,
          totalTransactions: performance.totalTransactions,
          averageTransaction: performance.averageTransaction,
          topPaymentMethod: 'cash' // This would need to be calculated from sales data
        }

        inventorySummary = {
          totalValue: performance.inventoryValue,
          lowStockItems: performance.lowStockItems,
          totalItems: 0, // This would need to be calculated
          criticalItems: performance.lowStockItems
        }

        topSellingItems = topItems
      }

      setReportData({
        salesSummary,
        inventorySummary,
        branchComparison,
        topSellingItems,
        salesTrend: [] // This would need historical data
      })

    } catch (err) {
      setError('Failed to generate report')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const exportReport = () => {
    if (!reportData) return

    const reportContent = `
Padang Restaurant Management System - Report
Generated: ${new Date().toLocaleString()}
Period: Last ${dateRange} days
Branch: ${selectedBranch === 'all' ? 'All Branches' : branches.find(b => b.id === selectedBranch)?.name}

SALES SUMMARY
=============
Total Revenue: ${formatCurrency(reportData.salesSummary.totalRevenue)}
Total Transactions: ${reportData.salesSummary.totalTransactions}
Average Transaction: ${formatCurrency(reportData.salesSummary.averageTransaction)}

INVENTORY SUMMARY
================
Total Inventory Value: ${formatCurrency(reportData.inventorySummary.totalValue)}
Low Stock Items: ${reportData.inventorySummary.lowStockItems}

TOP SELLING ITEMS
================
${reportData.topSellingItems.map((item, index) => 
  `${index + 1}. ${item.name} - ${item.totalQuantity} sold - ${formatCurrency(item.totalRevenue)}`
).join('\n')}

${selectedBranch === 'all' ? `
BRANCH COMPARISON
================
${reportData.branchComparison.map(branch => 
  `${branch.branchName}: ${formatCurrency(branch.revenue)} (${branch.transactions} transactions)`
).join('\n')}
` : ''}
    `

    const blob = new Blob([reportContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `padang-restaurant-report-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'manager']}>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'manager']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <div className="flex space-x-4">
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="overview">Overview</option>
                <option value="sales">Sales Report</option>
                <option value="inventory">Inventory Report</option>
                <option value="performance">Performance Report</option>
              </select>
              <select
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">All Branches</option>
                {branches.map((branch) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name} ({branch.code})
                  </option>
                ))}
              </select>
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
              <button
                onClick={exportReport}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Export Report
              </button>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {reportData && (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="text-2xl">💰</div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Total Revenue
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {formatCurrency(reportData.salesSummary.totalRevenue)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="text-2xl">🧾</div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Total Transactions
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {reportData.salesSummary.totalTransactions.toLocaleString()}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="text-2xl">📊</div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Avg Transaction
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {formatCurrency(reportData.salesSummary.averageTransaction)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="text-2xl">📦</div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Inventory Value
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {formatCurrency(reportData.inventorySummary.totalValue)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Top Selling Items */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Top Selling Items (Last {dateRange} days)
                  </h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Rank
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Item
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity Sold
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Revenue
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {reportData.topSellingItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{index + 1}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.totalQuantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(item.totalRevenue)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Branch Comparison (if all branches selected) */}
              {selectedBranch === 'all' && reportData.branchComparison.length > 0 && (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Branch Performance Comparison
                    </h3>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Branch
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Revenue
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Transactions
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Avg Transaction
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Inventory Value
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Low Stock Items
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.branchComparison.map((branch, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {branch.branchName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(branch.revenue)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {branch.transactions}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(branch.averageTransaction)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(branch.inventoryValue)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                              {branch.lowStockItems}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
