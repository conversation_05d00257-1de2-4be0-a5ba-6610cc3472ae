'use client'

import { useState, useEffect } from 'react'
import { inventoryService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface InventoryItem {
  id: string
  current_stock: number
  minimum_stock: number
  maximum_stock: number
  reorder_point: number
  ingredient: {
    id: string
    name: string
    code: string
    unit: string
    cost_per_unit: number
  }
  warehouse: {
    id: string
    name: string
    code: string
  }
}

interface UpdateStockModalProps {
  isOpen: boolean
  onClose: () => void
  item: InventoryItem | null
  onStockUpdated: () => void
}

export default function UpdateStockModal({ isOpen, onClose, item, onStockUpdated }: UpdateStockModalProps) {
  const { user } = useAuth()
  const [newStock, setNewStock] = useState('')
  const [movementType, setMovementType] = useState<'in' | 'out' | 'adjustment'>('adjustment')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && item) {
      setNewStock(item.current_stock.toString())
      setMovementType('adjustment')
      setNotes('')
      setError('')
    }
  }, [isOpen, item])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!item || !newStock) {
      setError('Please enter a valid stock quantity')
      return
    }

    const newStockValue = parseFloat(newStock)
    if (newStockValue < 0) {
      setError('Stock quantity cannot be negative')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      await inventoryService.updateStock(
        item.id,
        newStockValue,
        movementType,
        notes || `Stock ${movementType} via inventory management`,
        user?.id
      )

      onStockUpdated()
      handleClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update stock')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setNewStock('')
    setMovementType('adjustment')
    setNotes('')
    setError('')
    onClose()
  }

  if (!isOpen || !item) return null

  const stockDifference = parseFloat(newStock || '0') - item.current_stock
  const isIncrease = stockDifference > 0
  const isDecrease = stockDifference < 0

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Update Stock</h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <h4 className="font-medium text-gray-900">{item.ingredient.name}</h4>
            <p className="text-sm text-gray-600">
              Code: {item.ingredient.code} | Unit: {item.ingredient.unit}
            </p>
            <p className="text-sm text-gray-600">
              Current Stock: {item.current_stock.toLocaleString()} {item.ingredient.unit}
            </p>
            <p className="text-sm text-gray-600">
              Warehouse: {item.warehouse.name}
            </p>
          </div>

          {error && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Stock Quantity * ({item.ingredient.unit})
              </label>
              <input
                type="number"
                step="0.001"
                min="0"
                value={newStock}
                onChange={(e) => setNewStock(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter new stock quantity"
                required
              />
              {stockDifference !== 0 && (
                <p className={`text-sm mt-1 ${isIncrease ? 'text-green-600' : isDecrease ? 'text-red-600' : 'text-gray-600'}`}>
                  {isIncrease ? '+' : ''}{stockDifference.toLocaleString()} {item.ingredient.unit}
                  {isIncrease ? ' (increase)' : isDecrease ? ' (decrease)' : ''}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Movement Type
              </label>
              <select
                value={movementType}
                onChange={(e) => setMovementType(e.target.value as 'in' | 'out' | 'adjustment')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="adjustment">Stock Adjustment</option>
                <option value="in">Stock In (Received)</option>
                <option value="out">Stock Out (Used/Sold)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Optional notes about this stock update"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Updating...' : 'Update Stock'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
