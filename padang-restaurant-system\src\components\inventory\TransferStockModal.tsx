'use client'

import { useState, useEffect } from 'react'
import { inventoryService, warehouseService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface InventoryItem {
  id: string
  current_stock: number
  minimum_stock: number
  maximum_stock: number
  reorder_point: number
  ingredient: {
    id: string
    name: string
    code: string
    unit: string
    cost_per_unit: number
  }
  warehouse: {
    id: string
    name: string
    code: string
  }
}

interface Warehouse {
  id: string
  name: string
  code: string
  type: string
}

interface TransferStockModalProps {
  isOpen: boolean
  onClose: () => void
  item: InventoryItem | null
  onTransferCreated: () => void
}

export default function TransferStockModal({ isOpen, onClose, item, onTransferCreated }: TransferStockModalProps) {
  const { user } = useAuth()
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [selectedWarehouse, setSelectedWarehouse] = useState('')
  const [quantity, setQuantity] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen) {
      loadWarehouses()
      setSelectedWarehouse('')
      setQuantity('')
      setNotes('')
      setError('')
    }
  }, [isOpen])

  const loadWarehouses = async () => {
    try {
      const data = await warehouseService.getAllWarehouses()
      // Filter out the current warehouse
      const filteredWarehouses = data.filter(w => w.id !== item?.warehouse.id)
      setWarehouses(filteredWarehouses)
    } catch (err) {
      setError('Failed to load warehouses')
      console.error(err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!item || !selectedWarehouse || !quantity) {
      setError('Please fill in all required fields')
      return
    }

    const transferQuantity = parseFloat(quantity)
    if (transferQuantity <= 0) {
      setError('Transfer quantity must be greater than 0')
      return
    }

    if (transferQuantity > item.current_stock) {
      setError('Transfer quantity cannot exceed current stock')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const transferItems = [{
        ingredient_id: item.ingredient.id,
        quantity: transferQuantity,
        unit: item.ingredient.unit,
        notes: notes || `Transfer of ${item.ingredient.name}`
      }]

      await inventoryService.createTransfer(
        item.warehouse.id,
        selectedWarehouse,
        transferItems,
        user?.id || '',
        notes || `Transfer request for ${item.ingredient.name}`
      )

      onTransferCreated()
      handleClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create transfer')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedWarehouse('')
    setQuantity('')
    setNotes('')
    setError('')
    onClose()
  }

  if (!isOpen || !item) return null

  const selectedWarehouseData = warehouses.find(w => w.id === selectedWarehouse)

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Transfer Stock</h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <h4 className="font-medium text-gray-900">{item.ingredient.name}</h4>
            <p className="text-sm text-gray-600">
              Code: {item.ingredient.code} | Unit: {item.ingredient.unit}
            </p>
            <p className="text-sm text-gray-600">
              Available Stock: {item.current_stock.toLocaleString()} {item.ingredient.unit}
            </p>
            <p className="text-sm text-gray-600">
              From: {item.warehouse.name} ({item.warehouse.code})
            </p>
          </div>

          {error && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transfer To *
              </label>
              <select
                value={selectedWarehouse}
                onChange={(e) => setSelectedWarehouse(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="">Select destination warehouse</option>
                {warehouses.map((warehouse) => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name} ({warehouse.code}) - {warehouse.type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity * ({item.ingredient.unit})
              </label>
              <input
                type="number"
                step="0.001"
                min="0.001"
                max={item.current_stock}
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter transfer quantity"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Maximum: {item.current_stock.toLocaleString()} {item.ingredient.unit}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transfer Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Optional notes about this transfer"
              />
            </div>

            {selectedWarehouseData && (
              <div className="p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Transfer Summary:</strong><br />
                  {quantity && `${parseFloat(quantity).toLocaleString()} ${item.ingredient.unit}`} of {item.ingredient.name}<br />
                  From: {item.warehouse.name}<br />
                  To: {selectedWarehouseData.name}
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating Transfer...' : 'Create Transfer'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
