"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/inventory/AddStockModal */ \"(app-pages-browser)/./src/components/inventory/AddStockModal.tsx\");\n/* harmony import */ var _components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/inventory/UpdateStockModal */ \"(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx\");\n/* harmony import */ var _components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/inventory/TransferStockModal */ \"(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InventoryPage() {\n    var _warehouses_find;\n    _s();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [warehouses, setWarehouses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWarehouse, setSelectedWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lowStockItems, setLowStockItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInventoryLoading, setIsInventoryLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddStockModalOpen, setIsAddStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferStockModalOpen, setIsTransferStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWarehouses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadWarehouses]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.warehouseService.getAllWarehouses();\n                setWarehouses(data);\n                // Auto-select first warehouse if available and no warehouse is selected\n                if (data.length > 0 && !selectedWarehouse) {\n                    setSelectedWarehouse(data[0].id);\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';\n                setError(errorMessage);\n                console.error('Error loading warehouses:', err);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadWarehouses]\"], [\n        selectedWarehouse\n    ]);\n    const loadInventory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadInventory]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null) // Clear previous errors\n                ;\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.inventoryService.getWarehouseInventory(warehouseId);\n                setInventory(data || []) // Ensure we always have an array\n                ;\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory';\n                setError(errorMessage);\n                console.error('Error loading inventory:', err);\n                setInventory([]) // Reset inventory on error\n                ;\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadInventory]\"], []);\n    const loadLowStockItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadLowStockItems]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.inventoryService.getLowStockItems();\n                setLowStockItems(data || []);\n            } catch (err) {\n                console.error('Failed to load low stock items:', err);\n            // Don't set error state for this as it's not critical\n            }\n        }\n    }[\"InventoryPage.useCallback[loadLowStockItems]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            const initializeData = {\n                \"InventoryPage.useEffect.initializeData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        loadWarehouses(),\n                        loadLowStockItems()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"InventoryPage.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"InventoryPage.useEffect\"], [\n        loadWarehouses,\n        loadLowStockItems\n    ]);\n    // Load inventory when warehouse changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            if (selectedWarehouse) {\n                loadInventory(selectedWarehouse);\n            } else {\n                setInventory([]);\n            }\n        }\n    }[\"InventoryPage.useEffect\"], [\n        selectedWarehouse,\n        loadInventory\n    ]);\n    const getStockStatus = (item)=>{\n        if (item.current_stock <= item.minimum_stock) {\n            return {\n                status: 'critical',\n                color: 'text-red-600 bg-red-100'\n            };\n        } else if (item.current_stock <= item.reorder_point) {\n            return {\n                status: 'low',\n                color: 'text-yellow-600 bg-yellow-100'\n            };\n        } else {\n            return {\n                status: 'good',\n                color: 'text-green-600 bg-green-100'\n            };\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const calculateInventoryStats = ()=>{\n        const totalItems = inventory.length;\n        const lowStockCount = inventory.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        const reorderNeededCount = inventory.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const totalValue = inventory.reduce((total, item)=>{\n            const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n            return total + itemValue;\n        }, 0);\n        return {\n            totalItems,\n            lowStockCount,\n            reorderNeededCount,\n            totalValue\n        };\n    };\n    const handleWarehouseChange = (event)=>{\n        setSelectedWarehouse(event.target.value);\n    };\n    const handleAddStock = ()=>{\n        setIsAddStockModalOpen(true);\n    };\n    const handleBulkUpdate = ()=>{\n        setIsBulkUpdateModalOpen(true);\n    };\n    const handleUpdateStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsUpdateStockModalOpen(true);\n        }\n    };\n    const handleTransferStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsTransferStockModalOpen(true);\n        }\n    };\n    const handleStockUpdated = ()=>{\n        if (selectedWarehouse) {\n            loadInventory(selectedWarehouse);\n        }\n        loadLowStockItems();\n    };\n    const stats = calculateInventoryStats();\n    // Show loading spinner during initial load\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Inventory Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWarehouse,\n                                        onChange: handleWarehouseChange,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                        disabled: warehouses.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Warehouse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            warehouses.map((warehouse)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: warehouse.id,\n                                                    children: [\n                                                        warehouse.name,\n                                                        \" (\",\n                                                        warehouse.code,\n                                                        \")\"\n                                                    ]\n                                                }, warehouse.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddStock,\n                                                disabled: !selectedWarehouse,\n                                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Add Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleBulkUpdate,\n                                                disabled: !selectedWarehouse || inventory.length === 0,\n                                                className: \"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Bulk Update\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"absolute top-0 bottom-0 right-0 px-4 py-3\",\n                                \"aria-label\": \"Close error message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    lowStockItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 text-xl mr-3\",\n                                    \"aria-hidden\": \"true\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-red-800\",\n                                            children: \"Low Stock Alert\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: [\n                                                lowStockItems.length,\n                                                \" item\",\n                                                lowStockItems.length !== 1 ? 's' : '',\n                                                lowStockItems.length === 1 ? ' is' : ' are',\n                                                \" running low across all warehouses\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: stats.totalItems\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Low Stock Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: stats.lowStockCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCB0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: formatCurrency(stats.totalValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Reorder Needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: stats.reorderNeededCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Current Inventory\",\n                                        selectedWarehouse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                ((_warehouses_find = warehouses.find((w)=>w.id === selectedWarehouse)) === null || _warehouses_find === void 0 ? void 0 : _warehouses_find.name) || 'Unknown Warehouse'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            isInventoryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, this) : !selectedWarehouse ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"Please select a warehouse to view inventory\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this) : inventory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"No inventory items found for this warehouse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Ingredient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Min/Max\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: inventory.map((item)=>{\n                                                const stockStatus = getStockStatus(item);\n                                                const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.ingredient.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.ingredient.code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    item.current_stock.toLocaleString(),\n                                                                    \" \",\n                                                                    item.ingredient.unit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(stockStatus.color),\n                                                                children: stockStatus.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: [\n                                                                item.minimum_stock.toLocaleString(),\n                                                                \" / \",\n                                                                item.maximum_stock.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(itemValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpdateStock(item.id),\n                                                                    className: \"text-indigo-600 hover:text-indigo-900 mr-3 transition-colors\",\n                                                                    children: \"Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleTransferStock(item.id),\n                                                                    className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                    children: \"Transfer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isAddStockModalOpen,\n                        onClose: ()=>setIsAddStockModalOpen(false),\n                        warehouseId: selectedWarehouse,\n                        onStockAdded: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: isUpdateStockModalOpen,\n                        onClose: ()=>setIsUpdateStockModalOpen(false),\n                        item: selectedItem,\n                        onStockUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: isTransferStockModalOpen,\n                        onClose: ()=>setIsTransferStockModalOpen(false),\n                        item: selectedItem,\n                        onTransferCreated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"mXes4mXyA0gAywNKq7n/MQuDdtk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});