'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { menuService, salesService, branchService } from '@/lib/database'

interface MenuItem {
  id: string
  price: number
  is_available: boolean
  daily_quota: number
  current_sold: number
  menu_item: {
    id: string
    name: string
    description: string
    category: string
    base_price: number
    spice_level: number
    is_signature_dish: boolean
    image_url: string
  }
}

interface CartItem {
  menu_item_id: string
  name: string
  price: number
  quantity: number
  special_instructions: string
}

interface Branch {
  id: string
  name: string
  code: string
}

export default function POSPage() {
  const { profile } = useAuth()
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [selectedBranch, setSelectedBranch] = useState<string>('')
  const [cart, setCart] = useState<CartItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [paymentMethod, setPaymentMethod] = useState<string>('cash')
  const [discountAmount, setDiscountAmount] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [processingOrder, setProcessingOrder] = useState(false)

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    if (selectedBranch) {
      loadBranchMenu(selectedBranch)
    }
  }, [selectedBranch])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      const branchesData = await branchService.getAllBranches()
      setBranches(branchesData)
      
      // Set default branch based on user's branch or first available
      const defaultBranch = profile?.branch_id || (branchesData.length > 0 ? branchesData[0].id : '')
      setSelectedBranch(defaultBranch)
    } catch (err) {
      setError('Failed to load data')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const loadBranchMenu = async (branchId: string) => {
    try {
      const data = await menuService.getBranchMenu(branchId)
      setMenuItems(data)
    } catch (err) {
      setError('Failed to load menu')
      console.error(err)
    }
  }

  const addToCart = (menuItem: MenuItem) => {
    const existingItem = cart.find(item => item.menu_item_id === menuItem.menu_item.id)
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.menu_item_id === menuItem.menu_item.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ))
    } else {
      setCart([...cart, {
        menu_item_id: menuItem.menu_item.id,
        name: menuItem.menu_item.name,
        price: menuItem.price,
        quantity: 1,
        special_instructions: ''
      }])
    }
  }

  const updateCartItemQuantity = (menuItemId: string, quantity: number) => {
    if (quantity <= 0) {
      setCart(cart.filter(item => item.menu_item_id !== menuItemId))
    } else {
      setCart(cart.map(item =>
        item.menu_item_id === menuItemId
          ? { ...item, quantity }
          : item
      ))
    }
  }

  const updateSpecialInstructions = (menuItemId: string, instructions: string) => {
    setCart(cart.map(item =>
      item.menu_item_id === menuItemId
        ? { ...item, special_instructions: instructions }
        : item
    ))
  }

  const calculateSubtotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const calculateTax = () => {
    return calculateSubtotal() * 0.1 // 10% tax
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax() - discountAmount
  }

  const processOrder = async () => {
    if (!selectedBranch || cart.length === 0 || !profile?.id) {
      setError('Please select items and ensure all required fields are filled')
      return
    }

    try {
      setProcessingOrder(true)
      
      const customerInfo = {
        name: customerName,
        phone: customerPhone
      }

      const paymentInfo = {
        method: paymentMethod,
        tax_amount: calculateTax(),
        discount_amount: discountAmount
      }

      await salesService.createSalesTransaction(
        selectedBranch,
        cart,
        customerInfo,
        paymentInfo,
        profile.id
      )

      // Clear cart and customer info
      setCart([])
      setCustomerName('')
      setCustomerPhone('')
      setDiscountAmount(0)
      setError(null)
      
      alert('Order processed successfully!')
    } catch (err) {
      setError('Failed to process order')
      console.error(err)
    } finally {
      setProcessingOrder(false)
    }
  }

  const categories = ['all', ...new Set(menuItems.map(item => item.menu_item.category))]
  const filteredMenuItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.menu_item.category === selectedCategory)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getSpiceLevel = (level: number) => {
    return '🌶️'.repeat(level)
  }

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'manager', 'cashier']}>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'manager', 'cashier']}>
      <DashboardLayout>
        <div className="flex h-screen bg-gray-100">
          {/* Menu Section */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold text-gray-900">Point of Sale</h1>
                <select
                  value={selectedBranch}
                  onChange={(e) => setSelectedBranch(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Select Branch</option>
                  {branches.map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name} ({branch.code})
                    </option>
                  ))}
                </select>
              </div>

              {/* Category Filter */}
              <div className="flex space-x-2 mb-4">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      selectedCategory === category
                        ? 'bg-indigo-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}

            {/* Menu Items Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredMenuItems.map((item) => (
                <div
                  key={item.id}
                  className={`bg-white rounded-lg shadow-md p-4 cursor-pointer transition-transform hover:scale-105 ${
                    !item.is_available ? 'opacity-50' : ''
                  }`}
                  onClick={() => item.is_available && addToCart(item)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                      {item.menu_item.name}
                    </h3>
                    {item.menu_item.is_signature_dish && (
                      <span className="text-yellow-500 text-xl">⭐</span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {item.menu_item.description}
                  </p>
                  
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-lg font-bold text-indigo-600">
                      {formatCurrency(item.price)}
                    </span>
                    {item.menu_item.spice_level > 0 && (
                      <span className="text-sm">
                        {getSpiceLevel(item.menu_item.spice_level)}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Category: {item.menu_item.category}</span>
                    <span>
                      {item.current_sold}/{item.daily_quota || '∞'}
                    </span>
                  </div>
                  
                  {!item.is_available && (
                    <div className="mt-2 text-center text-red-600 font-medium">
                      Not Available
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Cart Section */}
          <div className="w-96 bg-white shadow-lg p-6 overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Order Summary</h2>
            
            {/* Customer Info */}
            <div className="mb-4 space-y-2">
              <input
                type="text"
                placeholder="Customer Name (Optional)"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <input
                type="tel"
                placeholder="Phone Number (Optional)"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            {/* Cart Items */}
            <div className="space-y-3 mb-4 max-h-64 overflow-y-auto">
              {cart.map((item) => (
                <div key={item.menu_item_id} className="border border-gray-200 rounded-md p-3">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                    <button
                      onClick={() => updateCartItemQuantity(item.menu_item_id, 0)}
                      className="text-red-500 hover:text-red-700 text-sm"
                    >
                      ✕
                    </button>
                  </div>
                  
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateCartItemQuantity(item.menu_item_id, item.quantity - 1)}
                        className="bg-gray-200 text-gray-700 w-6 h-6 rounded-full text-sm hover:bg-gray-300"
                      >
                        -
                      </button>
                      <span className="text-sm font-medium">{item.quantity}</span>
                      <button
                        onClick={() => updateCartItemQuantity(item.menu_item_id, item.quantity + 1)}
                        className="bg-gray-200 text-gray-700 w-6 h-6 rounded-full text-sm hover:bg-gray-300"
                      >
                        +
                      </button>
                    </div>
                    <span className="text-sm font-medium">
                      {formatCurrency(item.price * item.quantity)}
                    </span>
                  </div>
                  
                  <input
                    type="text"
                    placeholder="Special instructions..."
                    value={item.special_instructions}
                    onChange={(e) => updateSpecialInstructions(item.menu_item_id, e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  />
                </div>
              ))}
            </div>

            {/* Order Total */}
            <div className="border-t border-gray-200 pt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(calculateSubtotal())}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax (10%):</span>
                <span>{formatCurrency(calculateTax())}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Discount:</span>
                <input
                  type="number"
                  value={discountAmount}
                  onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
                  className="w-20 border border-gray-300 rounded-md px-2 py-1 text-sm text-right focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  min="0"
                />
              </div>
              <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                <span>Total:</span>
                <span>{formatCurrency(calculateTotal())}</span>
              </div>
            </div>

            {/* Payment Method */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Method
              </label>
              <select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="cash">Cash</option>
                <option value="card">Credit/Debit Card</option>
                <option value="e_wallet">E-Wallet</option>
                <option value="qris">QRIS</option>
                <option value="bank_transfer">Bank Transfer</option>
              </select>
            </div>

            {/* Process Order Button */}
            <button
              onClick={processOrder}
              disabled={cart.length === 0 || processingOrder}
              className="w-full mt-4 bg-indigo-600 text-white py-3 rounded-md font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {processingOrder ? 'Processing...' : `Process Order (${formatCurrency(calculateTotal())})`}
            </button>

            {/* Clear Cart Button */}
            <button
              onClick={() => setCart([])}
              disabled={cart.length === 0}
              className="w-full mt-2 bg-gray-600 text-white py-2 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Cart
            </button>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
