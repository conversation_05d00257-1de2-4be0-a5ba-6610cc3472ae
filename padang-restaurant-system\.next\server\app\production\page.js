/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/production/page";
exports.ids = ["app/production/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproduction%2Fpage&page=%2Fproduction%2Fpage&appPaths=%2Fproduction%2Fpage&pagePath=private-next-app-dir%2Fproduction%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproduction%2Fpage&page=%2Fproduction%2Fpage&appPaths=%2Fproduction%2Fpage&pagePath=private-next-app-dir%2Fproduction%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/production/page.tsx */ \"(rsc)/./src/app/production/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'production',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/production/page\",\n        pathname: \"/production\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproduction%2Fpage&page=%2Fproduction%2Fpage&appPaths=%2Fproduction%2Fpage&pagePath=private-next-app-dir%2Fproduction%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/production/page.tsx */ \"(rsc)/./src/app/production/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9kdWN0aW9uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccGFkYW5naHViX3N1cGFiYXNlXFxcXHBhZGFuZy1yZXN0YXVyYW50LXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3Rpb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwYWRhbmdodWJfc3VwYWJhc2VcXHBhZGFuZy1yZXN0YXVyYW50LXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccGFkYW5naHViX3N1cGFiYXNlXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Padang Restaurant System\",\n    description: \"Comprehensive restaurant management system for Padang restaurants\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/production/page.tsx":
/*!*************************************!*\
  !*** ./src/app/production/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\production\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcGFkYW5naHViX3N1cGFiYXNlJTVDJTVDcGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcGFkYW5naHViX3N1cGFiYXNlJTVDJTVDcGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3BhZGFuZ2h1Yl9zdXBhYmFzZSU1QyU1Q3BhZGFuZy1yZXN0YXVyYW50LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkQ6XFxcXHBhZGFuZ2h1Yl9zdXBhYmFzZVxcXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/production/page.tsx */ \"(ssr)/./src/app/production/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9kdWN0aW9uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccGFkYW5naHViX3N1cGFiYXNlXFxcXHBhZGFuZy1yZXN0YXVyYW50LXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHByb2R1Y3Rpb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cproduction%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/production/page.tsx":
/*!*************************************!*\
  !*** ./src/app/production/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(ssr)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProductionPage() {\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [recipes, setRecipes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batches, setBatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [kitchens, setKitchens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedKitchen, setSelectedKitchen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedRecipe, setSelectedRecipe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [plannedQuantity, setPlannedQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductionPage.useEffect\": ()=>{\n            loadInitialData();\n        }\n    }[\"ProductionPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductionPage.useEffect\": ()=>{\n            if (selectedKitchen) {\n                loadProductionBatches(selectedKitchen);\n            }\n        }\n    }[\"ProductionPage.useEffect\"], [\n        selectedKitchen\n    ]);\n    const loadInitialData = async ()=>{\n        try {\n            setLoading(true);\n            const [recipesData, kitchensData] = await Promise.all([\n                _lib_database__WEBPACK_IMPORTED_MODULE_5__.productionService.getAllRecipes(),\n                _lib_database__WEBPACK_IMPORTED_MODULE_5__.kitchenService.getAllKitchens()\n            ]);\n            setRecipes(recipesData);\n            setKitchens(kitchensData);\n            if (kitchensData.length > 0) {\n                setSelectedKitchen(kitchensData[0].id);\n            }\n        } catch (err) {\n            setError('Failed to load data');\n            console.error(err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadProductionBatches = async (kitchenId)=>{\n        try {\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.productionService.getProductionBatches(kitchenId);\n            setBatches(data);\n        } catch (err) {\n            console.error('Failed to load production batches:', err);\n        }\n    };\n    const createProductionBatch = async ()=>{\n        if (!selectedRecipe || !selectedKitchen || !profile?.id) {\n            setError('Please select recipe and kitchen');\n            return;\n        }\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.productionService.createProductionBatch(selectedKitchen, selectedRecipe, plannedQuantity, profile.id);\n            setShowCreateForm(false);\n            setSelectedRecipe('');\n            setPlannedQuantity(1);\n            loadProductionBatches(selectedKitchen);\n        } catch (err) {\n            setError('Failed to create production batch');\n            console.error(err);\n        }\n    };\n    const updateBatchStatus = async (batchId, newStatus)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.productionService.updateBatchStatus(batchId, newStatus, profile?.id);\n            loadProductionBatches(selectedKitchen);\n        } catch (err) {\n            setError('Failed to update batch status');\n            console.error(err);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'planned':\n                return 'bg-blue-100 text-blue-800';\n            case 'in_progress':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDifficultyStars = (level)=>{\n        return '★'.repeat(level) + '☆'.repeat(5 - level);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Production Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedKitchen,\n                                        onChange: (e)=>setSelectedKitchen(e.target.value),\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Kitchen\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            kitchens.map((kitchen)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: kitchen.id,\n                                                    children: [\n                                                        kitchen.name,\n                                                        \" (\",\n                                                        kitchen.code,\n                                                        \")\"\n                                                    ]\n                                                }, kitchen.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCreateForm(true),\n                                        className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700\",\n                                        children: \"Start Production\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Batches\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: batches.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"In Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: batches.filter((b)=>b.status === 'in_progress').length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"✅\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-green-600\",\n                                                            children: batches.filter((b)=>b.status === 'completed' && new Date(b.actual_end_time).toDateString() === new Date().toDateString()).length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"⭐\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Avg Quality Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: batches.filter((b)=>b.quality_score).length > 0 ? (batches.reduce((sum, b)=>sum + (b.quality_score || 0), 0) / batches.filter((b)=>b.quality_score).length).toFixed(1) : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Start New Production Batch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-4 sm:grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Recipe\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedRecipe,\n                                                onChange: (e)=>setSelectedRecipe(e.target.value),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select Recipe\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    recipes.map((recipe)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: recipe.id,\n                                                            children: [\n                                                                recipe.name,\n                                                                \" - \",\n                                                                getDifficultyStars(recipe.difficulty_level)\n                                                            ]\n                                                        }, recipe.id, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Planned Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: plannedQuantity,\n                                                onChange: (e)=>setPlannedQuantity(parseInt(e.target.value)),\n                                                min: \"1\",\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createProductionBatch,\n                                                className: \"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\",\n                                                children: \"Create Batch\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreateForm(false),\n                                                className: \"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Production Batches\",\n                                        selectedKitchen && kitchens.find((k)=>k.id === selectedKitchen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                kitchens.find((k)=>k.id === selectedKitchen)?.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Batch #\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Recipe\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Quantity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Started By\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: batches.map((batch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: batch.batch_number\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: batch.recipe.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: batch.recipe.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: [\n                                                                batch.actual_quantity || batch.planned_quantity,\n                                                                \" / \",\n                                                                batch.planned_quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(batch.status)}`,\n                                                                children: batch.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: batch.quality_score ? `${batch.quality_score}/10` : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: batch.starter?.full_name || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                batch.status === 'planned' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>updateBatchStatus(batch.id, 'in_progress'),\n                                                                    className: \"text-green-600 hover:text-green-900 mr-3\",\n                                                                    children: \"Start\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                batch.status === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>updateBatchStatus(batch.id, 'completed'),\n                                                                    className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                                                                    children: \"Complete\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-indigo-600 hover:text-indigo-900\",\n                                                                    children: \"Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, batch.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\production\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/production/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signIn } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        const { error } = await signIn(email, password);\n        if (error) {\n            setError(error.message);\n        }\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Padang Restaurant System\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm -space-y-px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Email address\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\",\n                                children: loading ? 'Signing in...' : 'Sign in'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoginForm */ \"(ssr)/./src/components/auth/LoginForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProtectedRoute({ children, allowedRoles, requireProfile = true }) {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 29,\n            columnNumber: 12\n        }, this);\n    }\n    if (requireProfile && !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile Setup Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Your account needs to be configured by an administrator before you can access the system.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    if (allowedRoles && profile && !allowedRoles.includes(profile.role)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"You don't have permission to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Your role: \",\n                            profile?.role\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { profile, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const navigation = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: '📊'\n        },\n        {\n            name: 'Inventory',\n            href: '/inventory',\n            icon: '📦'\n        },\n        {\n            name: 'Production',\n            href: '/production',\n            icon: '👨‍🍳'\n        },\n        {\n            name: 'Branches',\n            href: '/branches',\n            icon: '🏪'\n        },\n        {\n            name: 'POS',\n            href: '/pos',\n            icon: '💰'\n        },\n        {\n            name: 'Reports',\n            href: '/reports',\n            icon: '📈'\n        }\n    ];\n    // Filter navigation based on user role\n    const filteredNavigation = navigation.filter((item)=>{\n        if (!profile) return false;\n        switch(profile.role){\n            case 'admin':\n                return true // Admin can access everything\n                ;\n            case 'manager':\n                return ![\n                    'branches'\n                ].includes(item.href.replace('/', ''));\n            case 'staff':\n                return [\n                    'dashboard',\n                    'inventory',\n                    'production'\n                ].includes(item.href.replace('/', ''));\n            case 'cashier':\n                return [\n                    'dashboard',\n                    'pos'\n                ].includes(item.href.replace('/', ''));\n            default:\n                return false;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close sidebar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"✕\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                navigation: filteredNavigation,\n                                pathname: pathname\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: filteredNavigation,\n                        pathname: pathname\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\",\n                            onClick: ()=>setSidebarOpen(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Open sidebar\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                \"☰\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Padang Restaurant System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                profile?.full_name,\n                                                \" (\",\n                                                profile?.role,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: signOut,\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\nfunction SidebarContent({ navigation, pathname }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 flex-shrink-0 px-4 bg-indigo-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-white text-lg font-semibold\",\n                    children: \"PRS\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-2 py-4 space-y-1\",\n                    children: navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            className: `${isActive ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-3 text-lg\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session } })=>{\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        fetchProfile(session.user.id);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        await fetchProfile(session.user.id);\n                    } else {\n                        setProfile(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').select('*').eq('id', userId).single();\n            if (error) {\n                console.error('Error fetching profile:', error);\n            } else {\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching profile:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: 'No user logged in'\n        };\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').update(updates).eq('id', user.id);\n        if (!error) {\n            setProfile((prev)=>prev ? {\n                    ...prev,\n                    ...updates\n                } : null);\n        }\n        return {\n            error\n        };\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 127,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*)\n      `).eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses\n    // async getLowStockItems(branchId?: string) {\n    //   const { data, error } = await supabase\n    //     .rpc('get_low_stock_items', { branch_id: branchId })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getLowStockItems (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*)\n      `);\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        const lowStockItems = data?.filter((item)=>item.current_stock < item.minimum_stock).sort((a, b)=>a.current_stock - b.current_stock);\n        return lowStockItems;\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(unit, name),\n        warehouse:warehouses(name, code)\n      `).eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(`Failed to fetch inventory: ${fetchError.message}`);\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            throw new Error(`Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`);\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(`Failed to update inventory: ${updateError.message}`);\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: inventory.ingredient?.unit || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || `Stock ${movementType} - ${inventory.ingredient?.name} at ${inventory.warehouse?.name}`,\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(`Failed to record stock movement: ${movementError.message}`);\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(`Stock update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: ingredient?.unit || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId, limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*),\n        performer:profiles(full_name)\n      `).eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n        *,\n        from_warehouse:warehouses!from_warehouse_id(*),\n        to_warehouse:warehouses!to_warehouse_id(*),\n        requester:profiles!requested_by(*),\n        transfer_items(*, ingredient:ingredients(*))\n      `).eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(`from_warehouse_id.eq.${warehouseId},to_warehouse_id.eq.${warehouseId}`);\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer with enhanced validation\n    async approveTransfer (transferId, approvedBy, approvedItems) {\n        try {\n            // Validate transfer exists and is in pending status\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n          *,\n          from_warehouse:warehouses!from_warehouse_id(*),\n          to_warehouse:warehouses!to_warehouse_id(*),\n          transfer_items(*, ingredient:ingredients(*))\n        `).eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (transfer.status !== 'pending') {\n                throw new Error(`Cannot approve transfer with status: ${transfer.status}`);\n            }\n            // Validate stock availability for approved quantities\n            for (const approvedItem of approvedItems){\n                const transferItem = transfer.transfer_items.find((item)=>item.id === approvedItem.id);\n                if (!transferItem) {\n                    throw new Error(`Transfer item not found: ${approvedItem.id}`);\n                }\n                // Check current stock in source warehouse\n                const { data: inventory, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', transferItem.ingredient_id).single();\n                if (inventoryError) {\n                    throw new Error(`Cannot verify stock for ingredient: ${transferItem.ingredient.name}`);\n                }\n                if (inventory.current_stock < approvedItem.approved_quantity) {\n                    throw new Error(`Insufficient stock for ${transferItem.ingredient.name}. Available: ${inventory.current_stock}, Requested: ${approvedItem.approved_quantity}`);\n                }\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'approved',\n                approved_by: approvedBy,\n                approved_at: timestamp\n            }).eq('id', transferId);\n            if (transferError) {\n                throw new Error(`Failed to approve transfer: ${transferError.message}`);\n            }\n            // Update approved quantities for items\n            for (const item of approvedItems){\n                const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                    approved_quantity: item.approved_quantity\n                }).eq('id', item.id);\n                if (itemError) {\n                    throw new Error(`Failed to update approved quantity: ${itemError.message}`);\n                }\n            }\n            return {\n                success: true,\n                transferId,\n                approvedAt: timestamp,\n                approvedItems: approvedItems.length\n            };\n        } catch (error) {\n            throw new Error(`Transfer approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Complete transfer and update inventory levels\n    async completeTransfer (transferId, completedBy) {\n        try {\n            // Fetch transfer with all details\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n          *,\n          from_warehouse:warehouses!from_warehouse_id(*),\n          to_warehouse:warehouses!to_warehouse_id(*),\n          transfer_items(*, ingredient:ingredients(*))\n        `).eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {\n                throw new Error(`Cannot complete transfer with status: ${transfer.status}`);\n            }\n            const timestamp = new Date().toISOString();\n            const stockMovements = [];\n            // Process each transfer item\n            for (const item of transfer.transfer_items){\n                const approvedQty = item.approved_quantity || item.requested_quantity;\n                // Reduce stock from source warehouse\n                const { data: sourceInventory, error: sourceError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', item.ingredient_id).single();\n                if (sourceError) {\n                    throw new Error(`Source inventory not found for ${item.ingredient.name}`);\n                }\n                if (sourceInventory.current_stock < approvedQty) {\n                    throw new Error(`Insufficient stock for ${item.ingredient.name}. Available: ${sourceInventory.current_stock}, Required: ${approvedQty}`);\n                }\n                // Update source inventory\n                const { error: sourceUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: sourceInventory.current_stock - approvedQty,\n                    updated_at: timestamp\n                }).eq('id', sourceInventory.id);\n                if (sourceUpdateError) {\n                    throw new Error(`Failed to update source inventory: ${sourceUpdateError.message}`);\n                }\n                // Add stock movement for source (outbound)\n                stockMovements.push({\n                    warehouse_id: transfer.from_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_out',\n                    reference_id: transferId,\n                    notes: `Transfer out to ${transfer.to_warehouse.name} - ${item.ingredient.name}`,\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n                // Check if destination inventory exists\n                const { data: destInventory, error: destFetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.to_warehouse_id).eq('ingredient_id', item.ingredient_id).maybeSingle();\n                if (destFetchError) {\n                    throw new Error(`Error checking destination inventory: ${destFetchError.message}`);\n                }\n                if (destInventory) {\n                    // Update existing destination inventory\n                    const { error: destUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: destInventory.current_stock + approvedQty,\n                        updated_at: timestamp,\n                        last_restocked_at: timestamp\n                    }).eq('id', destInventory.id);\n                    if (destUpdateError) {\n                        throw new Error(`Failed to update destination inventory: ${destUpdateError.message}`);\n                    }\n                } else {\n                    // Create new destination inventory record\n                    const { error: destCreateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n                        warehouse_id: transfer.to_warehouse_id,\n                        ingredient_id: item.ingredient_id,\n                        current_stock: approvedQty,\n                        minimum_stock: 0,\n                        maximum_stock: approvedQty * 10,\n                        reorder_point: approvedQty * 0.2,\n                        last_restocked_at: timestamp\n                    });\n                    if (destCreateError) {\n                        throw new Error(`Failed to create destination inventory: ${destCreateError.message}`);\n                    }\n                }\n                // Add stock movement for destination (inbound)\n                stockMovements.push({\n                    warehouse_id: transfer.to_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_in',\n                    reference_id: transferId,\n                    notes: `Transfer in from ${transfer.from_warehouse.name} - ${item.ingredient.name}`,\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n            }\n            // Insert all stock movements\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n            if (movementError) {\n                throw new Error(`Failed to record stock movements: ${movementError.message}`);\n            }\n            // Update transfer status to completed\n            const { error: completeError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'completed',\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (completeError) {\n                throw new Error(`Failed to complete transfer: ${completeError.message}`);\n            }\n            return {\n                success: true,\n                transferId,\n                completedAt: timestamp,\n                itemsTransferred: transfer.transfer_items.length,\n                stockMovements: stockMovements.length\n            };\n        } catch (error) {\n            throw new Error(`Transfer completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Cancel transfer\n    async cancelTransfer (transferId, cancelledBy, reason) {\n        try {\n            // Validate transfer exists and can be cancelled\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select('*').eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (![\n                'pending',\n                'approved',\n                'in_transit'\n            ].includes(transfer.status)) {\n                throw new Error(`Cannot cancel transfer with status: ${transfer.status}`);\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: cancelError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'cancelled',\n                notes: transfer.notes ? `${transfer.notes}\\n\\nCancelled: ${reason || 'No reason provided'}` : `Cancelled: ${reason || 'No reason provided'}`,\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (cancelError) {\n                throw new Error(`Failed to cancel transfer: ${cancelError.message}`);\n            }\n            return {\n                success: true,\n                transferId,\n                cancelledAt: timestamp,\n                reason: reason || 'No reason provided'\n            };\n        } catch (error) {\n            throw new Error(`Transfer cancellation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n              *,\n              ingredient:ingredients(unit, name),\n              warehouse:warehouses(name, code)\n            `).eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Failed to fetch inventory: ${fetchError.message}`\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Failed to update inventory: ${updateError.message}`\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: inventory.ingredient?.unit || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || `Bulk stock ${update.movementType} - ${inventory.ingredient?.name}`,\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(`Failed to record stock movements: ${movementError.message}`);\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(`Bulk update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(`\n        *,\n        manager:profiles!fk_branches_manager(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(`\n        *,\n        manager:profiles!fk_branches_manager(*),\n        warehouses(*),\n        kitchens(*)\n      `).eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(`\n        *,\n        branch:branches(*),\n        manager:profiles(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(`\n        *,\n        manager:profiles(*)\n      `).eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(`\n        *,\n        category:ingredient_categories(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(`\n        *,\n        category:ingredient_categories(*)\n      `).eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(`\n        *,\n        recipe_ingredients(\n          *,\n          ingredient:ingredients(*)\n        )\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(`\n        *,\n        recipe_ingredients(\n          *,\n          ingredient:ingredients(*)\n        )\n      `).eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy) {\n        const batchNumber = `BATCH-${Date.now()}`;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString()\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(`\n        *,\n        recipe:recipes(*),\n        kitchen:kitchens(*),\n        starter:profiles!started_by(*),\n        completer:profiles!completed_by(*)\n      `);\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(`\n        *,\n        recipe:recipes(name)\n      `).eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(`\n        *,\n        branch:branches(*),\n        warehouse:warehouses(*),\n        head_chef:profiles(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(`\n        *,\n        branch:branches(*),\n        warehouse:warehouses(*),\n        head_chef:profiles(*)\n      `).eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(`\n        *,\n        branch:branches(*),\n        server:profiles!served_by(*),\n        sales_transaction_items(\n          *,\n          menu_item:menu_items(*)\n        )\n      `);\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = `TXN-${Date.now()}`;\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(`\n        *,\n        recipe:recipes(*)\n      `).eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(`\n        *,\n        menu_item:menu_items(\n          *,\n          recipe:recipes(*)\n        )\n      `).eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        current_stock,\n        minimum_stock,\n        ingredient:ingredients(cost_per_unit)\n      `).in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>sum + item.current_stock * (item.ingredient?.cost_per_unit || 0), 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId, limit = 10, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(`\n        quantity,\n        total_price,\n        menu_item:menu_items(name, category),\n        sales_transactions!inner(created_at, branch_id)\n      `).eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId, limit = 10, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://klgxgxjhjnojngrtcloe.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtsZ3hneGpoam5vam5ncnRjbG9lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1MzQ0NDAsImV4cCI6MjA2NzExMDQ0MH0.5F7hUxdV4XPRyfTrCYbCDXLQ6jzDiroSClLj1IcQj1s\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproduction%2Fpage&page=%2Fproduction%2Fpage&appPaths=%2Fproduction%2Fpage&pagePath=private-next-app-dir%2Fproduction%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();