import { supabase } from './supabase'

// Inventory Management Functions
export const inventoryService = {
  // Get all inventory items for a warehouse
  async getWarehouseInventory(warehouseId: string) {
    const { data, error } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get low stock items across all warehouses
  // async getLowStockItems(branchId?: string) {
  //   const { data, error } = await supabase
  //     .rpc('get_low_stock_items', { branch_id: branchId })
  
  //   if (error) throw error
  //   return data
  // },
  async getLowStockItems(branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
  
    if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }
  
    const { data, error } = await query
  
    if (error) throw error
    
    // Filter low stock items in JavaScript
    const lowStockItems = data?.filter(item => 
      item.current_stock < item.minimum_stock
    ).sort((a, b) => a.current_stock - b.current_stock)
  
    return lowStockItems
  },

  // async getLowStockItems(branchId?: string) {
  //   let query = supabase
  //     .from('inventory')
  //     .select(`
  //       *,
  //       ingredient:ingredients(*),
  //       warehouse:warehouses(*)
  //     `)
  //     .filter('current_stock', 'lt', 10)
  
  //   if (branchId) {
  //     query = query.eq('warehouses.branch_id', branchId)
  //   }
  
  //   const { data, error } = await query.order('current_stock', { ascending: true })
  
  //   if (error) throw error
  //   return data
  // },

  // Update stock levels
  async updateStock(inventoryId: string, newStock: number, movementType: string, notes?: string, performedBy?: string) {
    const { data: inventory, error: fetchError } = await supabase
      .from('inventory')
      .select('*, ingredient:ingredients(unit)')
      .eq('id', inventoryId)
      .single()

    if (fetchError) throw fetchError

    const quantity = newStock - inventory.current_stock

    // Update inventory
    const { error: updateError } = await supabase
      .from('inventory')
      .update({
        current_stock: newStock,
        updated_at: new Date().toISOString()
      })
      .eq('id', inventoryId)

    if (updateError) throw updateError

    // Record stock movement
    const { error: movementError } = await supabase
      .from('stock_movements')
      .insert({
        warehouse_id: inventory.warehouse_id,
        ingredient_id: inventory.ingredient_id,
        movement_type: movementType,
        quantity: Math.abs(quantity),
        unit: inventory.ingredient?.unit || 'kg',
        reference_type: 'manual_adjustment',
        notes,
        performed_by: performedBy
      })

    if (movementError) throw movementError

    return { success: true }
  },

  // Create new inventory record
  async createInventoryRecord(warehouseId: string, ingredientId: string, initialStock: number, notes?: string) {
    const { data, error } = await supabase
      .from('inventory')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        current_stock: initialStock,
        minimum_stock: 0,
        maximum_stock: initialStock * 10, // Default to 10x initial stock
        reorder_point: initialStock * 0.2, // Default to 20% of initial stock
      })
      .select()
      .single()

    if (error) throw error

    // Record initial stock movement
    const { data: ingredient } = await supabase
      .from('ingredients')
      .select('unit')
      .eq('id', ingredientId)
      .single()

    await supabase
      .from('stock_movements')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        movement_type: 'in',
        quantity: initialStock,
        unit: ingredient?.unit || 'kg',
        reference_type: 'initial_stock',
        notes: notes || 'Initial stock entry'
      })

    return data
  },

  // Get stock movements for a warehouse
  async getStockMovements(warehouseId: string, limit = 50) {
    const { data, error } = await supabase
      .from('stock_movements')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*),
        performer:profiles(full_name)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Create warehouse transfer
  async createTransfer(fromWarehouseId: string, toWarehouseId: string, items: any[], requestedBy: string, notes?: string) {
    // Create transfer record
    const { data: transfer, error: transferError } = await supabase
      .from('warehouse_transfers')
      .insert({
        from_warehouse_id: fromWarehouseId,
        to_warehouse_id: toWarehouseId,
        requested_by: requestedBy,
        total_items: items.length,
        notes,
        status: 'pending'
      })
      .select()
      .single()

    if (transferError) throw transferError

    // Create transfer items
    const transferItems = items.map(item => ({
      transfer_id: transfer.id,
      ingredient_id: item.ingredient_id,
      requested_quantity: item.quantity,
      unit: item.unit,
      notes: item.notes
    }))

    const { error: itemsError } = await supabase
      .from('transfer_items')
      .insert(transferItems)

    if (itemsError) throw itemsError

    return transfer
  },

  // Get pending transfers
  async getPendingTransfers(warehouseId?: string) {
    let query = supabase
      .from('warehouse_transfers')
      .select(`
        *,
        from_warehouse:warehouses!from_warehouse_id(*),
        to_warehouse:warehouses!to_warehouse_id(*),
        requester:profiles!requested_by(*),
        transfer_items(*, ingredient:ingredients(*))
      `)
      .eq('status', 'pending')

    if (warehouseId) {
      query = query.or(`from_warehouse_id.eq.${warehouseId},to_warehouse_id.eq.${warehouseId}`)
    }

    const { data, error } = await query.order('requested_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Approve transfer
  async approveTransfer(transferId: string, approvedBy: string, approvedItems: any[]) {
    // Update transfer status
    const { error: transferError } = await supabase
      .from('warehouse_transfers')
      .update({
        status: 'approved',
        approved_by: approvedBy,
        approved_at: new Date().toISOString()
      })
      .eq('id', transferId)

    if (transferError) throw transferError

    // Update approved quantities for items
    for (const item of approvedItems) {
      const { error: itemError } = await supabase
        .from('transfer_items')
        .update({
          approved_quantity: item.approved_quantity
        })
        .eq('id', item.id)

      if (itemError) throw itemError
    }

    return { success: true }
  }
}

// Branch Management Functions
// export const branchService = {
//   // Get all branches
//   async getAllBranches() {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*)
//       `)
//       .eq('is_active', true)
//       .order('name')

//     if (error) throw error
//     return data
//   },

//   // Get branch with details
//   async getBranchDetails(branchId: string) {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*),
//         warehouses(*),
//         kitchens(*)
//       `)
//       .eq('id', branchId)
//       .single()

//     if (error) throw error
//     return data
//   }
// }
export const branchService = {
  // Get all branches
  async getAllBranches() {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get branch with details
  async getBranchDetails(branchId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*),
        warehouses(*),
        kitchens(*)
      `)
      .eq('id', branchId)
      .single()

    if (error) throw error
    return data
  }
}

// Warehouse Management Functions
export const warehouseService = {
  // Get all warehouses
  async getAllWarehouses() {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        branch:branches(*),
        manager:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get warehouses by branch
  async getWarehousesByBranch(branchId: string) {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        manager:profiles(*)
      `)
      .eq('branch_id', branchId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Ingredient Management Functions
export const ingredientService = {
  // Get all ingredients
  async getAllIngredients() {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get ingredients by category
  async getIngredientsByCategory(categoryId: string) {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Production Management Functions
export const productionService = {
  // Get all recipes
  async getAllRecipes() {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get recipe details
  async getRecipeDetails(recipeId: string) {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('id', recipeId)
      .single()

    if (error) throw error
    return data
  },

  // Create production batch
  async createProductionBatch(kitchenId: string, recipeId: string, plannedQuantity: number, startedBy: string) {
    const batchNumber = `BATCH-${Date.now()}`

    const { data, error } = await supabase
      .from('production_batches')
      .insert({
        kitchen_id: kitchenId,
        recipe_id: recipeId,
        batch_number: batchNumber,
        planned_quantity: plannedQuantity,
        status: 'planned',
        started_by: startedBy,
        planned_start_time: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Get production batches
  async getProductionBatches(kitchenId?: string, status?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(*),
        starter:profiles!started_by(*),
        completer:profiles!completed_by(*)
      `)

    if (kitchenId) {
      query = query.eq('kitchen_id', kitchenId)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Update batch status
  async updateBatchStatus(batchId: string, status: string, userId?: string, actualQuantity?: number, qualityScore?: number, qualityNotes?: string) {
    const updates: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (status === 'in_progress') {
      updates.actual_start_time = new Date().toISOString()
    } else if (status === 'completed') {
      updates.actual_end_time = new Date().toISOString()
      updates.completed_by = userId
      if (actualQuantity) updates.actual_quantity = actualQuantity
      if (qualityScore) updates.quality_score = qualityScore
      if (qualityNotes) updates.quality_notes = qualityNotes
    }

    const { error } = await supabase
      .from('production_batches')
      .update(updates)
      .eq('id', batchId)

    if (error) throw error
    return { success: true }
  },

  // Record ingredient usage for batch
  async recordIngredientUsage(batchId: string, ingredientUsage: any[]) {
    const usageRecords = ingredientUsage.map(usage => ({
      batch_id: batchId,
      ingredient_id: usage.ingredient_id,
      planned_quantity: usage.planned_quantity,
      actual_quantity: usage.actual_quantity,
      unit: usage.unit,
      cost_per_unit: usage.cost_per_unit,
      total_cost: usage.actual_quantity * usage.cost_per_unit
    }))

    const { error } = await supabase
      .from('batch_ingredients_used')
      .insert(usageRecords)

    if (error) throw error
    return { success: true }
  },

  // Get kitchen production summary
  async getKitchenProductionSummary(kitchenId: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(name)
      `)
      .eq('kitchen_id', kitchenId)

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  }
}

// Kitchen Management Functions
export const kitchenService = {
  // Get all kitchens
  async getAllKitchens() {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get kitchen details
  async getKitchenDetails(kitchenId: string) {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('id', kitchenId)
      .single()

    if (error) throw error
    return data
  }
}

// Sales Management Functions
export const salesService = {
  // Get sales transactions
  async getSalesTransactions(branchId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('sales_transactions')
      .select(`
        *,
        branch:branches(*),
        server:profiles!served_by(*),
        sales_transaction_items(
          *,
          menu_item:menu_items(*)
        )
      `)

    if (branchId) {
      query = query.eq('branch_id', branchId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get daily sales summary
  async getDailySalesSummary(branchId: string, date?: string) {
    const targetDate = date || new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('daily_sales_summaries')
      .select('*')
      .eq('branch_id', branchId)
      .eq('date', targetDate)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Create sales transaction
  async createSalesTransaction(branchId: string, items: any[], customerInfo: any, paymentInfo: any, servedBy: string) {
    const transactionNumber = `TXN-${Date.now()}`
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('sales_transactions')
      .insert({
        branch_id: branchId,
        transaction_number: transactionNumber,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        total_amount: totalAmount,
        tax_amount: paymentInfo.tax_amount || 0,
        discount_amount: paymentInfo.discount_amount || 0,
        payment_method: paymentInfo.method,
        payment_status: 'completed',
        served_by: servedBy
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Create transaction items
    const transactionItems = items.map(item => ({
      transaction_id: transaction.id,
      menu_item_id: item.menu_item_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.quantity * item.unit_price,
      special_instructions: item.special_instructions
    }))

    const { error: itemsError } = await supabase
      .from('sales_transaction_items')
      .insert(transactionItems)

    if (itemsError) throw itemsError

    return transaction
  }
}

// Menu Management Functions
export const menuService = {
  // Get menu items
  async getMenuItems() {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        recipe:recipes(*)
      `)
      .eq('is_available', true)
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (error) throw error
    return data
  },

  // Get branch menu with pricing
  // async getBranchMenu(branchId: string) {
  //   const { data, error } = await supabase
  //     .from('branch_menu_pricing')
  //     .select(`
  //       *,
  //       menu_item:menu_items(
  //         *,
  //         recipe:recipes(*)
  //       )
  //     `)
  //     .eq('branch_id', branchId)
  //     .eq('is_available', true)
  //     .order('menu_item.category', { ascending: true })

  //   if (error) throw error
  //   return data
  // },
  async getBranchMenu(branchId: string) {
    const { data, error } = await supabase
      .from('branch_menu_pricing')
      .select(`
        *,
        menu_item:menu_items(
          *,
          recipe:recipes(*)
        )
      `)
      .eq('branch_id', branchId)
      .eq('is_available', true)
      .order('menu_item(category)', { ascending: true })
  
    if (error) throw error
    return data
  },

  // Update menu item availability
  async updateMenuItemAvailability(branchId: string, menuItemId: string, isAvailable: boolean) {
    const { error } = await supabase
      .from('branch_menu_pricing')
      .update({ is_available: isAvailable })
      .eq('branch_id', branchId)
      .eq('menu_item_id', menuItemId)

    if (error) throw error
    return { success: true }
  }
}

// // Analytics and Reporting Functions
// export const analyticsService = {
//   // Get branch performance metrics
//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     // Get sales data
//     const { data: salesData, error: salesError } = await supabase
//       .from('sales_transactions')
//       .select('total_amount, created_at, payment_method')
//       .eq('branch_id', branchId)
//       .gte('created_at', start)
//       .lte('created_at', end)

//     if (salesError) throw salesError

//     // Get inventory data
//     const { data: inventoryData, error: inventoryError } = await supabase
//       .from('inventory')
//       .select(`
//         current_stock,
//         minimum_stock,
//         ingredient:ingredients(cost_per_unit)
//       `)
//       .in('warehouse_id',
//         supabase
//           .from('warehouses')
//           .select('id')
//           .eq('branch_id', branchId)
//       )

//     if (inventoryError) throw inventoryError

//     // Calculate metrics
//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
//     const totalTransactions = salesData.length
//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
//     const inventoryValue = inventoryData.reduce((sum, item) =>
//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
//     )
//     const lowStockItems = inventoryData.filter(item =>
//       item.current_stock <= item.minimum_stock
//     ).length

//     return {
//       totalRevenue,
//       totalTransactions,
//       averageTransaction,
//       inventoryValue,
//       lowStockItems,
//       salesData,
//       inventoryData
//     }
//   },

//   // Get top selling items
//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     const { data, error } = await supabase
//       .from('sales_transaction_items')
//       .select(`
//         quantity,
//         total_price,
//         menu_item:menu_items(name, category),
//         transaction:sales_transactions!inner(created_at, branch_id)
//       `)
//       .eq('transaction.branch_id', branchId)
//       .gte('transaction.created_at', start)
//       .lte('transaction.created_at', end)

//     if (error) throw error

//     // Aggregate by menu item
//     const itemStats = data.reduce((acc: any, item) => {
//       const itemName = item.menu_item.name
//       if (!acc[itemName]) {
//         acc[itemName] = {
//           name: itemName,
//           category: item.menu_item.category,
//           totalQuantity: 0,
//           totalRevenue: 0
//         }
//       }
//       acc[itemName].totalQuantity += item.quantity
//       acc[itemName].totalRevenue += item.total_price
//       return acc
//     }, {})

//     return Object.values(itemStats)
//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
//       .slice(0, limit)
//   }
// }
// Analytics and Reporting Functions
export const analyticsService = {
  // Get branch performance metrics
  async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Get sales data
    const { data: salesData, error: salesError } = await supabase
      .from('sales_transactions')
      .select('total_amount, created_at, payment_method')
      .eq('branch_id', branchId)
      .gte('created_at', start)
      .lte('created_at', end)
    
    if (salesError) throw salesError
    
    // First get warehouse IDs for the branch
    const { data: warehouseIds, error: warehouseError } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)
    
    if (warehouseError) throw warehouseError
    
    // Get inventory data using warehouse IDs
    const warehouseIdList = warehouseIds.map(w => w.id)
    const { data: inventoryData, error: inventoryError } = await supabase
      .from('inventory')
      .select(`
        current_stock,
        minimum_stock,
        ingredient:ingredients(cost_per_unit)
      `)
      .in('warehouse_id', warehouseIdList)
    
    if (inventoryError) throw inventoryError
    
    // Calculate metrics
    const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
    const totalTransactions = salesData.length
    const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
    const inventoryValue = inventoryData.reduce((sum, item) =>
      sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
    )
    const lowStockItems = inventoryData.filter(item =>
      item.current_stock <= item.minimum_stock
    ).length
    
    return {
      totalRevenue,
      totalTransactions,
      averageTransaction,
      inventoryValue,
      lowStockItems,
      salesData,
      inventoryData
    }
  },

  // Get top selling items
  async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    const { data, error } = await supabase
      .from('sales_transaction_items')
      .select(`
        quantity,
        total_price,
        menu_item:menu_items(name, category),
        sales_transactions!inner(created_at, branch_id)
      `)
      .eq('sales_transactions.branch_id', branchId)
      .gte('sales_transactions.created_at', start)
      .lte('sales_transactions.created_at', end)
    
    if (error) throw error
    
    // Aggregate by menu item
    const itemStats = data.reduce((acc: any, item) => {
      const itemName = item.menu_item.name
      if (!acc[itemName]) {
        acc[itemName] = {
          name: itemName,
          category: item.menu_item.category,
          totalQuantity: 0,
          totalRevenue: 0
        }
      }
      acc[itemName].totalQuantity += item.quantity
      acc[itemName].totalRevenue += item.total_price
      return acc
    }, {})
    
    return Object.values(itemStats)
      .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
      .slice(0, limit)
  },

  // Alternative approach for top selling items using RPC if the above doesn't work
  async getTopSellingItemsRPC(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Call a stored procedure/function for complex aggregation
    const { data, error } = await supabase
      .rpc('get_top_selling_items', {
        branch_id: branchId,
        start_date: start,
        end_date: end,
        item_limit: limit
      })
    
    if (error) throw error
    return data
  }
}
