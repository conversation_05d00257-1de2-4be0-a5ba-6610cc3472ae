import { supabase } from './supabase'
import { logStockChange, logTransferOperation, logBulkOperation } from './auditLog'

// Inventory Management Functions
export const inventoryService = {
  // Get all inventory items for a warehouse
  async getWarehouseInventory(warehouseId: string) {
    const { data, error } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get low stock items across all warehouses (optimized with database filtering)
  async getLowStockItems(branchId?: string) {
    // Use a more efficient query with database-side filtering
    const { data, error } = await supabase
      .rpc('get_low_stock_items_optimized', {
        branch_id_param: branchId || null
      })

    if (error) {
      // Fallback to client-side filtering if RPC function doesn't exist
      console.warn('RPC function not available, using fallback query')
      return this.getLowStockItemsFallback(branchId)
    }

    return data || []
  },

  // Fallback method for low stock items
  async getLowStockItemsFallback(branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
      .filter('current_stock', 'lt', 'minimum_stock') // Database-side filtering

    if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query
      .order('current_stock', { ascending: true })
      .limit(100) // Limit results for performance

    if (error) throw error

    return data || []
  },

  // async getLowStockItems(branchId?: string) {
  //   let query = supabase
  //     .from('inventory')
  //     .select(`
  //       *,
  //       ingredient:ingredients(*),
  //       warehouse:warehouses(*)
  //     `)
  //     .filter('current_stock', 'lt', 10)
  
  //   if (branchId) {
  //     query = query.eq('warehouses.branch_id', branchId)
  //   }
  
  //   const { data, error } = await query.order('current_stock', { ascending: true })
  
  //   if (error) throw error
  //   return data
  // },

  // Update stock levels with enhanced validation and transaction safety
  async updateStock(inventoryId: string, newStock: number, movementType: string, notes?: string, performedBy?: string) {
    // Input validation
    if (!inventoryId || newStock < 0) {
      throw new Error('Invalid input: inventory ID is required and stock cannot be negative')
    }

    if (!['in', 'out', 'adjustment', 'waste'].includes(movementType)) {
      throw new Error('Invalid movement type')
    }

    // Fetch current inventory with detailed information
    const { data: inventory, error: fetchError } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(unit, name),
        warehouse:warehouses(name, code)
      `)
      .eq('id', inventoryId)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch inventory: ${fetchError.message}`)
    }

    const quantity = newStock - inventory.current_stock
    const isIncrease = quantity > 0

    // Business rule validation
    if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {
      throw new Error(`Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`)
    }

    // Check if this would create negative stock for outbound movements
    if (movementType === 'out' && newStock < 0) {
      throw new Error('Cannot reduce stock below zero')
    }

    try {
      // Use a transaction-like approach with error handling
      const timestamp = new Date().toISOString()

      // Update inventory record
      const { error: updateError } = await supabase
        .from('inventory')
        .update({
          current_stock: newStock,
          updated_at: timestamp,
          last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at
        })
        .eq('id', inventoryId)

      if (updateError) {
        throw new Error(`Failed to update inventory: ${updateError.message}`)
      }

      // Record stock movement with enhanced details
      const { error: movementError } = await supabase
        .from('stock_movements')
        .insert({
          warehouse_id: inventory.warehouse_id,
          ingredient_id: inventory.ingredient_id,
          movement_type: movementType,
          quantity: Math.abs(quantity),
          unit: inventory.ingredient?.unit || 'kg',
          reference_type: 'manual_adjustment',
          notes: notes || `Stock ${movementType} - ${inventory.ingredient?.name} at ${inventory.warehouse?.name}`,
          performed_by: performedBy,
          created_at: timestamp
        })

      if (movementError) {
        // Attempt to rollback the inventory update
        await supabase
          .from('inventory')
          .update({
            current_stock: inventory.current_stock,
            updated_at: inventory.updated_at
          })
          .eq('id', inventoryId)

        throw new Error(`Failed to record stock movement: ${movementError.message}`)
      }

      // Log the stock change for audit trail
      if (performedBy) {
        try {
          await logStockChange(
            movementType as 'stock_add' | 'stock_reduce' | 'stock_adjust' | 'stock_waste',
            inventoryId,
            performedBy,
            'staff', // Default role, should be passed from context
            {
              ingredient_name: inventory.ingredient?.name || 'Unknown',
              warehouse_name: inventory.warehouse?.name || 'Unknown',
              previous_stock: inventory.current_stock,
              new_stock: newStock,
              quantity_changed: Math.abs(quantity),
              unit: inventory.ingredient?.unit || 'kg',
              notes: notes
            },
            inventory.warehouse_id
          )
        } catch (auditError) {
          console.error('Failed to log stock change:', auditError)
          // Don't fail the operation if audit logging fails
        }
      }

      return {
        success: true,
        previousStock: inventory.current_stock,
        newStock: newStock,
        quantity: Math.abs(quantity),
        movementType,
        timestamp
      }
    } catch (error) {
      throw new Error(`Stock update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Create new inventory record
  async createInventoryRecord(warehouseId: string, ingredientId: string, initialStock: number, notes?: string) {
    const { data, error } = await supabase
      .from('inventory')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        current_stock: initialStock,
        minimum_stock: 0,
        maximum_stock: initialStock * 10, // Default to 10x initial stock
        reorder_point: initialStock * 0.2, // Default to 20% of initial stock
      })
      .select()
      .single()

    if (error) throw error

    // Record initial stock movement
    const { data: ingredient } = await supabase
      .from('ingredients')
      .select('unit')
      .eq('id', ingredientId)
      .single()

    await supabase
      .from('stock_movements')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        movement_type: 'in',
        quantity: initialStock,
        unit: ingredient?.unit || 'kg',
        reference_type: 'initial_stock',
        notes: notes || 'Initial stock entry'
      })

    return data
  },

  // Get stock movements for a warehouse
  async getStockMovements(warehouseId: string, limit = 50) {
    const { data, error } = await supabase
      .from('stock_movements')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*),
        performer:profiles(full_name)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Create warehouse transfer
  async createTransfer(fromWarehouseId: string, toWarehouseId: string, items: any[], requestedBy: string, notes?: string) {
    // Create transfer record
    const { data: transfer, error: transferError } = await supabase
      .from('warehouse_transfers')
      .insert({
        from_warehouse_id: fromWarehouseId,
        to_warehouse_id: toWarehouseId,
        requested_by: requestedBy,
        total_items: items.length,
        notes,
        status: 'pending'
      })
      .select()
      .single()

    if (transferError) throw transferError

    // Create transfer items
    const transferItems = items.map(item => ({
      transfer_id: transfer.id,
      ingredient_id: item.ingredient_id,
      requested_quantity: item.quantity,
      unit: item.unit,
      notes: item.notes
    }))

    const { error: itemsError } = await supabase
      .from('transfer_items')
      .insert(transferItems)

    if (itemsError) throw itemsError

    return transfer
  },

  // Get pending transfers
  async getPendingTransfers(warehouseId?: string) {
    let query = supabase
      .from('warehouse_transfers')
      .select(`
        *,
        from_warehouse:warehouses!from_warehouse_id(*),
        to_warehouse:warehouses!to_warehouse_id(*),
        requester:profiles!requested_by(*),
        transfer_items(*, ingredient:ingredients(*))
      `)
      .eq('status', 'pending')

    if (warehouseId) {
      query = query.or(`from_warehouse_id.eq.${warehouseId},to_warehouse_id.eq.${warehouseId}`)
    }

    const { data, error } = await query.order('requested_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Approve transfer with enhanced validation
  async approveTransfer(transferId: string, approvedBy: string, approvedItems: any[]) {
    try {
      // Validate transfer exists and is in pending status
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select(`
          *,
          from_warehouse:warehouses!from_warehouse_id(*),
          to_warehouse:warehouses!to_warehouse_id(*),
          transfer_items(*, ingredient:ingredients(*))
        `)
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (transfer.status !== 'pending') {
        throw new Error(`Cannot approve transfer with status: ${transfer.status}`)
      }

      // Validate stock availability for approved quantities
      for (const approvedItem of approvedItems) {
        const transferItem = transfer.transfer_items.find((item: any) => item.id === approvedItem.id)
        if (!transferItem) {
          throw new Error(`Transfer item not found: ${approvedItem.id}`)
        }

        // Check current stock in source warehouse
        const { data: inventory, error: inventoryError } = await supabase
          .from('inventory')
          .select('current_stock')
          .eq('warehouse_id', transfer.from_warehouse_id)
          .eq('ingredient_id', transferItem.ingredient_id)
          .single()

        if (inventoryError) {
          throw new Error(`Cannot verify stock for ingredient: ${transferItem.ingredient.name}`)
        }

        if (inventory.current_stock < approvedItem.approved_quantity) {
          throw new Error(`Insufficient stock for ${transferItem.ingredient.name}. Available: ${inventory.current_stock}, Requested: ${approvedItem.approved_quantity}`)
        }
      }

      const timestamp = new Date().toISOString()

      // Update transfer status
      const { error: transferError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'approved',
          approved_by: approvedBy,
          approved_at: timestamp
        })
        .eq('id', transferId)

      if (transferError) {
        throw new Error(`Failed to approve transfer: ${transferError.message}`)
      }

      // Update approved quantities for items
      for (const item of approvedItems) {
        const { error: itemError } = await supabase
          .from('transfer_items')
          .update({
            approved_quantity: item.approved_quantity
          })
          .eq('id', item.id)

        if (itemError) {
          throw new Error(`Failed to update approved quantity: ${itemError.message}`)
        }
      }

      return {
        success: true,
        transferId,
        approvedAt: timestamp,
        approvedItems: approvedItems.length
      }

    } catch (error) {
      throw new Error(`Transfer approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Complete transfer and update inventory levels
  async completeTransfer(transferId: string, completedBy: string) {
    try {
      // Fetch transfer with all details
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select(`
          *,
          from_warehouse:warehouses!from_warehouse_id(*),
          to_warehouse:warehouses!to_warehouse_id(*),
          transfer_items(*, ingredient:ingredients(*))
        `)
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {
        throw new Error(`Cannot complete transfer with status: ${transfer.status}`)
      }

      const timestamp = new Date().toISOString()
      const stockMovements: any[] = []

      // Process each transfer item
      for (const item of transfer.transfer_items) {
        const approvedQty = item.approved_quantity || item.requested_quantity

        // Reduce stock from source warehouse
        const { data: sourceInventory, error: sourceError } = await supabase
          .from('inventory')
          .select('*')
          .eq('warehouse_id', transfer.from_warehouse_id)
          .eq('ingredient_id', item.ingredient_id)
          .single()

        if (sourceError) {
          throw new Error(`Source inventory not found for ${item.ingredient.name}`)
        }

        if (sourceInventory.current_stock < approvedQty) {
          throw new Error(`Insufficient stock for ${item.ingredient.name}. Available: ${sourceInventory.current_stock}, Required: ${approvedQty}`)
        }

        // Update source inventory
        const { error: sourceUpdateError } = await supabase
          .from('inventory')
          .update({
            current_stock: sourceInventory.current_stock - approvedQty,
            updated_at: timestamp
          })
          .eq('id', sourceInventory.id)

        if (sourceUpdateError) {
          throw new Error(`Failed to update source inventory: ${sourceUpdateError.message}`)
        }

        // Add stock movement for source (outbound)
        stockMovements.push({
          warehouse_id: transfer.from_warehouse_id,
          ingredient_id: item.ingredient_id,
          movement_type: 'transfer',
          quantity: approvedQty,
          unit: item.unit,
          reference_type: 'transfer_out',
          reference_id: transferId,
          notes: `Transfer out to ${transfer.to_warehouse.name} - ${item.ingredient.name}`,
          performed_by: completedBy,
          created_at: timestamp
        })

        // Check if destination inventory exists
        const { data: destInventory, error: destFetchError } = await supabase
          .from('inventory')
          .select('*')
          .eq('warehouse_id', transfer.to_warehouse_id)
          .eq('ingredient_id', item.ingredient_id)
          .maybeSingle()

        if (destFetchError) {
          throw new Error(`Error checking destination inventory: ${destFetchError.message}`)
        }

        if (destInventory) {
          // Update existing destination inventory
          const { error: destUpdateError } = await supabase
            .from('inventory')
            .update({
              current_stock: destInventory.current_stock + approvedQty,
              updated_at: timestamp,
              last_restocked_at: timestamp
            })
            .eq('id', destInventory.id)

          if (destUpdateError) {
            throw new Error(`Failed to update destination inventory: ${destUpdateError.message}`)
          }
        } else {
          // Create new destination inventory record
          const { error: destCreateError } = await supabase
            .from('inventory')
            .insert({
              warehouse_id: transfer.to_warehouse_id,
              ingredient_id: item.ingredient_id,
              current_stock: approvedQty,
              minimum_stock: 0,
              maximum_stock: approvedQty * 10,
              reorder_point: approvedQty * 0.2,
              last_restocked_at: timestamp
            })

          if (destCreateError) {
            throw new Error(`Failed to create destination inventory: ${destCreateError.message}`)
          }
        }

        // Add stock movement for destination (inbound)
        stockMovements.push({
          warehouse_id: transfer.to_warehouse_id,
          ingredient_id: item.ingredient_id,
          movement_type: 'transfer',
          quantity: approvedQty,
          unit: item.unit,
          reference_type: 'transfer_in',
          reference_id: transferId,
          notes: `Transfer in from ${transfer.from_warehouse.name} - ${item.ingredient.name}`,
          performed_by: completedBy,
          created_at: timestamp
        })
      }

      // Insert all stock movements
      const { error: movementError } = await supabase
        .from('stock_movements')
        .insert(stockMovements)

      if (movementError) {
        throw new Error(`Failed to record stock movements: ${movementError.message}`)
      }

      // Update transfer status to completed
      const { error: completeError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'completed',
          completed_at: timestamp
        })
        .eq('id', transferId)

      if (completeError) {
        throw new Error(`Failed to complete transfer: ${completeError.message}`)
      }

      return {
        success: true,
        transferId,
        completedAt: timestamp,
        itemsTransferred: transfer.transfer_items.length,
        stockMovements: stockMovements.length
      }

    } catch (error) {
      throw new Error(`Transfer completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Cancel transfer
  async cancelTransfer(transferId: string, cancelledBy: string, reason?: string) {
    try {
      // Validate transfer exists and can be cancelled
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select('*')
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (!['pending', 'approved', 'in_transit'].includes(transfer.status)) {
        throw new Error(`Cannot cancel transfer with status: ${transfer.status}`)
      }

      const timestamp = new Date().toISOString()

      // Update transfer status
      const { error: cancelError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'cancelled',
          notes: transfer.notes ? `${transfer.notes}\n\nCancelled: ${reason || 'No reason provided'}` : `Cancelled: ${reason || 'No reason provided'}`,
          completed_at: timestamp
        })
        .eq('id', transferId)

      if (cancelError) {
        throw new Error(`Failed to cancel transfer: ${cancelError.message}`)
      }

      return {
        success: true,
        transferId,
        cancelledAt: timestamp,
        reason: reason || 'No reason provided'
      }

    } catch (error) {
      throw new Error(`Transfer cancellation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Bulk update stock levels for multiple items
  async bulkUpdateStock(updates: Array<{
    inventoryId: string;
    newStock: number;
    movementType: string;
    notes?: string;
  }>, performedBy?: string) {
    if (!updates || updates.length === 0) {
      throw new Error('No updates provided')
    }

    const results: Array<{
      inventoryId: string;
      success: boolean;
      error?: string;
      previousStock?: number;
      newStock?: number;
    }> = []

    const timestamp = new Date().toISOString()
    const stockMovements: any[] = []

    try {
      // Process each update
      for (const update of updates) {
        try {
          // Validate input
          if (!update.inventoryId || update.newStock < 0) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: 'Invalid input: inventory ID is required and stock cannot be negative'
            })
            continue
          }

          // Fetch current inventory
          const { data: inventory, error: fetchError } = await supabase
            .from('inventory')
            .select(`
              *,
              ingredient:ingredients(unit, name),
              warehouse:warehouses(name, code)
            `)
            .eq('id', update.inventoryId)
            .single()

          if (fetchError) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Failed to fetch inventory: ${fetchError.message}`
            })
            continue
          }

          const quantity = update.newStock - inventory.current_stock

          // Business rule validation
          if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`
            })
            continue
          }

          // Update inventory
          const { error: updateError } = await supabase
            .from('inventory')
            .update({
              current_stock: update.newStock,
              updated_at: timestamp,
              last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at
            })
            .eq('id', update.inventoryId)

          if (updateError) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Failed to update inventory: ${updateError.message}`
            })
            continue
          }

          // Prepare stock movement record
          stockMovements.push({
            warehouse_id: inventory.warehouse_id,
            ingredient_id: inventory.ingredient_id,
            movement_type: update.movementType,
            quantity: Math.abs(quantity),
            unit: inventory.ingredient?.unit || 'kg',
            reference_type: 'bulk_adjustment',
            notes: update.notes || `Bulk stock ${update.movementType} - ${inventory.ingredient?.name}`,
            performed_by: performedBy,
            created_at: timestamp
          })

          results.push({
            inventoryId: update.inventoryId,
            success: true,
            previousStock: inventory.current_stock,
            newStock: update.newStock
          })

        } catch (error) {
          results.push({
            inventoryId: update.inventoryId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      // Insert all stock movements in batch
      if (stockMovements.length > 0) {
        const { error: movementError } = await supabase
          .from('stock_movements')
          .insert(stockMovements)

        if (movementError) {
          throw new Error(`Failed to record stock movements: ${movementError.message}`)
        }
      }

      const successCount = results.filter(r => r.success).length
      const failureCount = results.filter(r => !r.success).length

      return {
        success: failureCount === 0,
        totalUpdates: updates.length,
        successCount,
        failureCount,
        results,
        timestamp
      }

    } catch (error) {
      throw new Error(`Bulk update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Get comprehensive low stock alerts with reorder suggestions
  async getLowStockAlertsWithReorderSuggestions(branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)

    if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query

    if (error) throw error

    // Process and categorize alerts
    const alerts = data?.map(item => {
      const stockLevel = item.current_stock
      const minStock = item.minimum_stock
      const reorderPoint = item.reorder_point
      const maxStock = item.maximum_stock

      let alertLevel: 'critical' | 'low' | 'reorder' | 'good' = 'good'
      let suggestedReorderQuantity = 0
      let daysUntilStockout = null

      // Determine alert level
      if (stockLevel <= 0) {
        alertLevel = 'critical'
      } else if (stockLevel <= minStock) {
        alertLevel = 'critical'
      } else if (stockLevel <= reorderPoint) {
        alertLevel = 'reorder'
      } else if (stockLevel <= minStock * 1.5) {
        alertLevel = 'low'
      }

      // Calculate suggested reorder quantity
      if (alertLevel !== 'good') {
        const targetStock = maxStock || (minStock * 3)
        suggestedReorderQuantity = Math.max(0, targetStock - stockLevel)
      }

      // Estimate days until stockout (simplified calculation)
      // In a real system, you'd use historical consumption data
      if (stockLevel > 0 && minStock > 0) {
        const avgDailyConsumption = minStock / 30 // Rough estimate
        if (avgDailyConsumption > 0) {
          daysUntilStockout = Math.floor(stockLevel / avgDailyConsumption)
        }
      }

      return {
        ...item,
        alertLevel,
        suggestedReorderQuantity,
        daysUntilStockout,
        stockPercentage: maxStock > 0 ? (stockLevel / maxStock) * 100 : 0
      }
    }).filter(item => item.alertLevel !== 'good')
      .sort((a, b) => {
        // Sort by alert level priority, then by days until stockout
        const alertPriority = { critical: 0, low: 1, reorder: 2, good: 3 }
        const aPriority = alertPriority[a.alertLevel]
        const bPriority = alertPriority[b.alertLevel]

        if (aPriority !== bPriority) {
          return aPriority - bPriority
        }

        // If same alert level, sort by days until stockout (ascending)
        if (a.daysUntilStockout !== null && b.daysUntilStockout !== null) {
          return a.daysUntilStockout - b.daysUntilStockout
        }

        return 0
      })

    return alerts || []
  },

  // Get inventory performance metrics
  async getInventoryMetrics(warehouseId?: string, branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(cost_per_unit),
        warehouse:warehouses(branch_id)
      `)

    if (warehouseId) {
      query = query.eq('warehouse_id', warehouseId)
    } else if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query

    if (error) throw error

    if (!data || data.length === 0) {
      return {
        totalItems: 0,
        totalValue: 0,
        lowStockCount: 0,
        criticalStockCount: 0,
        reorderNeededCount: 0,
        averageStockLevel: 0,
        turnoverRate: 0
      }
    }

    const totalItems = data.length
    const totalValue = data.reduce((sum, item) => {
      const cost = item.ingredient?.cost_per_unit || 0
      return sum + (item.current_stock * cost)
    }, 0)

    const lowStockCount = data.filter(item =>
      item.current_stock <= item.minimum_stock && item.current_stock > 0
    ).length

    const criticalStockCount = data.filter(item =>
      item.current_stock <= 0
    ).length

    const reorderNeededCount = data.filter(item =>
      item.current_stock <= item.reorder_point
    ).length

    const averageStockLevel = data.reduce((sum, item) => {
      const maxStock = item.maximum_stock || item.minimum_stock * 3
      const stockPercentage = maxStock > 0 ? (item.current_stock / maxStock) * 100 : 0
      return sum + stockPercentage
    }, 0) / totalItems

    return {
      totalItems,
      totalValue,
      lowStockCount,
      criticalStockCount,
      reorderNeededCount,
      averageStockLevel: Math.round(averageStockLevel),
      turnoverRate: 0 // Would need historical data to calculate
    }
  },

  // Generate automatic reorder suggestions
  async generateReorderSuggestions(warehouseId?: string, branchId?: string) {
    const alerts = await this.getLowStockAlertsWithReorderSuggestions(branchId)

    const suggestions = alerts
      .filter(item => item.suggestedReorderQuantity > 0)
      .map(item => ({
        inventoryId: item.id,
        ingredientId: item.ingredient.id,
        ingredientName: item.ingredient.name,
        ingredientCode: item.ingredient.code,
        warehouseId: item.warehouse_id,
        warehouseName: item.warehouse.name,
        currentStock: item.current_stock,
        minimumStock: item.minimum_stock,
        reorderPoint: item.reorder_point,
        maximumStock: item.maximum_stock,
        suggestedQuantity: item.suggestedReorderQuantity,
        estimatedCost: (item.ingredient.cost_per_unit || 0) * item.suggestedReorderQuantity,
        priority: item.alertLevel,
        daysUntilStockout: item.daysUntilStockout,
        unit: item.ingredient.unit
      }))
      .sort((a, b) => {
        const priorityOrder = { critical: 0, low: 1, reorder: 2 }
        return priorityOrder[a.priority as keyof typeof priorityOrder] -
               priorityOrder[b.priority as keyof typeof priorityOrder]
      })

    const totalEstimatedCost = suggestions.reduce((sum, item) => sum + item.estimatedCost, 0)
    const criticalCount = suggestions.filter(item => item.priority === 'critical').length
    const lowCount = suggestions.filter(item => item.priority === 'low').length
    const reorderCount = suggestions.filter(item => item.priority === 'reorder').length

    return {
      suggestions,
      summary: {
        totalItems: suggestions.length,
        totalEstimatedCost,
        criticalCount,
        lowCount,
        reorderCount
      }
    }
  }
}

// Branch Management Functions
// export const branchService = {
//   // Get all branches
//   async getAllBranches() {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*)
//       `)
//       .eq('is_active', true)
//       .order('name')

//     if (error) throw error
//     return data
//   },

//   // Get branch with details
//   async getBranchDetails(branchId: string) {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*),
//         warehouses(*),
//         kitchens(*)
//       `)
//       .eq('id', branchId)
//       .single()

//     if (error) throw error
//     return data
//   }
// }
export const branchService = {
  // Get all branches
  async getAllBranches() {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get branch with details
  async getBranchDetails(branchId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*),
        warehouses(*),
        kitchens(*)
      `)
      .eq('id', branchId)
      .single()

    if (error) throw error
    return data
  }
}

// Warehouse Management Functions
export const warehouseService = {
  // Get all warehouses
  async getAllWarehouses() {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        branch:branches(*),
        manager:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get warehouses by branch
  async getWarehousesByBranch(branchId: string) {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        manager:profiles(*)
      `)
      .eq('branch_id', branchId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Ingredient Management Functions
export const ingredientService = {
  // Get all ingredients
  async getAllIngredients() {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get ingredients by category
  async getIngredientsByCategory(categoryId: string) {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Production Management Functions
export const productionService = {
  // Get all recipes
  async getAllRecipes() {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get recipe details
  async getRecipeDetails(recipeId: string) {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('id', recipeId)
      .single()

    if (error) throw error
    return data
  },

  // Create production batch
  async createProductionBatch(kitchenId: string, recipeId: string, plannedQuantity: number, startedBy: string) {
    const batchNumber = `BATCH-${Date.now()}`

    const { data, error } = await supabase
      .from('production_batches')
      .insert({
        kitchen_id: kitchenId,
        recipe_id: recipeId,
        batch_number: batchNumber,
        planned_quantity: plannedQuantity,
        status: 'planned',
        started_by: startedBy,
        planned_start_time: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Get production batches
  async getProductionBatches(kitchenId?: string, status?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(*),
        starter:profiles!started_by(*),
        completer:profiles!completed_by(*)
      `)

    if (kitchenId) {
      query = query.eq('kitchen_id', kitchenId)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Update batch status
  async updateBatchStatus(batchId: string, status: string, userId?: string, actualQuantity?: number, qualityScore?: number, qualityNotes?: string) {
    const updates: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (status === 'in_progress') {
      updates.actual_start_time = new Date().toISOString()
    } else if (status === 'completed') {
      updates.actual_end_time = new Date().toISOString()
      updates.completed_by = userId
      if (actualQuantity) updates.actual_quantity = actualQuantity
      if (qualityScore) updates.quality_score = qualityScore
      if (qualityNotes) updates.quality_notes = qualityNotes
    }

    const { error } = await supabase
      .from('production_batches')
      .update(updates)
      .eq('id', batchId)

    if (error) throw error
    return { success: true }
  },

  // Record ingredient usage for batch
  async recordIngredientUsage(batchId: string, ingredientUsage: any[]) {
    const usageRecords = ingredientUsage.map(usage => ({
      batch_id: batchId,
      ingredient_id: usage.ingredient_id,
      planned_quantity: usage.planned_quantity,
      actual_quantity: usage.actual_quantity,
      unit: usage.unit,
      cost_per_unit: usage.cost_per_unit,
      total_cost: usage.actual_quantity * usage.cost_per_unit
    }))

    const { error } = await supabase
      .from('batch_ingredients_used')
      .insert(usageRecords)

    if (error) throw error
    return { success: true }
  },

  // Get kitchen production summary
  async getKitchenProductionSummary(kitchenId: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(name)
      `)
      .eq('kitchen_id', kitchenId)

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  }
}

// Kitchen Management Functions
export const kitchenService = {
  // Get all kitchens
  async getAllKitchens() {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get kitchen details
  async getKitchenDetails(kitchenId: string) {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('id', kitchenId)
      .single()

    if (error) throw error
    return data
  }
}

// Sales Management Functions
export const salesService = {
  // Get sales transactions
  async getSalesTransactions(branchId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('sales_transactions')
      .select(`
        *,
        branch:branches(*),
        server:profiles!served_by(*),
        sales_transaction_items(
          *,
          menu_item:menu_items(*)
        )
      `)

    if (branchId) {
      query = query.eq('branch_id', branchId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get daily sales summary
  async getDailySalesSummary(branchId: string, date?: string) {
    const targetDate = date || new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('daily_sales_summaries')
      .select('*')
      .eq('branch_id', branchId)
      .eq('date', targetDate)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Create sales transaction
  async createSalesTransaction(branchId: string, items: any[], customerInfo: any, paymentInfo: any, servedBy: string) {
    const transactionNumber = `TXN-${Date.now()}`
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('sales_transactions')
      .insert({
        branch_id: branchId,
        transaction_number: transactionNumber,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        total_amount: totalAmount,
        tax_amount: paymentInfo.tax_amount || 0,
        discount_amount: paymentInfo.discount_amount || 0,
        payment_method: paymentInfo.method,
        payment_status: 'completed',
        served_by: servedBy
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Create transaction items
    const transactionItems = items.map(item => ({
      transaction_id: transaction.id,
      menu_item_id: item.menu_item_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.quantity * item.unit_price,
      special_instructions: item.special_instructions
    }))

    const { error: itemsError } = await supabase
      .from('sales_transaction_items')
      .insert(transactionItems)

    if (itemsError) throw itemsError

    return transaction
  }
}

// Menu Management Functions
export const menuService = {
  // Get menu items
  async getMenuItems() {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        recipe:recipes(*)
      `)
      .eq('is_available', true)
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (error) throw error
    return data
  },

  // Get branch menu with pricing
  // async getBranchMenu(branchId: string) {
  //   const { data, error } = await supabase
  //     .from('branch_menu_pricing')
  //     .select(`
  //       *,
  //       menu_item:menu_items(
  //         *,
  //         recipe:recipes(*)
  //       )
  //     `)
  //     .eq('branch_id', branchId)
  //     .eq('is_available', true)
  //     .order('menu_item.category', { ascending: true })

  //   if (error) throw error
  //   return data
  // },
  async getBranchMenu(branchId: string) {
    const { data, error } = await supabase
      .from('branch_menu_pricing')
      .select(`
        *,
        menu_item:menu_items(
          *,
          recipe:recipes(*)
        )
      `)
      .eq('branch_id', branchId)
      .eq('is_available', true)
      .order('menu_item(category)', { ascending: true })
  
    if (error) throw error
    return data
  },

  // Update menu item availability
  async updateMenuItemAvailability(branchId: string, menuItemId: string, isAvailable: boolean) {
    const { error } = await supabase
      .from('branch_menu_pricing')
      .update({ is_available: isAvailable })
      .eq('branch_id', branchId)
      .eq('menu_item_id', menuItemId)

    if (error) throw error
    return { success: true }
  }
}

// // Analytics and Reporting Functions
// export const analyticsService = {
//   // Get branch performance metrics
//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     // Get sales data
//     const { data: salesData, error: salesError } = await supabase
//       .from('sales_transactions')
//       .select('total_amount, created_at, payment_method')
//       .eq('branch_id', branchId)
//       .gte('created_at', start)
//       .lte('created_at', end)

//     if (salesError) throw salesError

//     // Get inventory data
//     const { data: inventoryData, error: inventoryError } = await supabase
//       .from('inventory')
//       .select(`
//         current_stock,
//         minimum_stock,
//         ingredient:ingredients(cost_per_unit)
//       `)
//       .in('warehouse_id',
//         supabase
//           .from('warehouses')
//           .select('id')
//           .eq('branch_id', branchId)
//       )

//     if (inventoryError) throw inventoryError

//     // Calculate metrics
//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
//     const totalTransactions = salesData.length
//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
//     const inventoryValue = inventoryData.reduce((sum, item) =>
//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
//     )
//     const lowStockItems = inventoryData.filter(item =>
//       item.current_stock <= item.minimum_stock
//     ).length

//     return {
//       totalRevenue,
//       totalTransactions,
//       averageTransaction,
//       inventoryValue,
//       lowStockItems,
//       salesData,
//       inventoryData
//     }
//   },

//   // Get top selling items
//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     const { data, error } = await supabase
//       .from('sales_transaction_items')
//       .select(`
//         quantity,
//         total_price,
//         menu_item:menu_items(name, category),
//         transaction:sales_transactions!inner(created_at, branch_id)
//       `)
//       .eq('transaction.branch_id', branchId)
//       .gte('transaction.created_at', start)
//       .lte('transaction.created_at', end)

//     if (error) throw error

//     // Aggregate by menu item
//     const itemStats = data.reduce((acc: any, item) => {
//       const itemName = item.menu_item.name
//       if (!acc[itemName]) {
//         acc[itemName] = {
//           name: itemName,
//           category: item.menu_item.category,
//           totalQuantity: 0,
//           totalRevenue: 0
//         }
//       }
//       acc[itemName].totalQuantity += item.quantity
//       acc[itemName].totalRevenue += item.total_price
//       return acc
//     }, {})

//     return Object.values(itemStats)
//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
//       .slice(0, limit)
//   }
// }
// Analytics and Reporting Functions
export const analyticsService = {
  // Get branch performance metrics
  async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Get sales data
    const { data: salesData, error: salesError } = await supabase
      .from('sales_transactions')
      .select('total_amount, created_at, payment_method')
      .eq('branch_id', branchId)
      .gte('created_at', start)
      .lte('created_at', end)
    
    if (salesError) throw salesError
    
    // First get warehouse IDs for the branch
    const { data: warehouseIds, error: warehouseError } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)
    
    if (warehouseError) throw warehouseError
    
    // Get inventory data using warehouse IDs
    const warehouseIdList = warehouseIds.map(w => w.id)
    const { data: inventoryData, error: inventoryError } = await supabase
      .from('inventory')
      .select(`
        current_stock,
        minimum_stock,
        ingredient:ingredients(cost_per_unit)
      `)
      .in('warehouse_id', warehouseIdList)
    
    if (inventoryError) throw inventoryError
    
    // Calculate metrics
    const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
    const totalTransactions = salesData.length
    const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
    const inventoryValue = inventoryData.reduce((sum, item) =>
      sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
    )
    const lowStockItems = inventoryData.filter(item =>
      item.current_stock <= item.minimum_stock
    ).length
    
    return {
      totalRevenue,
      totalTransactions,
      averageTransaction,
      inventoryValue,
      lowStockItems,
      salesData,
      inventoryData
    }
  },

  // Get top selling items
  async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    const { data, error } = await supabase
      .from('sales_transaction_items')
      .select(`
        quantity,
        total_price,
        menu_item:menu_items(name, category),
        sales_transactions!inner(created_at, branch_id)
      `)
      .eq('sales_transactions.branch_id', branchId)
      .gte('sales_transactions.created_at', start)
      .lte('sales_transactions.created_at', end)
    
    if (error) throw error
    
    // Aggregate by menu item
    const itemStats = data.reduce((acc: any, item) => {
      const itemName = item.menu_item.name
      if (!acc[itemName]) {
        acc[itemName] = {
          name: itemName,
          category: item.menu_item.category,
          totalQuantity: 0,
          totalRevenue: 0
        }
      }
      acc[itemName].totalQuantity += item.quantity
      acc[itemName].totalRevenue += item.total_price
      return acc
    }, {})
    
    return Object.values(itemStats)
      .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
      .slice(0, limit)
  },

  // Alternative approach for top selling items using RPC if the above doesn't work
  async getTopSellingItemsRPC(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Call a stored procedure/function for complex aggregation
    const { data, error } = await supabase
      .rpc('get_top_selling_items', {
        branch_id: branchId,
        start_date: start,
        end_date: end,
        item_limit: limit
      })
    
    if (error) throw error
    return data
  }
}
