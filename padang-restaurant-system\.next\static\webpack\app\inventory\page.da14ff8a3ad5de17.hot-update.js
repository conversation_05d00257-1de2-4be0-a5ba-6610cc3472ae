"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/inventory/AddStockModal */ \"(app-pages-browser)/./src/components/inventory/AddStockModal.tsx\");\n/* harmony import */ var _components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/inventory/UpdateStockModal */ \"(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx\");\n/* harmony import */ var _components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/inventory/TransferStockModal */ \"(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InventoryPage() {\n    var _warehouses_find;\n    _s();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [warehouses, setWarehouses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWarehouse, setSelectedWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lowStockItems, setLowStockItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInventoryLoading, setIsInventoryLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddStockModalOpen, setIsAddStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferStockModalOpen, setIsTransferStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWarehouses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadWarehouses]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.warehouseService.getAllWarehouses();\n                setWarehouses(data);\n                // Auto-select first warehouse if available and no warehouse is selected\n                if (data.length > 0 && !selectedWarehouse) {\n                    setSelectedWarehouse(data[0].id);\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';\n                setError(errorMessage);\n                console.error('Error loading warehouses:', err);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadWarehouses]\"], [\n        selectedWarehouse\n    ]);\n    const loadInventory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadInventory]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null) // Clear previous errors\n                ;\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.inventoryService.getWarehouseInventory(warehouseId);\n                setInventory(data || []) // Ensure we always have an array\n                ;\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory';\n                setError(errorMessage);\n                console.error('Error loading inventory:', err);\n                setInventory([]) // Reset inventory on error\n                ;\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadInventory]\"], []);\n    const loadLowStockItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadLowStockItems]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.inventoryService.getLowStockItems();\n                setLowStockItems(data || []);\n            } catch (err) {\n                console.error('Failed to load low stock items:', err);\n            // Don't set error state for this as it's not critical\n            }\n        }\n    }[\"InventoryPage.useCallback[loadLowStockItems]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            const initializeData = {\n                \"InventoryPage.useEffect.initializeData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        loadWarehouses(),\n                        loadLowStockItems()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"InventoryPage.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"InventoryPage.useEffect\"], [\n        loadWarehouses,\n        loadLowStockItems\n    ]);\n    // Load inventory when warehouse changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            if (selectedWarehouse) {\n                loadInventory(selectedWarehouse);\n            } else {\n                setInventory([]);\n            }\n        }\n    }[\"InventoryPage.useEffect\"], [\n        selectedWarehouse,\n        loadInventory\n    ]);\n    const getStockStatus = (item)=>{\n        if (item.current_stock <= item.minimum_stock) {\n            return {\n                status: 'critical',\n                color: 'text-red-600 bg-red-100'\n            };\n        } else if (item.current_stock <= item.reorder_point) {\n            return {\n                status: 'low',\n                color: 'text-yellow-600 bg-yellow-100'\n            };\n        } else {\n            return {\n                status: 'good',\n                color: 'text-green-600 bg-green-100'\n            };\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const calculateInventoryStats = ()=>{\n        const totalItems = inventory.length;\n        const lowStockCount = inventory.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        const reorderNeededCount = inventory.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const totalValue = inventory.reduce((total, item)=>{\n            const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n            return total + itemValue;\n        }, 0);\n        return {\n            totalItems,\n            lowStockCount,\n            reorderNeededCount,\n            totalValue\n        };\n    };\n    const handleWarehouseChange = (event)=>{\n        setSelectedWarehouse(event.target.value);\n    };\n    const handleAddStock = ()=>{\n        setIsAddStockModalOpen(true);\n    };\n    const handleUpdateStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsUpdateStockModalOpen(true);\n        }\n    };\n    const handleTransferStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsTransferStockModalOpen(true);\n        }\n    };\n    const handleStockUpdated = ()=>{\n        if (selectedWarehouse) {\n            loadInventory(selectedWarehouse);\n        }\n        loadLowStockItems();\n    };\n    const stats = calculateInventoryStats();\n    // Show loading spinner during initial load\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Inventory Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWarehouse,\n                                        onChange: handleWarehouseChange,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                        disabled: warehouses.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Warehouse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            warehouses.map((warehouse)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: warehouse.id,\n                                                    children: [\n                                                        warehouse.name,\n                                                        \" (\",\n                                                        warehouse.code,\n                                                        \")\"\n                                                    ]\n                                                }, warehouse.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddStock,\n                                        disabled: !selectedWarehouse,\n                                        className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                        children: \"Add Stock\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"absolute top-0 bottom-0 right-0 px-4 py-3\",\n                                \"aria-label\": \"Close error message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this),\n                    lowStockItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 text-xl mr-3\",\n                                    \"aria-hidden\": \"true\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-red-800\",\n                                            children: \"Low Stock Alert\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: [\n                                                lowStockItems.length,\n                                                \" item\",\n                                                lowStockItems.length !== 1 ? 's' : '',\n                                                lowStockItems.length === 1 ? ' is' : ' are',\n                                                \" running low across all warehouses\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: stats.totalItems\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Low Stock Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: stats.lowStockCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCB0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: formatCurrency(stats.totalValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Reorder Needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: stats.reorderNeededCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Current Inventory\",\n                                        selectedWarehouse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                ((_warehouses_find = warehouses.find((w)=>w.id === selectedWarehouse)) === null || _warehouses_find === void 0 ? void 0 : _warehouses_find.name) || 'Unknown Warehouse'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            isInventoryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this) : !selectedWarehouse ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"Please select a warehouse to view inventory\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this) : inventory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"No inventory items found for this warehouse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Ingredient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Min/Max\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: inventory.map((item)=>{\n                                                const stockStatus = getStockStatus(item);\n                                                const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.ingredient.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.ingredient.code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    item.current_stock.toLocaleString(),\n                                                                    \" \",\n                                                                    item.ingredient.unit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(stockStatus.color),\n                                                                children: stockStatus.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: [\n                                                                item.minimum_stock.toLocaleString(),\n                                                                \" / \",\n                                                                item.maximum_stock.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(itemValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpdateStock(item.id),\n                                                                    className: \"text-indigo-600 hover:text-indigo-900 mr-3 transition-colors\",\n                                                                    children: \"Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleTransferStock(item.id),\n                                                                    className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                    children: \"Transfer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isAddStockModalOpen,\n                        onClose: ()=>setIsAddStockModalOpen(false),\n                        warehouseId: selectedWarehouse,\n                        onStockAdded: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: isUpdateStockModalOpen,\n                        onClose: ()=>setIsUpdateStockModalOpen(false),\n                        item: selectedItem,\n                        onStockUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: isTransferStockModalOpen,\n                        onClose: ()=>setIsTransferStockModalOpen(false),\n                        item: selectedItem,\n                        onTransferCreated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"mXes4mXyA0gAywNKq7n/MQuDdtk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW52ZW50b3J5L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0Q7QUFDUjtBQUNhO0FBQ0k7QUFDRDtBQUNNO0FBQ0k7QUFFUDtBQW9DcEQsU0FBU1c7UUE0VERDOztJQTNUckIsTUFBTSxFQUFFQyxPQUFPLEVBQUUsR0FBR1YsOERBQU9BO0lBQzNCLE1BQU0sQ0FBQ1csV0FBV0MsYUFBYSxHQUFHZiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUM5RCxNQUFNLENBQUNZLFlBQVlJLGNBQWMsR0FBR2hCLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDaUIsbUJBQW1CQyxxQkFBcUIsR0FBR2xCLCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQ21CLGVBQWVDLGlCQUFpQixHQUFHcEIsK0NBQVFBLENBQWtCLEVBQUU7SUFDdEUsTUFBTSxDQUFDcUIsV0FBV0MsYUFBYSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDdUIsb0JBQW9CQyxzQkFBc0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3lCLE9BQU9DLFNBQVMsR0FBRzFCLCtDQUFRQSxDQUFnQjtJQUVsRCxlQUFlO0lBQ2YsTUFBTSxDQUFDMkIscUJBQXFCQyx1QkFBdUIsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQzZCLHdCQUF3QkMsMEJBQTBCLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNyRSxNQUFNLENBQUMrQiwwQkFBMEJDLDRCQUE0QixHQUFHaEMsK0NBQVFBLENBQUM7SUFDekUsTUFBTSxDQUFDaUMsdUJBQXVCQyx5QkFBeUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ21DLGNBQWNDLGdCQUFnQixHQUFHcEMsK0NBQVFBLENBQXVCO0lBRXZFLE1BQU1xQyxpQkFBaUJuQyxrREFBV0E7cURBQUM7WUFDakMsSUFBSTtnQkFDRixNQUFNb0MsT0FBTyxNQUFNNUIsMkRBQWdCQSxDQUFDNkIsZ0JBQWdCO2dCQUNwRHZCLGNBQWNzQjtnQkFDZCx3RUFBd0U7Z0JBQ3hFLElBQUlBLEtBQUtFLE1BQU0sR0FBRyxLQUFLLENBQUN2QixtQkFBbUI7b0JBQ3pDQyxxQkFBcUJvQixJQUFJLENBQUMsRUFBRSxDQUFDRyxFQUFFO2dCQUNqQztZQUNGLEVBQUUsT0FBT0MsS0FBSztnQkFDWixNQUFNQyxlQUFlRCxlQUFlRSxRQUFRRixJQUFJRyxPQUFPLEdBQUc7Z0JBQzFEbkIsU0FBU2lCO2dCQUNURyxRQUFRckIsS0FBSyxDQUFDLDZCQUE2QmlCO1lBQzdDO1FBQ0Y7b0RBQUc7UUFBQ3pCO0tBQWtCO0lBRXRCLE1BQU04QixnQkFBZ0I3QyxrREFBV0E7b0RBQUMsT0FBTzhDO1lBQ3ZDLElBQUksQ0FBQ0EsYUFBYTtZQUVsQixJQUFJO2dCQUNGeEIsc0JBQXNCO2dCQUN0QkUsU0FBUyxNQUFNLHdCQUF3Qjs7Z0JBQ3ZDLE1BQU1ZLE9BQU8sTUFBTTdCLDJEQUFnQkEsQ0FBQ3dDLHFCQUFxQixDQUFDRDtnQkFDMURqQyxhQUFhdUIsUUFBUSxFQUFFLEVBQUUsaUNBQWlDOztZQUM1RCxFQUFFLE9BQU9JLEtBQUs7Z0JBQ1osTUFBTUMsZUFBZUQsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO2dCQUMxRG5CLFNBQVNpQjtnQkFDVEcsUUFBUXJCLEtBQUssQ0FBQyw0QkFBNEJpQjtnQkFDMUMzQixhQUFhLEVBQUUsRUFBRSwyQkFBMkI7O1lBQzlDLFNBQVU7Z0JBQ1JTLHNCQUFzQjtZQUN4QjtRQUNGO21EQUFHLEVBQUU7SUFFTCxNQUFNMEIsb0JBQW9CaEQsa0RBQVdBO3dEQUFDO1lBQ3BDLElBQUk7Z0JBQ0YsTUFBTW9DLE9BQU8sTUFBTTdCLDJEQUFnQkEsQ0FBQzBDLGdCQUFnQjtnQkFDcEQvQixpQkFBaUJrQixRQUFRLEVBQUU7WUFDN0IsRUFBRSxPQUFPSSxLQUFLO2dCQUNaSSxRQUFRckIsS0FBSyxDQUFDLG1DQUFtQ2lCO1lBQ2pELHNEQUFzRDtZQUN4RDtRQUNGO3VEQUFHLEVBQUU7SUFFTCxlQUFlO0lBQ2Z6QyxnREFBU0E7bUNBQUM7WUFDUixNQUFNbUQ7MERBQWlCO29CQUNyQjlCLGFBQWE7b0JBQ2IsTUFBTStCLFFBQVFDLEdBQUcsQ0FBQzt3QkFDaEJqQjt3QkFDQWE7cUJBQ0Q7b0JBQ0Q1QixhQUFhO2dCQUNmOztZQUVBOEI7UUFDRjtrQ0FBRztRQUFDZjtRQUFnQmE7S0FBa0I7SUFFdEMsd0NBQXdDO0lBQ3hDakQsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSWdCLG1CQUFtQjtnQkFDckI4QixjQUFjOUI7WUFDaEIsT0FBTztnQkFDTEYsYUFBYSxFQUFFO1lBQ2pCO1FBQ0Y7a0NBQUc7UUFBQ0U7UUFBbUI4QjtLQUFjO0lBRXJDLE1BQU1RLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxLQUFLQyxhQUFhLElBQUlELEtBQUtFLGFBQWEsRUFBRTtZQUM1QyxPQUFPO2dCQUFFQyxRQUFRO2dCQUFZQyxPQUFPO1lBQTBCO1FBQ2hFLE9BQU8sSUFBSUosS0FBS0MsYUFBYSxJQUFJRCxLQUFLSyxhQUFhLEVBQUU7WUFDbkQsT0FBTztnQkFBRUYsUUFBUTtnQkFBT0MsT0FBTztZQUFnQztRQUNqRSxPQUFPO1lBQ0wsT0FBTztnQkFBRUQsUUFBUTtnQkFBUUMsT0FBTztZQUE4QjtRQUNoRTtJQUNGO0lBRUEsTUFBTUUsaUJBQWlCLENBQUNDO1FBQ3RCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyx1QkFBdUI7WUFDdkJDLHVCQUF1QjtRQUN6QixHQUFHQyxNQUFNLENBQUNQO0lBQ1o7SUFFQSxNQUFNUSwwQkFBMEI7UUFDOUIsTUFBTUMsYUFBYTFELFVBQVUwQixNQUFNO1FBQ25DLE1BQU1pQyxnQkFBZ0IzRCxVQUFVNEQsTUFBTSxDQUFDbEIsQ0FBQUEsT0FBUUEsS0FBS0MsYUFBYSxJQUFJRCxLQUFLRSxhQUFhLEVBQUVsQixNQUFNO1FBQy9GLE1BQU1tQyxxQkFBcUI3RCxVQUFVNEQsTUFBTSxDQUFDbEIsQ0FBQUEsT0FBUUEsS0FBS0MsYUFBYSxJQUFJRCxLQUFLSyxhQUFhLEVBQUVyQixNQUFNO1FBQ3BHLE1BQU1vQyxhQUFhOUQsVUFBVStELE1BQU0sQ0FBQyxDQUFDQyxPQUFPdEI7WUFDMUMsTUFBTXVCLFlBQVl2QixLQUFLQyxhQUFhLEdBQUlELENBQUFBLEtBQUt3QixVQUFVLENBQUNDLGFBQWEsSUFBSTtZQUN6RSxPQUFPSCxRQUFRQztRQUNqQixHQUFHO1FBRUgsT0FBTztZQUNMUDtZQUNBQztZQUNBRTtZQUNBQztRQUNGO0lBQ0Y7SUFFQSxNQUFNTSx3QkFBd0IsQ0FBQ0M7UUFDN0JqRSxxQkFBcUJpRSxNQUFNQyxNQUFNLENBQUNDLEtBQUs7SUFDekM7SUFFQSxNQUFNQyxpQkFBaUI7UUFDckIxRCx1QkFBdUI7SUFDekI7SUFFQSxNQUFNMkQsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU1oQyxPQUFPMUMsVUFBVTJFLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpELEVBQUUsS0FBSytDO1FBQzFDLElBQUloQyxNQUFNO1lBQ1JwQixnQkFBZ0JvQjtZQUNoQjFCLDBCQUEwQjtRQUM1QjtJQUNGO0lBRUEsTUFBTTZELHNCQUFzQixDQUFDSDtRQUMzQixNQUFNaEMsT0FBTzFDLFVBQVUyRSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVqRCxFQUFFLEtBQUsrQztRQUMxQyxJQUFJaEMsTUFBTTtZQUNScEIsZ0JBQWdCb0I7WUFDaEJ4Qiw0QkFBNEI7UUFDOUI7SUFDRjtJQUVBLE1BQU00RCxxQkFBcUI7UUFDekIsSUFBSTNFLG1CQUFtQjtZQUNyQjhCLGNBQWM5QjtRQUNoQjtRQUNBaUM7SUFDRjtJQUVBLE1BQU0yQyxRQUFRdEI7SUFFZCwyQ0FBMkM7SUFDM0MsSUFBSWxELFdBQVc7UUFDYixxQkFDRSw4REFBQ2pCLHVFQUFjQTtZQUFDMEYsY0FBYztnQkFBQztnQkFBUztnQkFBVzthQUFRO3NCQUN6RCw0RUFBQ3pGLDBFQUFlQTswQkFDZCw0RUFBQzBGO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3pCO0lBRUEscUJBQ0UsOERBQUM1Rix1RUFBY0E7UUFBQzBGLGNBQWM7WUFBQztZQUFTO1lBQVc7U0FBUTtrQkFDekQsNEVBQUN6RiwwRUFBZUE7c0JBQ2QsNEVBQUMwRjtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUdELFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUNDYixPQUFPcEU7d0NBQ1BrRixVQUFVakI7d0NBQ1ZjLFdBQVU7d0NBQ1ZJLFVBQVV4RixXQUFXNEIsTUFBTSxLQUFLOzswREFFaEMsOERBQUM2RDtnREFBT2hCLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEJ6RSxXQUFXMEYsR0FBRyxDQUFDLENBQUNDLDBCQUNmLDhEQUFDRjtvREFBMEJoQixPQUFPa0IsVUFBVTlELEVBQUU7O3dEQUMzQzhELFVBQVVDLElBQUk7d0RBQUM7d0RBQUdELFVBQVVFLElBQUk7d0RBQUM7O21EQUR2QkYsVUFBVTlELEVBQUU7Ozs7Ozs7Ozs7O2tEQUs3Qiw4REFBQ2lFO3dDQUNDQyxTQUFTckI7d0NBQ1RjLFVBQVUsQ0FBQ25GO3dDQUNYK0UsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU9KdkUsdUJBQ0MsOERBQUNzRTt3QkFBSUMsV0FBVTt3QkFBMkVZLE1BQUs7OzBDQUM3Riw4REFBQ0M7Z0NBQUtiLFdBQVU7MENBQW1CdkU7Ozs7OzswQ0FDbkMsOERBQUNpRjtnQ0FDQ0MsU0FBUyxJQUFNakYsU0FBUztnQ0FDeEJzRSxXQUFVO2dDQUNWYyxjQUFXOzBDQUVYLDRFQUFDRDtvQ0FBS2IsV0FBVTs4Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTXBDN0UsY0FBY3FCLE1BQU0sR0FBRyxtQkFDdEIsOERBQUN1RDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTtvQ0FBNEJlLGVBQVk7OENBQU87Ozs7Ozs4Q0FDOUQsOERBQUNoQjs7c0RBQ0MsOERBQUNpQjs0Q0FBR2hCLFdBQVU7c0RBQW1DOzs7Ozs7c0RBQ2pELDhEQUFDaUI7NENBQUVqQixXQUFVOztnREFDVjdFLGNBQWNxQixNQUFNO2dEQUFDO2dEQUFNckIsY0FBY3FCLE1BQU0sS0FBSyxJQUFJLE1BQU07Z0RBQzlEckIsY0FBY3FCLE1BQU0sS0FBSyxJQUFJLFFBQVE7Z0RBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRdkQsOERBQUN1RDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQVdlLGVBQVk7OERBQU87Ozs7Ozs7Ozs7OzBEQUUvQyw4REFBQ2hCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDa0I7O3NFQUNDLDhEQUFDQzs0REFBR25CLFdBQVU7c0VBQTZDOzs7Ozs7c0VBRzNELDhEQUFDb0I7NERBQUdwQixXQUFVO3NFQUNYSCxNQUFNckIsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVE3Qiw4REFBQ3VCO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQVdlLGVBQVk7OERBQU87Ozs7Ozs7Ozs7OzBEQUUvQyw4REFBQ2hCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDa0I7O3NFQUNDLDhEQUFDQzs0REFBR25CLFdBQVU7c0VBQTZDOzs7Ozs7c0VBRzNELDhEQUFDb0I7NERBQUdwQixXQUFVO3NFQUNYSCxNQUFNcEIsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFoQyw4REFBQ3NCO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQVdlLGVBQVk7OERBQU87Ozs7Ozs7Ozs7OzBEQUUvQyw4REFBQ2hCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDa0I7O3NFQUNDLDhEQUFDQzs0REFBR25CLFdBQVU7c0VBQTZDOzs7Ozs7c0VBRzNELDhEQUFDb0I7NERBQUdwQixXQUFVO3NFQUNYbEMsZUFBZStCLE1BQU1qQixVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUTVDLDhEQUFDbUI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTtvREFBV2UsZUFBWTs4REFBTzs7Ozs7Ozs7Ozs7MERBRS9DLDhEQUFDaEI7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNrQjs7c0VBQ0MsOERBQUNDOzREQUFHbkIsV0FBVTtzRUFBNkM7Ozs7OztzRUFHM0QsOERBQUNvQjs0REFBR3BCLFdBQVU7c0VBQ1hILE1BQU1sQixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FVdkMsOERBQUNvQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDZ0I7b0NBQUdoQixXQUFVOzt3Q0FBOEM7d0NBRXpEL0UsbUNBQ0MsOERBQUM0Rjs0Q0FBS2IsV0FBVTs7Z0RBQTZCO2dEQUN4Q3BGLEVBQUFBLG1CQUFBQSxXQUFXNkUsSUFBSSxDQUFDNEIsQ0FBQUEsSUFBS0EsRUFBRTVFLEVBQUUsS0FBS3hCLGdDQUE5QkwsdUNBQUFBLGlCQUFrRDRGLElBQUksS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU1wRWpGLG1DQUNDLDhEQUFDd0U7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzs7Ozs7Ozs7O3VDQUVmLENBQUMvRSxrQ0FDSCw4REFBQzhFO2dDQUFJQyxXQUFVOzBDQUFzQzs7Ozs7dUNBR25EbEYsVUFBVTBCLE1BQU0sS0FBSyxrQkFDdkIsOERBQUN1RDtnQ0FBSUMsV0FBVTswQ0FBc0M7Ozs7O3FEQUlyRCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNzQjtvQ0FBTXRCLFdBQVU7O3NEQUNmLDhEQUFDdUI7NENBQU12QixXQUFVO3NEQUNmLDRFQUFDd0I7O2tFQUNDLDhEQUFDQzt3REFBR3pCLFdBQVU7a0VBQWlGOzs7Ozs7a0VBRy9GLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUFpRjs7Ozs7O2tFQUcvRiw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBaUY7Ozs7OztrRUFHL0YsOERBQUN5Qjt3REFBR3pCLFdBQVU7a0VBQWlGOzs7Ozs7a0VBRy9GLDhEQUFDeUI7d0RBQUd6QixXQUFVO2tFQUFpRjs7Ozs7O2tFQUcvRiw4REFBQ3lCO3dEQUFHekIsV0FBVTtrRUFBaUY7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUtuRyw4REFBQzBCOzRDQUFNMUIsV0FBVTtzREFDZGxGLFVBQVV3RixHQUFHLENBQUMsQ0FBQzlDO2dEQUNkLE1BQU1tRSxjQUFjcEUsZUFBZUM7Z0RBQ25DLE1BQU11QixZQUFZdkIsS0FBS0MsYUFBYSxHQUFJRCxDQUFBQSxLQUFLd0IsVUFBVSxDQUFDQyxhQUFhLElBQUk7Z0RBRXpFLHFCQUNFLDhEQUFDdUM7b0RBQWlCeEIsV0FBVTs7c0VBQzFCLDhEQUFDNEI7NERBQUc1QixXQUFVO3NFQUNaLDRFQUFDRDs7a0ZBQ0MsOERBQUNBO3dFQUFJQyxXQUFVO2tGQUNaeEMsS0FBS3dCLFVBQVUsQ0FBQ3dCLElBQUk7Ozs7OztrRkFFdkIsOERBQUNUO3dFQUFJQyxXQUFVO2tGQUNaeEMsS0FBS3dCLFVBQVUsQ0FBQ3lCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUkzQiw4REFBQ21COzREQUFHNUIsV0FBVTtzRUFDWiw0RUFBQ0Q7Z0VBQUlDLFdBQVU7O29FQUNaeEMsS0FBS0MsYUFBYSxDQUFDb0UsY0FBYztvRUFBRztvRUFBRXJFLEtBQUt3QixVQUFVLENBQUM4QyxJQUFJOzs7Ozs7Ozs7Ozs7c0VBRy9ELDhEQUFDRjs0REFBRzVCLFdBQVU7c0VBQ1osNEVBQUNhO2dFQUFLYixXQUFXLDREQUE4RSxPQUFsQjJCLFlBQVkvRCxLQUFLOzBFQUMzRitELFlBQVloRSxNQUFNOzs7Ozs7Ozs7OztzRUFHdkIsOERBQUNpRTs0REFBRzVCLFdBQVU7O2dFQUNYeEMsS0FBS0UsYUFBYSxDQUFDbUUsY0FBYztnRUFBRztnRUFBSXJFLEtBQUt1RSxhQUFhLENBQUNGLGNBQWM7Ozs7Ozs7c0VBRTVFLDhEQUFDRDs0REFBRzVCLFdBQVU7c0VBQ1hsQyxlQUFlaUI7Ozs7OztzRUFFbEIsOERBQUM2Qzs0REFBRzVCLFdBQVU7OzhFQUNaLDhEQUFDVTtvRUFDQ0MsU0FBUyxJQUFNcEIsa0JBQWtCL0IsS0FBS2YsRUFBRTtvRUFDeEN1RCxXQUFVOzhFQUNYOzs7Ozs7OEVBR0QsOERBQUNVO29FQUNDQyxTQUFTLElBQU1oQixvQkFBb0JuQyxLQUFLZixFQUFFO29FQUMxQ3VELFdBQVU7OEVBQ1g7Ozs7Ozs7Ozs7Ozs7bURBckNJeEMsS0FBS2YsRUFBRTs7Ozs7NENBMkNwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUVYsOERBQUNuQywyRUFBYUE7d0JBQ1owSCxRQUFRckc7d0JBQ1JzRyxTQUFTLElBQU1yRyx1QkFBdUI7d0JBQ3RDb0IsYUFBYS9CO3dCQUNiaUgsY0FBY3RDOzs7Ozs7a0NBR2hCLDhEQUFDckYsOEVBQWdCQTt3QkFDZnlILFFBQVFuRzt3QkFDUm9HLFNBQVMsSUFBTW5HLDBCQUEwQjt3QkFDekMwQixNQUFNckI7d0JBQ05nRyxnQkFBZ0J2Qzs7Ozs7O2tDQUdsQiw4REFBQ3BGLGdGQUFrQkE7d0JBQ2pCd0gsUUFBUWpHO3dCQUNSa0csU0FBUyxJQUFNakcsNEJBQTRCO3dCQUMzQ3dCLE1BQU1yQjt3QkFDTmlHLG1CQUFtQnhDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTS9CO0dBeGJ3QmpGOztRQUNGUiwwREFBT0E7OztLQURMUSIsInNvdXJjZXMiOlsiRDpcXHBhZGFuZ2h1Yl9zdXBhYmFzZVxccGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtXFxzcmNcXGFwcFxcaW52ZW50b3J5XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuaW1wb3J0IFByb3RlY3RlZFJvdXRlIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL1Byb3RlY3RlZFJvdXRlJ1xuaW1wb3J0IERhc2hib2FyZExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0Rhc2hib2FyZExheW91dCdcbmltcG9ydCBBZGRTdG9ja01vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9pbnZlbnRvcnkvQWRkU3RvY2tNb2RhbCdcbmltcG9ydCBVcGRhdGVTdG9ja01vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9pbnZlbnRvcnkvVXBkYXRlU3RvY2tNb2RhbCdcbmltcG9ydCBUcmFuc2ZlclN0b2NrTW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9UcmFuc2ZlclN0b2NrTW9kYWwnXG5pbXBvcnQgQnVsa1VwZGF0ZU1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9pbnZlbnRvcnkvQnVsa1VwZGF0ZU1vZGFsJ1xuaW1wb3J0IHsgaW52ZW50b3J5U2VydmljZSwgd2FyZWhvdXNlU2VydmljZSB9IGZyb20gJ0AvbGliL2RhdGFiYXNlJ1xuXG5pbnRlcmZhY2UgSW52ZW50b3J5SXRlbSB7XG4gIGlkOiBzdHJpbmdcbiAgY3VycmVudF9zdG9jazogbnVtYmVyXG4gIG1pbmltdW1fc3RvY2s6IG51bWJlclxuICBtYXhpbXVtX3N0b2NrOiBudW1iZXJcbiAgcmVvcmRlcl9wb2ludDogbnVtYmVyXG4gIGluZ3JlZGllbnQ6IHtcbiAgICBpZDogc3RyaW5nXG4gICAgbmFtZTogc3RyaW5nXG4gICAgY29kZTogc3RyaW5nXG4gICAgdW5pdDogc3RyaW5nXG4gICAgY29zdF9wZXJfdW5pdDogbnVtYmVyXG4gIH1cbiAgd2FyZWhvdXNlOiB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIGNvZGU6IHN0cmluZ1xuICB9XG59XG5cbmludGVyZmFjZSBXYXJlaG91c2Uge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBjb2RlOiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG59XG5cbnR5cGUgU3RvY2tTdGF0dXMgPSAnY3JpdGljYWwnIHwgJ2xvdycgfCAnZ29vZCdcblxuaW50ZXJmYWNlIFN0b2NrU3RhdHVzSW5mbyB7XG4gIHN0YXR1czogU3RvY2tTdGF0dXNcbiAgY29sb3I6IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnZlbnRvcnlQYWdlKCkge1xuICBjb25zdCB7IHByb2ZpbGUgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbaW52ZW50b3J5LCBzZXRJbnZlbnRvcnldID0gdXNlU3RhdGU8SW52ZW50b3J5SXRlbVtdPihbXSlcbiAgY29uc3QgW3dhcmVob3VzZXMsIHNldFdhcmVob3VzZXNdID0gdXNlU3RhdGU8V2FyZWhvdXNlW10+KFtdKVxuICBjb25zdCBbc2VsZWN0ZWRXYXJlaG91c2UsIHNldFNlbGVjdGVkV2FyZWhvdXNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpXG4gIGNvbnN0IFtsb3dTdG9ja0l0ZW1zLCBzZXRMb3dTdG9ja0l0ZW1zXSA9IHVzZVN0YXRlPEludmVudG9yeUl0ZW1bXT4oW10pXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbaXNJbnZlbnRvcnlMb2FkaW5nLCBzZXRJc0ludmVudG9yeUxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICAvLyBNb2RhbCBzdGF0ZXNcbiAgY29uc3QgW2lzQWRkU3RvY2tNb2RhbE9wZW4sIHNldElzQWRkU3RvY2tNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1VwZGF0ZVN0b2NrTW9kYWxPcGVuLCBzZXRJc1VwZGF0ZVN0b2NrTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNUcmFuc2ZlclN0b2NrTW9kYWxPcGVuLCBzZXRJc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc0J1bGtVcGRhdGVNb2RhbE9wZW4sIHNldElzQnVsa1VwZGF0ZU1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkSXRlbSwgc2V0U2VsZWN0ZWRJdGVtXSA9IHVzZVN0YXRlPEludmVudG9yeUl0ZW0gfCBudWxsPihudWxsKVxuXG4gIGNvbnN0IGxvYWRXYXJlaG91c2VzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgd2FyZWhvdXNlU2VydmljZS5nZXRBbGxXYXJlaG91c2VzKClcbiAgICAgIHNldFdhcmVob3VzZXMoZGF0YSlcbiAgICAgIC8vIEF1dG8tc2VsZWN0IGZpcnN0IHdhcmVob3VzZSBpZiBhdmFpbGFibGUgYW5kIG5vIHdhcmVob3VzZSBpcyBzZWxlY3RlZFxuICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCAmJiAhc2VsZWN0ZWRXYXJlaG91c2UpIHtcbiAgICAgICAgc2V0U2VsZWN0ZWRXYXJlaG91c2UoZGF0YVswXS5pZClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgd2FyZWhvdXNlcydcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSlcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgd2FyZWhvdXNlczonLCBlcnIpXG4gICAgfVxuICB9LCBbc2VsZWN0ZWRXYXJlaG91c2VdKVxuXG4gIGNvbnN0IGxvYWRJbnZlbnRvcnkgPSB1c2VDYWxsYmFjayhhc3luYyAod2FyZWhvdXNlSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghd2FyZWhvdXNlSWQpIHJldHVyblxuICAgIFxuICAgIHRyeSB7XG4gICAgICBzZXRJc0ludmVudG9yeUxvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpIC8vIENsZWFyIHByZXZpb3VzIGVycm9yc1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGludmVudG9yeVNlcnZpY2UuZ2V0V2FyZWhvdXNlSW52ZW50b3J5KHdhcmVob3VzZUlkKVxuICAgICAgc2V0SW52ZW50b3J5KGRhdGEgfHwgW10pIC8vIEVuc3VyZSB3ZSBhbHdheXMgaGF2ZSBhbiBhcnJheVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBpbnZlbnRvcnknXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGludmVudG9yeTonLCBlcnIpXG4gICAgICBzZXRJbnZlbnRvcnkoW10pIC8vIFJlc2V0IGludmVudG9yeSBvbiBlcnJvclxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0ludmVudG9yeUxvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9LCBbXSlcblxuICBjb25zdCBsb2FkTG93U3RvY2tJdGVtcyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGludmVudG9yeVNlcnZpY2UuZ2V0TG93U3RvY2tJdGVtcygpXG4gICAgICBzZXRMb3dTdG9ja0l0ZW1zKGRhdGEgfHwgW10pXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBsb3cgc3RvY2sgaXRlbXM6JywgZXJyKVxuICAgICAgLy8gRG9uJ3Qgc2V0IGVycm9yIHN0YXRlIGZvciB0aGlzIGFzIGl0J3Mgbm90IGNyaXRpY2FsXG4gICAgfVxuICB9LCBbXSlcblxuICAvLyBJbml0aWFsIGxvYWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbml0aWFsaXplRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBsb2FkV2FyZWhvdXNlcygpLFxuICAgICAgICBsb2FkTG93U3RvY2tJdGVtcygpXG4gICAgICBdKVxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgICBcbiAgICBpbml0aWFsaXplRGF0YSgpXG4gIH0sIFtsb2FkV2FyZWhvdXNlcywgbG9hZExvd1N0b2NrSXRlbXNdKVxuXG4gIC8vIExvYWQgaW52ZW50b3J5IHdoZW4gd2FyZWhvdXNlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRXYXJlaG91c2UpIHtcbiAgICAgIGxvYWRJbnZlbnRvcnkoc2VsZWN0ZWRXYXJlaG91c2UpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEludmVudG9yeShbXSlcbiAgICB9XG4gIH0sIFtzZWxlY3RlZFdhcmVob3VzZSwgbG9hZEludmVudG9yeV0pXG5cbiAgY29uc3QgZ2V0U3RvY2tTdGF0dXMgPSAoaXRlbTogSW52ZW50b3J5SXRlbSk6IFN0b2NrU3RhdHVzSW5mbyA9PiB7XG4gICAgaWYgKGl0ZW0uY3VycmVudF9zdG9jayA8PSBpdGVtLm1pbmltdW1fc3RvY2spIHtcbiAgICAgIHJldHVybiB7IHN0YXR1czogJ2NyaXRpY2FsJywgY29sb3I6ICd0ZXh0LXJlZC02MDAgYmctcmVkLTEwMCcgfVxuICAgIH0gZWxzZSBpZiAoaXRlbS5jdXJyZW50X3N0b2NrIDw9IGl0ZW0ucmVvcmRlcl9wb2ludCkge1xuICAgICAgcmV0dXJuIHsgc3RhdHVzOiAnbG93JywgY29sb3I6ICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTEwMCcgfVxuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4geyBzdGF0dXM6ICdnb29kJywgY29sb3I6ICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDAnIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCBmb3JtYXRDdXJyZW5jeSA9IChhbW91bnQ6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnaWQtSUQnLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnSURSJyxcbiAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMCxcbiAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMFxuICAgIH0pLmZvcm1hdChhbW91bnQpXG4gIH1cblxuICBjb25zdCBjYWxjdWxhdGVJbnZlbnRvcnlTdGF0cyA9ICgpID0+IHtcbiAgICBjb25zdCB0b3RhbEl0ZW1zID0gaW52ZW50b3J5Lmxlbmd0aFxuICAgIGNvbnN0IGxvd1N0b2NrQ291bnQgPSBpbnZlbnRvcnkuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jdXJyZW50X3N0b2NrIDw9IGl0ZW0ubWluaW11bV9zdG9jaykubGVuZ3RoXG4gICAgY29uc3QgcmVvcmRlck5lZWRlZENvdW50ID0gaW52ZW50b3J5LmZpbHRlcihpdGVtID0+IGl0ZW0uY3VycmVudF9zdG9jayA8PSBpdGVtLnJlb3JkZXJfcG9pbnQpLmxlbmd0aFxuICAgIGNvbnN0IHRvdGFsVmFsdWUgPSBpbnZlbnRvcnkucmVkdWNlKCh0b3RhbCwgaXRlbSkgPT4ge1xuICAgICAgY29uc3QgaXRlbVZhbHVlID0gaXRlbS5jdXJyZW50X3N0b2NrICogKGl0ZW0uaW5ncmVkaWVudC5jb3N0X3Blcl91bml0IHx8IDApXG4gICAgICByZXR1cm4gdG90YWwgKyBpdGVtVmFsdWVcbiAgICB9LCAwKVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsSXRlbXMsXG4gICAgICBsb3dTdG9ja0NvdW50LFxuICAgICAgcmVvcmRlck5lZWRlZENvdW50LFxuICAgICAgdG90YWxWYWx1ZVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVdhcmVob3VzZUNoYW5nZSA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTFNlbGVjdEVsZW1lbnQ+KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRXYXJlaG91c2UoZXZlbnQudGFyZ2V0LnZhbHVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQWRkU3RvY2sgPSAoKSA9PiB7XG4gICAgc2V0SXNBZGRTdG9ja01vZGFsT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVXBkYXRlU3RvY2sgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBpdGVtID0gaW52ZW50b3J5LmZpbmQoaSA9PiBpLmlkID09PSBpdGVtSWQpXG4gICAgaWYgKGl0ZW0pIHtcbiAgICAgIHNldFNlbGVjdGVkSXRlbShpdGVtKVxuICAgICAgc2V0SXNVcGRhdGVTdG9ja01vZGFsT3Blbih0cnVlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRyYW5zZmVyU3RvY2sgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBpdGVtID0gaW52ZW50b3J5LmZpbmQoaSA9PiBpLmlkID09PSBpdGVtSWQpXG4gICAgaWYgKGl0ZW0pIHtcbiAgICAgIHNldFNlbGVjdGVkSXRlbShpdGVtKVxuICAgICAgc2V0SXNUcmFuc2ZlclN0b2NrTW9kYWxPcGVuKHRydWUpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU3RvY2tVcGRhdGVkID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZFdhcmVob3VzZSkge1xuICAgICAgbG9hZEludmVudG9yeShzZWxlY3RlZFdhcmVob3VzZSlcbiAgICB9XG4gICAgbG9hZExvd1N0b2NrSXRlbXMoKVxuICB9XG5cbiAgY29uc3Qgc3RhdHMgPSBjYWxjdWxhdGVJbnZlbnRvcnlTdGF0cygpXG5cbiAgLy8gU2hvdyBsb2FkaW5nIHNwaW5uZXIgZHVyaW5nIGluaXRpYWwgbG9hZFxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxQcm90ZWN0ZWRSb3V0ZSBhbGxvd2VkUm9sZXM9e1snYWRtaW4nLCAnbWFuYWdlcicsICdzdGFmZiddfT5cbiAgICAgICAgPERhc2hib2FyZExheW91dD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMFwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgICAgIDwvUHJvdGVjdGVkUm91dGU+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8UHJvdGVjdGVkUm91dGUgYWxsb3dlZFJvbGVzPXtbJ2FkbWluJywgJ21hbmFnZXInLCAnc3RhZmYnXX0+XG4gICAgICA8RGFzaGJvYXJkTGF5b3V0PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOmp1c3RpZnktYmV0d2VlbiBzbTppdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkludmVudG9yeSBNYW5hZ2VtZW50PC9oMT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkV2FyZWhvdXNlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVXYXJlaG91c2VDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3dhcmVob3VzZXMubGVuZ3RoID09PSAwfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjdCBXYXJlaG91c2U8L29wdGlvbj5cbiAgICAgICAgICAgICAgICB7d2FyZWhvdXNlcy5tYXAoKHdhcmVob3VzZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e3dhcmVob3VzZS5pZH0gdmFsdWU9e3dhcmVob3VzZS5pZH0+XG4gICAgICAgICAgICAgICAgICAgIHt3YXJlaG91c2UubmFtZX0gKHt3YXJlaG91c2UuY29kZX0pXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkU3RvY2t9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFdhcmVob3VzZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1pbmRpZ28tNjAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbWQgaG92ZXI6YmctaW5kaWdvLTcwMCBkaXNhYmxlZDpiZy1ncmF5LTQwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQWRkIFN0b2NrXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRXJyb3IgQWxlcnQgKi99XG4gICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTEwMCBib3JkZXIgYm9yZGVyLXJlZC00MDAgdGV4dC1yZWQtNzAwIHB4LTQgcHktMyByb3VuZGVkIHJlbGF0aXZlXCIgcm9sZT1cImFsZXJ0XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHNtOmlubGluZVwiPntlcnJvcn08L3NwYW4+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRFcnJvcihudWxsKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBib3R0b20tMCByaWdodC0wIHB4LTQgcHktM1wiXG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsb3NlIGVycm9yIG1lc3NhZ2VcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+JnRpbWVzOzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIExvdyBTdG9jayBBbGVydCAqL31cbiAgICAgICAgICB7bG93U3RvY2tJdGVtcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgdGV4dC14bCBtci0zXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCI+4pqg77iPPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtcmVkLTgwMFwiPkxvdyBTdG9jayBBbGVydDwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2xvd1N0b2NrSXRlbXMubGVuZ3RofSBpdGVte2xvd1N0b2NrSXRlbXMubGVuZ3RoICE9PSAxID8gJ3MnIDogJyd9IFxuICAgICAgICAgICAgICAgICAgICB7bG93U3RvY2tJdGVtcy5sZW5ndGggPT09IDEgPyAnIGlzJyA6ICcgYXJlJ30gcnVubmluZyBsb3cgYWNyb3NzIGFsbCB3YXJlaG91c2VzXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBJbnZlbnRvcnkgU3RhdHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC01IHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIG92ZXJmbG93LWhpZGRlbiBzaGFkb3cgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCI+8J+TpjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTUgdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVG90YWwgSXRlbXNcbiAgICAgICAgICAgICAgICAgICAgICA8L2R0PlxuICAgICAgICAgICAgICAgICAgICAgIDxkZCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0cy50b3RhbEl0ZW1zfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBvdmVyZmxvdy1oaWRkZW4gc2hhZG93IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPuKaoO+4jzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTUgdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgTG93IFN0b2NrIEl0ZW1zXG4gICAgICAgICAgICAgICAgICAgICAgPC9kdD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXJlZC02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0cy5sb3dTdG9ja0NvdW50fVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBvdmVyZmxvdy1oaWRkZW4gc2hhZG93IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPvCfkrA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC01IHctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRvdGFsIFZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgPC9kdD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koc3RhdHMudG90YWxWYWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kZD5cbiAgICAgICAgICAgICAgICAgICAgPC9kbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIG92ZXJmbG93LWhpZGRlbiBzaGFkb3cgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCI+8J+UhDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTUgdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUmVvcmRlciBOZWVkZWRcbiAgICAgICAgICAgICAgICAgICAgICA8L2R0PlxuICAgICAgICAgICAgICAgICAgICAgIDxkZCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3N0YXRzLnJlb3JkZXJOZWVkZWRDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICA8L2RkPlxuICAgICAgICAgICAgICAgICAgICA8L2RsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSW52ZW50b3J5IFRhYmxlICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93IG92ZXJmbG93LWhpZGRlbiBzbTpyb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktNSBzbTpweC02XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGxlYWRpbmctNiBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgQ3VycmVudCBJbnZlbnRvcnlcbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRXYXJlaG91c2UgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG1sLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgLSB7d2FyZWhvdXNlcy5maW5kKHcgPT4gdy5pZCA9PT0gc2VsZWN0ZWRXYXJlaG91c2UpPy5uYW1lIHx8ICdVbmtub3duIFdhcmVob3VzZSd9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7aXNJbnZlbnRvcnlMb2FkaW5nID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMzJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1pbmRpZ28tNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6ICFzZWxlY3RlZFdhcmVob3VzZSA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTggdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIFBsZWFzZSBzZWxlY3QgYSB3YXJlaG91c2UgdG8gdmlldyBpbnZlbnRvcnlcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogaW52ZW50b3J5Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTggdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIE5vIGludmVudG9yeSBpdGVtcyBmb3VuZCBmb3IgdGhpcyB3YXJlaG91c2VcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEluZ3JlZGllbnRcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEN1cnJlbnQgU3RvY2tcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgTWluL01heFxuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFjdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2ludmVudG9yeS5tYXAoKGl0ZW0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdG9ja1N0YXR1cyA9IGdldFN0b2NrU3RhdHVzKGl0ZW0pXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbVZhbHVlID0gaXRlbS5jdXJyZW50X3N0b2NrICogKGl0ZW0uaW5ncmVkaWVudC5jb3N0X3Blcl91bml0IHx8IDApXG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e2l0ZW0uaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pbmdyZWRpZW50Lm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmluZ3JlZGllbnQuY29kZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmN1cnJlbnRfc3RvY2sudG9Mb2NhbGVTdHJpbmcoKX0ge2l0ZW0uaW5ncmVkaWVudC51bml0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtzdG9ja1N0YXR1cy5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdG9ja1N0YXR1cy5zdGF0dXN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLm1pbmltdW1fc3RvY2sudG9Mb2NhbGVTdHJpbmcoKX0gLyB7aXRlbS5tYXhpbXVtX3N0b2NrLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KGl0ZW1WYWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVVcGRhdGVTdG9jayhpdGVtLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTYwMCBob3Zlcjp0ZXh0LWluZGlnby05MDAgbXItMyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVXBkYXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRyYW5zZmVyU3RvY2soaXRlbS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdyZWVuLTkwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVHJhbnNmZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE1vZGFscyAqL31cbiAgICAgICAgICA8QWRkU3RvY2tNb2RhbFxuICAgICAgICAgICAgaXNPcGVuPXtpc0FkZFN0b2NrTW9kYWxPcGVufVxuICAgICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNBZGRTdG9ja01vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgICAgICB3YXJlaG91c2VJZD17c2VsZWN0ZWRXYXJlaG91c2V9XG4gICAgICAgICAgICBvblN0b2NrQWRkZWQ9e2hhbmRsZVN0b2NrVXBkYXRlZH1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFVwZGF0ZVN0b2NrTW9kYWxcbiAgICAgICAgICAgIGlzT3Blbj17aXNVcGRhdGVTdG9ja01vZGFsT3Blbn1cbiAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzVXBkYXRlU3RvY2tNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgaXRlbT17c2VsZWN0ZWRJdGVtfVxuICAgICAgICAgICAgb25TdG9ja1VwZGF0ZWQ9e2hhbmRsZVN0b2NrVXBkYXRlZH1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFRyYW5zZmVyU3RvY2tNb2RhbFxuICAgICAgICAgICAgaXNPcGVuPXtpc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW59XG4gICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgaXRlbT17c2VsZWN0ZWRJdGVtfVxuICAgICAgICAgICAgb25UcmFuc2ZlckNyZWF0ZWQ9e2hhbmRsZVN0b2NrVXBkYXRlZH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICAgIDwvUHJvdGVjdGVkUm91dGU+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZUF1dGgiLCJQcm90ZWN0ZWRSb3V0ZSIsIkRhc2hib2FyZExheW91dCIsIkFkZFN0b2NrTW9kYWwiLCJVcGRhdGVTdG9ja01vZGFsIiwiVHJhbnNmZXJTdG9ja01vZGFsIiwiaW52ZW50b3J5U2VydmljZSIsIndhcmVob3VzZVNlcnZpY2UiLCJJbnZlbnRvcnlQYWdlIiwid2FyZWhvdXNlcyIsInByb2ZpbGUiLCJpbnZlbnRvcnkiLCJzZXRJbnZlbnRvcnkiLCJzZXRXYXJlaG91c2VzIiwic2VsZWN0ZWRXYXJlaG91c2UiLCJzZXRTZWxlY3RlZFdhcmVob3VzZSIsImxvd1N0b2NrSXRlbXMiLCJzZXRMb3dTdG9ja0l0ZW1zIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNJbnZlbnRvcnlMb2FkaW5nIiwic2V0SXNJbnZlbnRvcnlMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImlzQWRkU3RvY2tNb2RhbE9wZW4iLCJzZXRJc0FkZFN0b2NrTW9kYWxPcGVuIiwiaXNVcGRhdGVTdG9ja01vZGFsT3BlbiIsInNldElzVXBkYXRlU3RvY2tNb2RhbE9wZW4iLCJpc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4iLCJzZXRJc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4iLCJpc0J1bGtVcGRhdGVNb2RhbE9wZW4iLCJzZXRJc0J1bGtVcGRhdGVNb2RhbE9wZW4iLCJzZWxlY3RlZEl0ZW0iLCJzZXRTZWxlY3RlZEl0ZW0iLCJsb2FkV2FyZWhvdXNlcyIsImRhdGEiLCJnZXRBbGxXYXJlaG91c2VzIiwibGVuZ3RoIiwiaWQiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsIm1lc3NhZ2UiLCJjb25zb2xlIiwibG9hZEludmVudG9yeSIsIndhcmVob3VzZUlkIiwiZ2V0V2FyZWhvdXNlSW52ZW50b3J5IiwibG9hZExvd1N0b2NrSXRlbXMiLCJnZXRMb3dTdG9ja0l0ZW1zIiwiaW5pdGlhbGl6ZURhdGEiLCJQcm9taXNlIiwiYWxsIiwiZ2V0U3RvY2tTdGF0dXMiLCJpdGVtIiwiY3VycmVudF9zdG9jayIsIm1pbmltdW1fc3RvY2siLCJzdGF0dXMiLCJjb2xvciIsInJlb3JkZXJfcG9pbnQiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiZm9ybWF0IiwiY2FsY3VsYXRlSW52ZW50b3J5U3RhdHMiLCJ0b3RhbEl0ZW1zIiwibG93U3RvY2tDb3VudCIsImZpbHRlciIsInJlb3JkZXJOZWVkZWRDb3VudCIsInRvdGFsVmFsdWUiLCJyZWR1Y2UiLCJ0b3RhbCIsIml0ZW1WYWx1ZSIsImluZ3JlZGllbnQiLCJjb3N0X3Blcl91bml0IiwiaGFuZGxlV2FyZWhvdXNlQ2hhbmdlIiwiZXZlbnQiLCJ0YXJnZXQiLCJ2YWx1ZSIsImhhbmRsZUFkZFN0b2NrIiwiaGFuZGxlVXBkYXRlU3RvY2siLCJpdGVtSWQiLCJmaW5kIiwiaSIsImhhbmRsZVRyYW5zZmVyU3RvY2siLCJoYW5kbGVTdG9ja1VwZGF0ZWQiLCJzdGF0cyIsImFsbG93ZWRSb2xlcyIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJkaXNhYmxlZCIsIm9wdGlvbiIsIm1hcCIsIndhcmVob3VzZSIsIm5hbWUiLCJjb2RlIiwiYnV0dG9uIiwib25DbGljayIsInJvbGUiLCJzcGFuIiwiYXJpYS1sYWJlbCIsImFyaWEtaGlkZGVuIiwiaDMiLCJwIiwiZGwiLCJkdCIsImRkIiwidyIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJzdG9ja1N0YXR1cyIsInRkIiwidG9Mb2NhbGVTdHJpbmciLCJ1bml0IiwibWF4aW11bV9zdG9jayIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblN0b2NrQWRkZWQiLCJvblN0b2NrVXBkYXRlZCIsIm9uVHJhbnNmZXJDcmVhdGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});