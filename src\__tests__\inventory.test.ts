import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { inventoryService } from '../lib/database'
import { supabase } from '../lib/supabase'

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({
            limit: vi.fn()
          }))
        })),
        insert: vi.fn(),
        update: vi.fn(() => ({
          eq: vi.fn()
        })),
        delete: vi.fn(() => ({
          eq: vi.fn()
        })),
        filter: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn()
          }))
        }))
      }))
    })),
    rpc: vi.fn()
  }
}))

// Mock audit logging
vi.mock('../lib/auditLog', () => ({
  logStockChange: vi.fn(),
  logTransferOperation: vi.fn(),
  logBulkOperation: vi.fn()
}))

describe('Inventory Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('updateStock', () => {
    it('should update stock successfully with valid input', async () => {
      const mockInventory = {
        id: 'inv-1',
        current_stock: 100,
        minimum_stock: 10,
        maximum_stock: 500,
        warehouse_id: 'wh-1',
        ingredient_id: 'ing-1',
        ingredient: {
          name: 'Test Ingredient',
          unit: 'kg'
        },
        warehouse: {
          name: 'Test Warehouse',
          code: 'TW01'
        }
      }

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockInventory,
            error: null
          })
        })
      })

      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null
        })
      })

      const mockInsert = vi.fn().mockResolvedValue({
        error: null
      })

      vi.mocked(supabase.from).mockImplementation((table: string) => {
        if (table === 'inventory') {
          return {
            select: mockSelect,
            update: mockUpdate
          } as any
        }
        if (table === 'stock_movements') {
          return {
            insert: mockInsert
          } as any
        }
        return {} as any
      })

      const result = await inventoryService.updateStock(
        'inv-1',
        150,
        'adjustment',
        'Test update',
        'user-1'
      )

      expect(result.success).toBe(true)
      expect(result.previousStock).toBe(100)
      expect(result.newStock).toBe(150)
      expect(result.quantity).toBe(50)
      expect(result.movementType).toBe('adjustment')
    })

    it('should throw error for negative stock', async () => {
      await expect(
        inventoryService.updateStock('inv-1', -10, 'adjustment')
      ).rejects.toThrow('Invalid input: inventory ID is required and stock cannot be negative')
    })

    it('should throw error for invalid movement type', async () => {
      await expect(
        inventoryService.updateStock('inv-1', 100, 'invalid_type')
      ).rejects.toThrow('Invalid movement type')
    })

    it('should throw error when exceeding maximum stock', async () => {
      const mockInventory = {
        id: 'inv-1',
        current_stock: 100,
        minimum_stock: 10,
        maximum_stock: 200,
        warehouse_id: 'wh-1',
        ingredient_id: 'ing-1',
        ingredient: {
          name: 'Test Ingredient',
          unit: 'kg'
        },
        warehouse: {
          name: 'Test Warehouse',
          code: 'TW01'
        }
      }

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockInventory,
            error: null
          })
        })
      })

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect
      } as any)

      await expect(
        inventoryService.updateStock('inv-1', 300, 'adjustment')
      ).rejects.toThrow('Stock cannot exceed maximum limit of 200 kg')
    })
  })

  describe('bulkUpdateStock', () => {
    it('should handle bulk updates successfully', async () => {
      const mockInventory1 = {
        id: 'inv-1',
        current_stock: 100,
        minimum_stock: 10,
        maximum_stock: 500,
        warehouse_id: 'wh-1',
        ingredient_id: 'ing-1',
        ingredient: {
          name: 'Test Ingredient 1',
          unit: 'kg'
        },
        warehouse: {
          name: 'Test Warehouse',
          code: 'TW01'
        }
      }

      const mockInventory2 = {
        id: 'inv-2',
        current_stock: 50,
        minimum_stock: 5,
        maximum_stock: 300,
        warehouse_id: 'wh-1',
        ingredient_id: 'ing-2',
        ingredient: {
          name: 'Test Ingredient 2',
          unit: 'kg'
        },
        warehouse: {
          name: 'Test Warehouse',
          code: 'TW01'
        }
      }

      const mockSelect = vi.fn()
        .mockReturnValueOnce({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockInventory1,
              error: null
            })
          })
        })
        .mockReturnValueOnce({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockInventory2,
              error: null
            })
          })
        })

      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null
        })
      })

      const mockInsert = vi.fn().mockResolvedValue({
        error: null
      })

      vi.mocked(supabase.from).mockImplementation((table: string) => {
        if (table === 'inventory') {
          return {
            select: mockSelect,
            update: mockUpdate
          } as any
        }
        if (table === 'stock_movements') {
          return {
            insert: mockInsert
          } as any
        }
        return {} as any
      })

      const updates = [
        {
          inventoryId: 'inv-1',
          newStock: 150,
          movementType: 'adjustment',
          notes: 'Bulk update 1'
        },
        {
          inventoryId: 'inv-2',
          newStock: 75,
          movementType: 'adjustment',
          notes: 'Bulk update 2'
        }
      ]

      const result = await inventoryService.bulkUpdateStock(updates, 'user-1')

      expect(result.success).toBe(true)
      expect(result.totalUpdates).toBe(2)
      expect(result.successCount).toBe(2)
      expect(result.failureCount).toBe(0)
    })

    it('should handle partial failures in bulk updates', async () => {
      const mockInventory1 = {
        id: 'inv-1',
        current_stock: 100,
        minimum_stock: 10,
        maximum_stock: 500,
        warehouse_id: 'wh-1',
        ingredient_id: 'ing-1',
        ingredient: {
          name: 'Test Ingredient 1',
          unit: 'kg'
        },
        warehouse: {
          name: 'Test Warehouse',
          code: 'TW01'
        }
      }

      const mockSelect = vi.fn()
        .mockReturnValueOnce({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockInventory1,
              error: null
            })
          })
        })
        .mockReturnValueOnce({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Inventory not found' }
            })
          })
        })

      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null
        })
      })

      const mockInsert = vi.fn().mockResolvedValue({
        error: null
      })

      vi.mocked(supabase.from).mockImplementation((table: string) => {
        if (table === 'inventory') {
          return {
            select: mockSelect,
            update: mockUpdate
          } as any
        }
        if (table === 'stock_movements') {
          return {
            insert: mockInsert
          } as any
        }
        return {} as any
      })

      const updates = [
        {
          inventoryId: 'inv-1',
          newStock: 150,
          movementType: 'adjustment',
          notes: 'Bulk update 1'
        },
        {
          inventoryId: 'inv-invalid',
          newStock: 75,
          movementType: 'adjustment',
          notes: 'Bulk update 2'
        }
      ]

      const result = await inventoryService.bulkUpdateStock(updates, 'user-1')

      expect(result.success).toBe(false)
      expect(result.totalUpdates).toBe(2)
      expect(result.successCount).toBe(1)
      expect(result.failureCount).toBe(1)
    })

    it('should throw error for empty updates array', async () => {
      await expect(
        inventoryService.bulkUpdateStock([], 'user-1')
      ).rejects.toThrow('No updates provided')
    })
  })

  describe('getLowStockItems', () => {
    it('should return low stock items using optimized query', async () => {
      const mockLowStockItems = [
        {
          id: 'inv-1',
          current_stock: 5,
          minimum_stock: 10,
          ingredient: { name: 'Low Stock Item 1' },
          warehouse: { name: 'Warehouse 1' }
        },
        {
          id: 'inv-2',
          current_stock: 2,
          minimum_stock: 15,
          ingredient: { name: 'Low Stock Item 2' },
          warehouse: { name: 'Warehouse 2' }
        }
      ]

      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockLowStockItems,
        error: null
      })

      const result = await inventoryService.getLowStockItems('branch-1')

      expect(result).toEqual(mockLowStockItems)
      expect(supabase.rpc).toHaveBeenCalledWith('get_low_stock_items_optimized', {
        branch_id_param: 'branch-1'
      })
    })

    it('should fallback to client-side filtering when RPC fails', async () => {
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: null,
        error: { message: 'RPC function not found' }
      })

      const mockInventoryData = [
        {
          id: 'inv-1',
          current_stock: 5,
          minimum_stock: 10,
          ingredient: { name: 'Low Stock Item 1' },
          warehouse: { name: 'Warehouse 1' }
        }
      ]

      const mockSelect = vi.fn().mockReturnValue({
        filter: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockReturnValue({
              limit: vi.fn().mockResolvedValue({
                data: mockInventoryData,
                error: null
              })
            })
          })
        })
      })

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect
      } as any)

      const result = await inventoryService.getLowStockItems('branch-1')

      expect(result).toEqual(mockInventoryData)
    })
  })
})
